version = 2.6.4

maxColumn = 120
align = most
continuationIndent.defnSite = 2
assumeStandardLibraryStripMargin = true
docstrings = ScalaDoc
lineEndings = preserve
includeCurlyBraceInSelectChains = false
danglingParentheses = true

align.tokens.add = [
  {
    code = ":"
  }
]

newlines.alwaysBeforeCurlyBraceLambdaParams = false
newlines.alwaysBeforeMultilineDef = false
newlines.implicitParamListModifierForce = [before]

verticalMultiline.atDefnSite = true

optIn.annotationNewlines = true

rewrite.rules = [SortImports, PreferCurlyFors, AvoidInfix]
