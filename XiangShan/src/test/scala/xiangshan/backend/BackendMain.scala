package xiangshan.backend

import freechips.rocketchip.diplomacy.LazyModule
import top.{Arg<PERSON><PERSON><PERSON>, BaseConfig, Generator}
import xiangshan.backend.regfile.IntPregParams
import xiangshan.{XSCoreParameters, XSCoreParamsKey, XSTileKey}

object BackendMain extends App {
  val (config, firrtlOpts, firtoolOpts) = ArgParser.parse(
    args :+ "--disable-always-basic-diff" :+ "--fpga-platform" :+ "--target" :+ "verilog")

  val defaultConfig = config.alterPartial({
    // Get XSCoreParams and pass it to the "small module"
    case XSCoreParamsKey => config(XSTileKey).head
  })

  val backendParams = defaultConfig(XSCoreParamsKey).backendParams
  val backend = LazyModule(new Backend(backendParams)(defaultConfig))

  Generator.execute(
    firrtlOpts :+ "--full-stacktrace" :+ "--target-dir" :+ "backend",
    backend.module,
    firtoolOpts
  )
  println("done")
}

