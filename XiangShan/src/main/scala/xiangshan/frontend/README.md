# Acknowledgement

The implementation of Frontend is inspired by following papers:

[1] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. "[Fetching instruction streams.](https://doi.org/10.1109/MICRO.2002.1176264)" 35th Annual IEEE/ACM International Symposium on Microarchitecture (MICRO). 2002.

[2] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "[Rebasing instruction prefetching: An industry perspective.](https://doi.org/10.1109/LCA.2020.3035068)" IEEE Computer Architecture Letters 19.2: 147-150. 2020.

[3] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "[Re-establishing fetch-directed instruction prefetching: An industry perspective.](https://doi.org/10.1109/ISPASS51385.2021.00034)" 2021 IEEE International Symposium on Performance Analysis of Systems and Software (ISPASS). 2021.