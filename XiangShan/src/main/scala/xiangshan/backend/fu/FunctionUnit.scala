/***************************************************************************************
* Copyright (c) 2020-2021 Institute of Computing Technology, Chinese Academy of Sciences
* Copyright (c) 2020-2021 Peng Cheng Laboratory
*
* XiangShan is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
*          http://license.coscl.org.cn/MulanPSL2
*
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
*
* See the Mulan PSL v2 for more details.
***************************************************************************************/

package xiangshan.backend.fu

import org.chipsalliance.cde.config.Parameters
import chisel3._
import chisel3.util._
import utility.XSPerfAccumulate
import xiangshan._
import xiangshan.backend.fu.fpu._

trait HasFuLatency {
  val latencyVal: Option[Int]
  val extraLatencyVal: Option[Int]
  val uncertainLatencyVal: Option[Int]
  val uncertainEnable: Option[Int]
}

case class CertainLatency(value: Int, extraValue: Int = 0) extends HasFuLatency {
  override val latencyVal: Option[Int] = Some(value + extraValue)
  override val extraLatencyVal: Option[Int] = Some(extraValue)
  override val uncertainLatencyVal: Option[Int] = None
  override val uncertainEnable: Option[Int] = None
}

case class UncertainLatency(value: Option[Int]) extends HasFuLatency {
  override val latencyVal: Option[Int] = None
  override val extraLatencyVal: Option[Int] = None
  override val uncertainLatencyVal: Option[Int] = value
  override val uncertainEnable: Option[Int] = Some(0) // for gate uncertain fu
}

object UncertainLatency {
  def apply(): UncertainLatency = UncertainLatency(None)
  def apply(value: Int): UncertainLatency = UncertainLatency(Some(value))
}

class FuOutput(val len: Int)(implicit p: Parameters) extends XSBundle {
  val data = UInt(len.W)
  val uop = new MicroOp
}

class FunctionUnitInput(val len: Int)(implicit p: Parameters) extends XSBundle {
  val src = Vec(3, UInt(len.W))
  val uop = new MicroOp
}

class FunctionUnitIO(val len: Int)(implicit p: Parameters) extends XSBundle {
  val in = Flipped(DecoupledIO(new FunctionUnitInput(len)))

  val out = DecoupledIO(new FuOutput(len))

  val redirectIn = Flipped(ValidIO(new Redirect))
}

abstract class FunctionUnit(len: Int = 64)(implicit p: Parameters) extends XSModule {

  val io = IO(new FunctionUnitIO(len))

  XSPerfAccumulate("in_valid", io.in.valid)
  XSPerfAccumulate("in_fire", io.in.fire)
  XSPerfAccumulate("out_valid", io.out.valid)
  XSPerfAccumulate("out_fire", io.out.fire)

}
