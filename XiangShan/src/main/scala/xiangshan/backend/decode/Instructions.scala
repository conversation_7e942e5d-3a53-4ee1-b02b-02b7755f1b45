package xiangshan.backend.decode

import chisel3.util._

object Zvbb {
  def VANDN_VV = BitPat("b000001???????????000?????1010111")
  def VANDN_VX = BitPat("b000001???????????100?????1010111")
  def VBREV_V  = BitPat("b010010??????01010010?????1010111")
  def VBREV8_V = BitPat("b010010??????01000010?????1010111")
  def VREV8_V  = BitPat("b010010??????01001010?????1010111")
  def VCLZ_V   = BitPat("b010010??????01100010?????1010111")
  def VCTZ_V   = BitPat("b010010??????01101010?????1010111")
  def VCPOP_V  = BitPat("b010010??????01110010?????1010111")
  def VROL_VV  = BitPat("b010101???????????000?????1010111")
  def VROL_VX  = BitPat("b010101???????????100?????1010111")
  def VROR_VI  = BitPat("b01010????????????011?????1010111")
  def VROR_VV  = BitPat("b010100???????????000?????1010111")
  def VROR_VX  = BitPat("b010100???????????100?????1010111")
  def VWSLL_VI = BitPat("b110101???????????011?????1010111")
  def VWSLL_VV = BitPat("b110101???????????000?????1010111")
  def VWSLL_VX = BitPat("b110101???????????100?????1010111")
}

object Zimop {
  def MOP_R  = BitPat("b1?00??0111???????100?????1110011")
  def MOP_RR = BitPat("b1?00??1??????????100?????1110011")
}
