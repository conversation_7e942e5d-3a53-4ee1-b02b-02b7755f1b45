package xiangshan.backend.decode.isa

import chisel3.util._

object PseudoInstructions {
  // no write csr
  //                                    csr     | rs1 | funct3 |  rd | opcode
  def CSRRS_RO           = BitPat("b????????????_00000____?10___?????_1110011")
  def CSRRC_RO           = BitPat("b????????????_00000____?11___?????_1110011")

  // no read csr
  def CSRRW_WO           = BitPat("b????????????_xxxxx____?01___?????_1110011")
}
