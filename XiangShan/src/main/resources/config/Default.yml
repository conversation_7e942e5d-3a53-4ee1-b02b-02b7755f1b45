PmemRanges:
  - { lower: 0x80000000, upper: 0x80000000000 }

PMAConfigs:
  - { base_addr: 0x0, range: 0x1000000000000, a: 3 }
  - { base_addr: 0x80000000000, c: true, atomic: true, a: 1, x: true, w: true, r: true }
  - { base_addr: 0x80000000, a: 1, w: true, r: true }
  - { base_addr: 0x3A000000, a: 1 }
  - { base_addr: 0x39002000, a: 1, w: true, r: true }
  - { base_addr: 0x39000000, a: 1, w: true, r: true }
  - { base_addr: 0x38022000, a: 1, w: true, r: true }
  - { base_addr: 0x38021000, a: 1, x: true, w: true, r: true }
  - { base_addr: 0x30010000, a: 1, w: true, r: true }
  - { base_addr: 0x20000000, a: 1, x: true, w: true, r: true }
  - { base_addr: 0x10000000, a: 1, w: true, r: true }
  - { base_addr: 0 }

EnableCHIAsyncBridge: true

L2CacheConfig: { size: 1 MB, ways: 8, inclusive: true, banks: 4, tp: true }

L3CacheConfig: { size: 16 MB, ways: 16, inclusive: false, banks: 4 }

DebugModuleBaseAddr: 0x38020000

SeperateDM: false

SeperateTLBus: false

SeperateTLBusRanges:
  - { base: 0x38020000, mask: 0xFFF } # Default Debug Module Address
