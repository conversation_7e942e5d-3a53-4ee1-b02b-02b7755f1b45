# This file is partially adapted from github/alejandrohdezma/sbt-scalafmt-defaults at
# https://github.com/alejandrohdezma/sbt-scalafmt-defaults/blob/main/.scalafmt.conf
# Some changes have been made against the original file based on the scalafmt doc.

version = "3.7.17"

runner.dialect = scala213
preset = default

maxColumn = 120
indent.defnSite = 2
indent.extendSite = 2

newlines.avoidForSimpleOverflow=[slc, tooLong]

assumeStandardLibraryStripMargin = true
align.stripMargin = true

align.preset = some
align.multiline = false

binPack.literalsIncludeSimpleExpr = true
binPack.literalsExclude = [ "Term.Name" ]

docstrings.style = keep

rewrite.rules = [
  AvoidInfix,
  RedundantParens,
  SortModifiers,
  PreferCurlyFors,
  Imports
]
rewrite.redundantBraces.methodBodies = false
rewrite.redundantBraces.stringInterpolation = false
rewrite.imports.sort = scalastyle
rewrite.trailingCommas.style = multiple

rewriteTokens = {
  "⇒": "=>"
  "→": "->"
  "←": "<-"
}

includeCurlyBraceInSelectChains = false

project.includePaths = ["glob:**.scala", "glob:**.sbt", "glob:**.sc", "glob:**.md"]
project.excludePaths = ["glob:**metals.sbt"]
