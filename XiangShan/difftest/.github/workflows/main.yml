name: CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  workflow_dispatch:

jobs:
  test-difftest-main:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4

      - name: Mill Installation
        run: |
          sudo curl -L https://github.com/com-lihaoyi/mill/releases/download/0.11.1/0.11.1 > /usr/local/bin/mill
          chmod +x /usr/local/bin/mill

      - name: Compile
        run: |
          mill -i design[chisel3].compile
          mill -i design[chisel].compile

      - name: Generate Verilog
        run: |
          make MFC=0
          make MFC=1

  test-difftest-nutshell:
    runs-on: ubuntu-22.04

    needs: test-difftest-main

    steps:
      - uses: actions/checkout@v4

      - name: Enable -Werror for EMU Build
        run: |
          echo "CXX_NO_WARNING=1" >> $GITHUB_ENV

      - name: Prepare environment
        run: |
            cd $GITHUB_WORKSPACE/..
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/install-verilator.sh
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/setup-tools.sh
            sudo bash setup-tools.sh

      - name: Prepare NutShell
        run: |
            cd $GITHUB_WORKSPACE/..
            git clone -b dev-difftest --single-branch https://github.com/OSCPU/NutShell.git
            cd NutShell && rm -rf difftest && cp -r $GITHUB_WORKSPACE .
            echo "NOOP_HOME=$(pwd)" >> $GITHUB_ENV
            echo "WORKLOAD=$(pwd)/ready-to-run/microbench.bin" >> $GITHUB_ENV
            echo "REF_SO=$(pwd)/ready-to-run/riscv64-nemu-interpreter-so" >> $GITHUB_ENV

      - name: Simulation without DiffTest
        run: |
            cd $NOOP_HOME
            make emu NO_DIFF=1 EMU_TRACE=1 -j2
            ./build/emu -i $WORKLOAD --dump-wave -C 10000
            ./build/emu -i $WORKLOAD --dump-wave-full -C 10000
            make clean

      - name: Simulation with No Diff
        run: |
            cd $NOOP_HOME
            make emu -j2
            ./build/emu -i $WORKLOAD --no-diff

      - name: Basic Difftest
        run: |
            cd $NOOP_HOME
            ./build/emu -i $WORKLOAD --diff $REF_SO

      - name: Basic Difftest without Image
        run: |
            cd $NOOP_HOME
            ./build/emu --no-diff -C 10000
            make clean

      - name: Basic Difftest with PGO build
        run: |
            cd $NOOP_HOME
            make emu PGO_WORKLOAD=$WORKLOAD LLVM_PROFDATA=llvm-profdata PGO_EMU_ARGS="--diff $REF_SO 2>/dev/null" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Snapshot
        run: |
            cd $NOOP_HOME
            make emu EMU_SNAPSHOT=1 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Trace
        run: |
            cd $NOOP_HOME
            make emu EMU_TRACE=1 -j2
            ./build/emu -b 10 -e 12 -i $WORKLOAD --dump-wave --diff $REF_SO
            ./build/emu -e 12 -i $WORKLOAD --dump-wave-full --diff $REF_SO
            make clean

      - name: Difftest with two threads
        run: |
            cd $NOOP_HOME
            make emu EMU_THREADS=2 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Verilator Coverage
        run: |
            cd $NOOP_HOME
            rm -rf *.coverage
            make emu EMU_COVERAGE=1 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO -C 10000 --dump-coverage
            make -C difftest coverage
            ls -lh *.coverage
            make clean

      - name: Difftest with DiffTrace
        run: |
            cd $NOOP_HOME
            make emu -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --dump-difftrace microbench
            ./build/emu -i $WORKLOAD --diff $REF_SO --load-difftrace microbench
            make clean

      - name: Difftest with Footprints
        run: |
            cd $NOOP_HOME
            make emu -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --dump-footprints microbench.bin
            ./build/emu -i microbench.bin --diff $REF_SO --as-footprints
            make clean

      - name: Difftest with Batch
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config B" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Global DPI-C Enable
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config E" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Squash and Global Enable
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config ES" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Squash Batch and Global Enable
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config ESB" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with Squash Batch GlobalEnable and Query
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config ESB" DIFFTEST_QUERY=1 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO
            make clean

      - name: Difftest with JsonProfile and DiffTestIOTrace
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config ET" -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --iotrace-name iotrace
            cd difftest && export NOOP_HOME=$(pwd)
            make difftest_verilog PROFILE=../build/generated-src/difftest_profile.json CONFIG=EL MFC=1
            make emu WITH_CHISELDB=0 WITH_CONSTANTIN=0 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --iotrace-name ../iotrace
            make clean && make -C .. clean

      - name: Difftest with JsonProfile and DiffTestIOTrace-ZSTD
        run: |
            cd $NOOP_HOME
            make emu MILL_ARGS="--difftest-config ET" IOTRACE_ZSTD=1 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --iotrace-name iotrace
            cd difftest && export NOOP_HOME=$(pwd)
            make difftest_verilog PROFILE=../build/generated-src/difftest_profile.json CONFIG=EL MFC=1
            make emu WITH_CHISELDB=0 WITH_CONSTANTIN=0 IOTRACE_ZSTD=1 -j2
            ./build/emu -i $WORKLOAD --diff $REF_SO --iotrace-name ../iotrace
            make clean && make -C .. clean

  test-difftest-vcs:
    runs-on: ubuntu-22.04

    needs: test-difftest-main

    steps:
      - uses: actions/checkout@v4

      - name: Enable -Werror for EMU Build
        run: |
          echo "CXX_NO_WARNING=1" >> $GITHUB_ENV

      - name: Prepare environment
        run: |
            cd $GITHUB_WORKSPACE/..
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/install-verilator.sh
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/setup-tools.sh
            sudo bash setup-tools.sh

      - name: Prepare NutShell
        run: |
            cd $GITHUB_WORKSPACE/..
            git clone -b dev-difftest --single-branch https://github.com/OSCPU/NutShell.git
            cd NutShell && rm -rf difftest && cp -r $GITHUB_WORKSPACE .
            echo "NOOP_HOME=$(pwd)" >> $GITHUB_ENV
            echo "WORKLOAD=$(pwd)/ready-to-run/microbench.bin" >> $GITHUB_ENV
            echo "REF_SO=$(pwd)/ready-to-run/riscv64-nemu-interpreter-so" >> $GITHUB_ENV

      - name: Verilator Build with VCS Top (NO_DIFF)
        run: |
            cd $NOOP_HOME
            make simv NO_DIFF=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +max-cycles=100000
            make clean

      - name: Verilator Build with VCS Top (SYNTHESIS)(with DutZone)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config Z" SYNTHESIS=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            make clean

      - name: Verilator Build with VCS Top (disable DPIC RAM/FLASH)(with DutZone)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config Z" DISABLE_DIFFTEST_RAM_DPIC=1 DISABLE_DIFFTEST_FLASH_DPIC=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO
            make clean

      - name: Verilator Build with VCS Top (with DutZone PerfCnt Query)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config ZP" DIFFTEST_PERFCNT=1 DIFFTEST_QUERY=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO
            make clean

      - name: Verilator Build with VCS Top (with Batch InternalStep PerfCnt)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config BIP" DIFFTEST_PERFCNT=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO
            make clean

      - name: Verilator Build with VCS Top (with GlobalEnable Squash Replay Batch PerfCnt)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config ESRBP" DIFFTEST_PERFCNT=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO
            make clean

      - name: Verilator Build with VCS Top (with GlobalEnable Squash Replay Batch InternalStep NonBlock PerfCnt Query)
        run: |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config ESRBINP" DIFFTEST_PERFCNT=1 DIFFTEST_QUERY=1 VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +no-diff +max-cycles=100000
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO
            make clean

      - name: Verilator Build with VCS Top (with workload-list)
        run : |
            cd $NOOP_HOME
            echo "./ready-to-run/microbench.bin 10000" > list.txt
            echo "./ready-to-run/linux.bin 20000" >> list.txt
            make simv VCS=verilator WORKLOAD_SWITCH=1 -j2
            ./build/simv +diff=$REF_SO +workload-list=./list.txt
            make clean

      - name: Verilator Build with VCS Top (with JsonProfile and DiffTestIOTrace)
        run : |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config ZET" VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO +iotrace-name=iotrace
            cd difftest && export NOOP_HOME=$(pwd)
            make difftest_verilog PROFILE=../build/generated-src/difftest_profile.json CONFIG=ZEL MFC=1
            make simv VCS=verilator WITH_CHISELDB=0 WITH_CONSTANTIN=0
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO +iotrace-name=../iotrace
            make clean && make -C .. clean

      - name: Verilator Build with VCS Top (with JsonProfile and DiffTestIOTrace-ZSTD)
        run : |
            cd $NOOP_HOME
            make simv MILL_ARGS="--difftest-config ZET" VCS=verilator IOTRACE_ZSTD=1 -j2
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO +iotrace-name=iotrace
            cd difftest && export NOOP_HOME=$(pwd)
            make difftest_verilog PROFILE=../build/generated-src/difftest_profile.json CONFIG=ZEL MFC=1
            make simv VCS=verilator WITH_CHISELDB=0 WITH_CONSTANTIN=0 IOTRACE_ZSTD=1
            ./build/simv +workload=$WORKLOAD +diff=$REF_SO +iotrace-name=../iotrace
            make clean && make -C .. clean

      - name: Verilator Build with VCS Top (with LogPerf and WarmUp)
        run : |
            cd $NOOP_HOME
            make simv VCS=verilator -j2
            ./build/simv +workload=$WORKLOAD +b=0 +e=-1 +diff=$REF_SO +max-instrs=5000 +warmup_instr=1000
            make clean

  test-difftest-fpga:
    runs-on: ubuntu-22.04

    needs: test-difftest-main

    steps:
      - uses: actions/checkout@v4

      - name: Prepare environment
        run: |
            cd $GITHUB_WORKSPACE/..
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/install-verilator.sh
            wget https://github.com/OpenXiangShan/xs-env/raw/refs/heads/master/setup-tools.sh
            sudo bash setup-tools.sh

      - name: Prepare NutShell
        run: |
            cd $GITHUB_WORKSPACE/..
            git clone -b dev-difftest --single-branch https://github.com/OSCPU/NutShell.git
            cd NutShell && rm -rf difftest && cp -r $GITHUB_WORKSPACE .
            echo "NOOP_HOME=$(pwd)" >> $GITHUB_ENV

      - name: FPGA-difftest Build
        run: |
            cd $NOOP_HOME
            make sim-verilog MILL_ARGS="--difftest-config SF" -j2
            cd ./difftest
            make fpga-build FPGA=1
