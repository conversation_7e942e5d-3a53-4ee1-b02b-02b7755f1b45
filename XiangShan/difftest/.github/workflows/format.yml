name: format

on:
  pull_request:
    branches: [ master ]

jobs:
  format:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Mill Installation
        run: |
          sudo curl -L https://github.com/com-lihaoyi/mill/releases/download/0.11.1/0.11.1 > /usr/local/bin/mill
          chmod +x /usr/local/bin/mill

      - name: Clang Format Installation
        run: |
          pip install clang-format==18.1.4

      - name: Scala Formatter
        run: |
          make scala-format

      - name: Clang Formatter
        run: |
          make clang-format

      - name: Format Check
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "Please run 'make format' manually"
            exit 1
          fi
