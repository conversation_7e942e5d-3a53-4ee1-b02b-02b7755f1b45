name: Feature request
description: Suggest an idea for XiangShan project.
labels: 'feature request'
body:
  - type: markdown
    attributes:
      value: |
        You could use English or Chineses to open an issue.
        您可以使用中文或英文提问，我们推荐使用英文。

        This template is only used for suggesting an idea for XiangShan project. If this doesn't look right, please back and choose another template.
        这个 issue 模板仅用于向香山提出想法、建议、特性需求。如果您遇到的不是此类问题，请回退并选择其他模板。

        Please fill in the following information so we can better understand and address your issue. Thank you!
        请填写以下内容，以便我们更好地理解和处理您的问题。谢谢！
  - type: checkboxes
    id: before-start
    attributes:
      label: Before start
      description: |
        Before start, PLEASE MAKE SURE you have done these:
        开始之前，请确保您已经做过以下事情：
      options:
        - label: I have read the [RISC-V ISA Manual](https://github.com/riscv/riscv-isa-manual). 我已经阅读过 RISC-V 指令集手册。
          required: true
        - label: I have read the [XiangShan Documents](https://xiangshan-doc.readthedocs.io/zh_CN/latest). 我已经阅读过香山文档。
          required: true
        - label: I have searched the previous issues and did not find anything relevant. 我已经搜索过之前的 issue，并没有找到相关的。
          required: true
        - label: I have searched the previous discussions and did not find anything relevant. 我已经搜索过之前的 discussions，并没有找到相关的。
          required: true
        - label: I have reviewed the commit messages from the relevant commit history. 我已经浏览过相关的提交历史和提交信息。
          required: true
  - type: textarea
    id: describe
    attributes:
      label: Describe the feature you'd like
      description: |
        A clear and concise description of what you want. 请清晰、准确地描述您期望的特性。
        Also your consideration of that. 以及您的具体考虑。
      placeholder: 
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here. 可在此处附上其他相关信息。
      placeholder: 
    validations:
      required: false
