name: Problem
description: Problem encountered when compiling XiangShan, running EMU, etc.
labels: 'problem'
body:
  - type: markdown
    attributes:
      value: |
        You could use English or Chineses to open an issue.
        您可以使用中文或英文提问，我们推荐使用英文。

        This template is only used for problem encountered when compiling XiangShan, running EMU, etc. If this doesn't look right, please back and choose another template.
        这个 issue 模板仅用于处理您在编译香山、运行 EMU 等时遇到的问题。如果您遇到的不是此类问题，请回退并选择其他模板。

        Please fill in the following information so we can better understand and address your issue. Thank you!
        请填写以下内容，以便我们更好地理解和处理您的问题。谢谢！
  - type: checkboxes
    id: before-start
    attributes:
      label: Before start
      description: |
        Before start, PLEASE MAKE SURE you have done these:
        开始之前，请确保您已经做过以下事情：
      options:
        - label: I have read the [XiangShan Documents](https://xiangshan-doc.readthedocs.io/zh_CN/latest). 我已经阅读过香山文档。
          required: true
        - label: I have searched the previous issues and did not find anything relevant. 我已经搜索过之前的 issue，并没有找到相关的。
          required: true
        - label: I have searched the previous discussions and did not find anything relevant. 我已经搜索过之前的 discussions，并没有找到相关的。
          required: true
  - type: textarea
    id: describe
    attributes:
      label: Describe you problem
      description: |
        A clear and concise description of what the problem is. 请清晰、准确地描述您遇到的问题。
        If applicable, add screenshots to help explain your problem. 建议您附上遇到问题时的屏幕截图。
      placeholder: 
    validations:
      required: true
  - type: textarea
    id: what-did
    attributes:
      label: What did you do before
      description: |
        Please describe step by step what did you do before you encounted the problem. 请描述您遇到问题前都做了哪些操作。
        This will help us to reproduce your problem. 这将有助于我们复现您的问题。
      placeholder: 
    validations:
      required: true
  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: |
        Please provide your environment information. 请提供您的环境信息。
        
        Tips: 
         - gcc version: **run `gcc --version` and paste first line of the output**
         - mill version: **run `mill -i --version` and paste first line of the output**
         - java version: **run `java --version` and paste first line of the output**
      placeholder: 
      value: |
       - XiangShan branch: 
       - XiangShan commit id: 
       - NEMU commit id: 
       - SPIKE commit id: 
       - Operating System: 
       - gcc version: 
       - mill version: 
       - java version: 
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here. 可在此处附上其他相关信息。
      placeholder: 
    validations:
      required: false
