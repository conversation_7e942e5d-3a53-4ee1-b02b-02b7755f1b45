name: Performance Regression
on:
  schedule:
    #run at 4:00 UTC (12:00 UTC+8) on Friday
    - cron: '0 4 * * 5'
    #run at 4:00 UTC (12:00 UTC+8) Every two weeks
    # - cron: '0 4 13,27 * *'
  #run it manually when the workflow is in the default branch
  workflow_dispatch:
    inputs:
      test_commit:
        description: 'Commit SHA to run the workflow on'
        required: false
        default: ''
  #only for test push
  # push:
  #   branches: [ ci-perf-yml ]


jobs:
  run:
    runs-on: perf
    continue-on-error: true
    #At most 3 days to finish
    timeout-minutes: 4320
    name: Checkpoints
    steps:
      - name: Set test commit
        id: set_test_commit
        run: |
          if [ "${{ github.event.inputs.test_commit }}" = "" ]; then
            echo "Using latest commit."
            echo "commit_sha=${{ github.sha }}" >> $GITHUB_OUTPUT
          else
            echo "Using specified commit: ${{ github.event.inputs.test_commit }}"
            echo "commit_sha=${{ github.event.inputs.test_commit }}" >> $GITHUB_OUTPUT
          fi

      - name: Determine if this is the biweekly run
        id: determine_run
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "run_biweekly=true" >> $GITHUB_OUTPUT
          elif [ $(( 10#$(date +'%V') % 2 )) -eq 1 ]; then
            echo "run_biweekly=true" >> $GITHUB_OUTPUT
          else
            echo "run_biweekly=false" >> $GITHUB_OUTPUT
          fi

      - name: Checkout code at specific commit
        if: steps.determine_run.outputs.run_biweekly == 'true'
        uses: actions/checkout@v2
        with:
          ref: ${{ steps.set_test_commit.outputs.commit_sha }}
          submodules: 'recursive'

      - name: Set env
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          SHORT_SHA=$(git rev-parse --short HEAD)
          DATE=$(git show -s --format=%cd --date=format:%y%m%d HEAD)
          source /nfs/home/<USER>/.ci_env
          echo "NODE_SERVER_LIST=$NODE_SERVER_LIST" >> $GITHUB_ENV
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "AM_HOME=/nfs/home/<USER>/ci-workloads/nexus-am" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/ci-workloads/env-scripts/perf" >> $GITHUB_ENV
          echo "SPEC_DIR=/nfs/home/<USER>/master-perf-report/cr${DATE}-${SHORT_SHA}" >> $GITHUB_ENV
          echo "CKPT_HOME=/nfs/home/<USER>/checkpoints_profiles/spec06_rv64gcb_O3_20m_gcc12.2.0-intFpcOff-jeMalloc/checkpoint-0-0-0" >> $GITHUB_ENV
          echo "CKPT_JSON_PATH=/nfs/home/<USER>/checkpoints_profiles/spec06_rv64gcb_O3_20m_gcc12.2.0-intFpcOff-jeMalloc/checkpoint-0-0-0/cluster-0-0.json" >> $GITHUB_ENV

      - name: Clean Up
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean

      - name: Build EMU with DRAMsim3
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          if [ -e "$SPEC_DIR/emu" ]; then
            mkdir -p $NOOP_HOME/build
            cp $SPEC_DIR/emu $NOOP_HOME/build/emu
          else
            python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build          \
              --dramsim3 /nfs/home/<USER>/ci-workloads/DRAMsim3              \
              --with-dramsim3 --threads 16                                  \
              --pgo $GITHUB_WORKSPACE/ready-to-run/coremark-2-iteration.bin \
              --llvm-profdata llvm-profdata
            mkdir -p $SPEC_DIR
            cp $NOOP_HOME/build/emu $SPEC_DIR/emu
          fi

      - name: Run SPEC CPU2006 checkpoints
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          cd $PERF_HOME
          python3 xs_autorun_multiServer.py $CKPT_HOME $CKPT_JSON_PATH \
            --xs $NOOP_HOME --threads 16 --dir $SPEC_DIR --resume \
            -L "$NODE_SERVER_LIST"
          find $NOOP_HOME/build/ -maxdepth 1 -name "*.vcd" -exec mv {} $SPEC_DIR \;

      - name: Report SPEC CPU2006 score
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          cd $PERF_HOME
          python3 xs_autorun_multiServer.py $CKPT_HOME $CKPT_JSON_PATH \
            --xs $NOOP_HOME --threads 16 --dir $SPEC_DIR \
            --check --dump-json-path $SPEC_DIR/err_ckps.json
          python3 xs_autorun_multiServer.py $CKPT_HOME $CKPT_JSON_PATH \
            --xs $NOOP_HOME --threads 16 --dir $SPEC_DIR --report \
            > $SPEC_DIR/score.txt
          # mkdir $GITHUB_WORKSPACE/result
          # cp $SPEC_DIR/err_ckps.json $GITHUB_WORKSPACE/result/err_ckps.json
          # cp $SPEC_DIR/score.txt $GITHUB_WORKSPACE/result/score.txt

      - name: Summary result
        if: steps.determine_run.outputs.run_biweekly == 'true'
        run: |
          echo "### :rocket: Performance Test Result" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "$SPEC_DIR/score.txt" >> $GITHUB_STEP_SUMMARY
          tail -n 39 $SPEC_DIR/score.txt >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          
          echo "### :rainbow: Key Indicators" >> $GITHUB_STEP_SUMMARY
          echo "Estimated SPEC CPU2006 Score per GHz: " >> $GITHUB_STEP_SUMMARY
          TOTAL_SCORE=$(grep "SPEC2006/GHz:" $SPEC_DIR/score.txt | awk '{print $NF}')
          INT_SCORE=$(grep "SPECint2006/GHz:" $SPEC_DIR/score.txt | awk '{print $NF}')
          FP_SCORE=$(grep "SPECfp2006/GHz:" $SPEC_DIR/score.txt | awk '{print $NF}')
          echo "- int: **${INT_SCORE}**" >> $GITHUB_STEP_SUMMARY
          echo "- fp: **${FP_SCORE}**" >> $GITHUB_STEP_SUMMARY
          echo "- total: **${TOTAL_SCORE}**" >> $GITHUB_STEP_SUMMARY

          if ! grep -q '^{\s*}$' $SPEC_DIR/err_ckps.json; then
            echo "### :sos: Failed Tests" >> $GITHUB_STEP_SUMMARY  
            echo '```' >> $GITHUB_STEP_SUMMARY
            cat $SPEC_DIR/err_ckps.json >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
          fi

      # - name: Upload result
      #   if: steps.determine_run.outputs.run_biweekly == 'true'
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: result
      #     path: result
