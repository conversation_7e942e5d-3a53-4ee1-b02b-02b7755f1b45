name: Nightly Regression
on:
  schedule:
    # run at 15:33 UTC (23:33 UTC+8)
    - cron: '33 15 * * *'

jobs:
  build:
    runs-on: bosc
    continue-on-error: false
    # At most 12 hours to finish (before 11:33 UTC+8).
    timeout-minutes: 720
    # Build + 8 checkpoints * 1-hour timeout
    name: Nightly Regression - Checkpoints
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: 'recursive'
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/xs-perf/${HEAD_SHA}" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          echo "GCPT_RESTORE_BIN=/nfs/home/<USER>/ci-workloads/fix-gcpt/gcpt.bin" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-perf/${HEAD_SHA}
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: Build EMU with DRAMsim3 and Spike-Diff
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build          \
            --dramsim3 /nfs/home/<USER>/ci-workloads/DRAMsim3              \
            --with-dramsim3 --threads 16 --spike                          \
            --pgo $GITHUB_WORKSPACE/ready-to-run/coremark-2-iteration.bin \
            --llvm-profdata llvm-profdata
      - name: Random Checkpoint 0
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_0.txt
      - name: Random Checkpoint 1
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_1.txt
      - name: Random Checkpoint 2
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_2.txt
      - name: Random Checkpoint 3
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_3.txt
      - name: Random Checkpoint 4
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_4.txt
      - name: Random Checkpoint 5
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_5.txt
      - name: Random Checkpoint 6
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_6.txt
      - name: Random Checkpoint 7
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py           \
            --wave-dump $WAVE_HOME --threads 16 --numa             \
            --spike --ci random --timeout 3600 --ram-size=16GB     \
            --gcpt-restore-bin $GCPT_RESTORE_BIN                   \
            2> perf.log
          cat perf.log | sort | tee $PERF_HOME/random_7.txt
