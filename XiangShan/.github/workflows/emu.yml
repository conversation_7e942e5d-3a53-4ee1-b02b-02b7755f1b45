# This file describes the GitHub Actions workflow for continuous integration of XS Core.
name: EMU Test

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  changes: # Changes Detection
    name: Changes Detection
    runs-on: ubuntu-latest
    # Required permissions
    permissions:
      pull-requests: read
    # Set job outputs to values from filter step
    outputs:
      core: ${{ steps.filter.outputs.core }}
    steps:
    - uses: actions/checkout@v4
    - uses: dorny/paths-filter@v3
      id: filter
      with:
        predicate-quantifier: 'every'
        filters: .github/filters.yaml

  generate-verilog:
    runs-on: bosc
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: Generate Verilog
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: check top wiring
        run:
          bash .github/workflows/check-usage.sh "BoringUtils" $GITHUB_WORKSPACE
      - name: generate standalone devices for TL
        run: |
          make StandAloneCLINT DEVICE_BASE_ADDR=0x38000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=1 DEVICE_PREFIX=CLINT_
          make StandAloneDebugModule DEVICE_BASE_ADDR=0x38020000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=1 DEVICE_PREFIX=DM_
          make StandAlonePLIC DEVICE_BASE_ADDR=0x3C000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=1 DEVICE_PREFIX=PLIC_
          make clean
      - name: generate standalone devices for AXI4
        run: |
          make StandAloneCLINT DEVICE_BASE_ADDR=0x38000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=CLINT_
          make StandAloneDebugModule DEVICE_BASE_ADDR=0x38020000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=DM_
          make StandAlonePLIC DEVICE_BASE_ADDR=0x3C000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=PLIC_
          make clean
      - name: generate XSNoCTop verilog file
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --generate --config XSNoCTopConfig
      - name: check XSNoCTop verilog
        run: |
          python3 $GITHUB_WORKSPACE/.github/workflows/check_verilog.py build/rtl
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: generate verilog file
        run:
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --generate --num-cores 2
      - name: check verilog
        run: |
          python3 $GITHUB_WORKSPACE/.github/workflows/check_verilog.py build/rtl
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: generate chirrtl only
        run: |
          make sim-verilog CHISEL_TARGET=chirrtl
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: build MinimalConfig Release emu
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build \
            --threads 8 --config MinimalConfig --release
      - name: run MinimalConfig - Linux
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 8 --numa --ci linux-hello-opensbi 2> perf.log
          cat perf.log | sort
      - name: generate NOC XiangShan
        run: |
          make clean
          make verilog PLDM=1 PLDM_ARGS="--difftest-config H" CONFIG=XSNoCDiffTopConfig
      - name: generate NOC Difftest
        run: |
          export NOOP_HOME=$GITHUB_WORKSPACE/difftest
          cd difftest && make difftest_verilog PROFILE=../build/generated-src/difftest_profile.json NUM_CORES=2 CONFIG=ZESNH
      - name: check interface between XSNoCDiffTop and Difftest
        run: |
          cd $GITHUB_WORKSPACE
          python3 ./difftest/scripts/st_tools/interface.py ./difftest/build/rtl/GatewayEndpoint.sv
          python3 ./difftest/scripts/st_tools/interface.py ./build/rtl/XSDiffTop.sv --core --filelist ./build/rtl/filelist.f --simtop ./difftest/build/rtl/SimTop.sv
          cp -r -v ./difftest/build/* ./build/
          verilator --lint-only -Wno-fatal --top-module XSDiffTopChecker build/XSDiffTopChecker.sv build/rtl/*sv build/rtl/*v -Ibuild/generated-src/ -LDFLAGS -"lreadline"

  emu-basics:
    runs-on: bosc
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: EMU - Basics
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "AM_HOME=/nfs/home/<USER>/ci-workloads/nexus-am" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/xs-perf/${HEAD_SHA}" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          echo "GCPT_RESTORE_BIN=/nfs/home/<USER>/ci-workloads/fix-gcpt/gcpt.bin" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-perf/${HEAD_SHA}
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: Build EMU
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --build --threads 16 \
            --pgo $GITHUB_WORKSPACE/ready-to-run/coremark-2-iteration.bin --llvm-profdata llvm-profdata
      - name: Basic Test - cputest
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --ci cputest 2> /dev/zero
      - name: Basic Test - riscv-tests
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --rvtest /nfs/home/<USER>/ci-workloads/riscv-tests --ci riscv-tests 2> /dev/zero
      - name: Basic Test - misc-tests
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci misc-tests 2> /dev/zero
      - name: Extension Test - hypervisor
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci rvh-tests 2> /dev/zero
      - name: Simple Test - microbench
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --ci microbench 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/microbench.log
      - name: Simple Test - CoreMark
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci coremark 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/coremark.log
      - name: System Test - Linux
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci linux-hello-opensbi 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/linux.log
      - name: Floating-point Test - povray
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --max-instr 5000000 --ci povray --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/povray.log
      - name: Uncache Fetch Test - copy and run
        run: |
          $GITHUB_WORKSPACE/build/emu  -F $GITHUB_WORKSPACE/ready-to-run/copy_and_run.bin -i $GITHUB_WORKSPACE/ready-to-run/microbench.bin --diff $GITHUB_WORKSPACE/ready-to-run/riscv64-nemu-interpreter-so --enable-fork 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/copy_and_run.log
      - name: V Extension Test - rvv-bench
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci rvv-bench 2> /dev/zero
      - name: F16 Test - f16_test
        run: |
          python3 ./scripts/xiangshan.py --wave-dump ./build --thread 16 --numa --ci f16_test 2> /dev/zero
      - name: Zcb Extension Test - zcb-test
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --ci zcb-test 2> /dev/zero

  emu-chi:
    runs-on: bosc
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: EMU - CHI
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: build KunminghuV2Config Release emu
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build \
            --threads 8 --config KunminghuV2Config --release
      - name: run KunminghuV2Config - Linux
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 8 --numa --ci linux-hello-opensbi 2> perf.log
          cat perf.log | sort

  emu-performance:
    runs-on: bosc
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: EMU - Performance
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "AM_HOME=/nfs/home/<USER>/ci-workloads/nexus-am" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/xs-perf/${HEAD_SHA}" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          echo "GCPT_RESTORE_BIN=/nfs/home/<USER>/ci-workloads/fix-gcpt/gcpt.bin" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-perf/${HEAD_SHA}
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: Build EMU
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build \
            --yaml-config $GITHUB_WORKSPACE/src/main/resources/config/Default.yml \
            --dramsim3 /nfs/home/<USER>/ci-workloads/DRAMsim3            \
            --with-dramsim3 --threads 16 \
            --pgo $GITHUB_WORKSPACE/ready-to-run/coremark-2-iteration.bin --llvm-profdata llvm-profdata
      - name: SPEC06 Test - hmmer-Vector
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci hmmer-Vector 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/hmmer-Vector.log
      - name: SPEC06 Test - mcf
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci mcf --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/mcf.log
      - name: SPEC06 Test - xalancbmk
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci xalancbmk --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/xalancbmk.log
      - name: SPEC06 Test - gcc
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci gcc --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/gcc.log
      - name: SPEC06 Test - namd
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci namd --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/namd.log
      - name: SPEC06 Test - milc
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci milc --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/milc.log
      - name: SPEC06 Test - lbm
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci lbm --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/lbm.log
      - name: SPEC06 Test - gromacs
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci gromacs --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/gromacs.log
      - name: SPEC06 Test - wrf
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci wrf --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/wrf.log
      - name: SPEC06 Test - astar
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --max-instr 5000000 --numa --ci astar --gcpt-restore-bin $GCPT_RESTORE_BIN 2> perf.log
          cat perf.log | sort | tee $PERF_HOME/astar.log

  emu-mc:
    runs-on: bosc
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: EMU - MC
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "AM_HOME=/nfs/home/<USER>/ci-workloads/nexus-am" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/xs-perf/${HEAD_SHA}" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-perf/${HEAD_SHA}
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: Build MC EMU
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --build \
            --num-cores 2 --emu-optimize "" \
            --dramsim3 /nfs/home/<USER>/ci-workloads/DRAMsim3 \
            --with-dramsim3 --threads 16 \
            --pgo /nfs/home/<USER>/ci-workloads/linux-hello-smp-new/bbl.bin --llvm-profdata llvm-profdata
      - name: MC Test
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --diff ./ready-to-run/riscv64-nemu-interpreter-dual-so --ci mc-tests 2> /dev/zero
      - name: SMP Linux
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --wave-dump $WAVE_HOME --threads 16 --numa --diff ./ready-to-run/riscv64-nemu-interpreter-dual-so --ci linux-hello-smp-new 2> /dev/null

  simv-basics:
    runs-on: eda
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 900
    name: SIMV - Basics
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: |
          export HEAD_SHA=${{ github.run_number }}
          echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "NEMU_HOME=/nfs/home/<USER>/ci-workloads/NEMU" >> $GITHUB_ENV
          echo "AM_HOME=/nfs/home/<USER>/ci-workloads/nexus-am" >> $GITHUB_ENV
          echo "PERF_HOME=/nfs/home/<USER>/xs-perf/${HEAD_SHA}" >> $GITHUB_ENV
          echo "WAVE_HOME=/nfs/home/<USER>/xs-wave/${HEAD_SHA}" >> $GITHUB_ENV
          mkdir -p /nfs/home/<USER>/xs-perf/${HEAD_SHA}
          mkdir -p /nfs/home/<USER>/xs-wave/${HEAD_SHA}
      - name: init
        run: make init
      - name: clean up
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: Remote Connection Test
        run: |
          ssh -tt eda01 "echo test-ok"
      - name: Generate Verilog for VCS
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --vcs-gen --no-db --xprop
      - name: Build SIMV on Remote
        run: |
          ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --vcs-build --no-db --xprop"
      - name: V Extension Test - rvv-test
        run: |
          ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --wave-dump `echo $WAVE_HOME` --ci-vcs rvv-test"
      - name: Simple Test - CoreMark 1 iteration
        run: |
          ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --wave-dump `echo $WAVE_HOME` --ci-vcs coremark-1-iteration"
      # - name: Simple Test - MicroBench
      #   run: |
      #     ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --wave-dump `echo $WAVE_HOME` --ci-vcs microbench --am=/nfs/home/<USER>/ci-workloads/nexus-am/"
      # - name: Basic Test - cputest
      #   run: |
      #     ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --ci-vcs cputest --am=/nfs/home/<USER>/ci-workloads/nexus-am/" 2> /dev/zero
      # - name: Simple Test - CoreMark
      #   run: |
      #     ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --ci-vcs coremark --am=/nfs/home/<USER>/ci-workloads/nexus-am/ --timeout 1800" 2> /dev/zero
      # - name: System Test - Linux
      #   run: |
      #     ssh -tt eda01 "python3 `echo $GITHUB_WORKSPACE`/scripts/xiangshan.py --ci-vcs linux-hello-opensbi --timeout 7200" 2> /dev/zero

  artifacts-uploading:
    runs-on: ubuntu-latest
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 40
    name: Upload Artifacts
    steps:
      - uses: actions/checkout@v4
      - name: set env
        run: echo "NOOP_HOME=$GITHUB_WORKSPACE" >> $GITHUB_ENV
      - name: psutil
        run: sudo apt install python3-psutil
      - name: init
        run: make init
      - name: mill
        uses: jodersky/setup-mill@v0.3.0
        with:
          mill-version: 0.12.3
      - name: swapfile
        run: |
          sudo fallocate -l 10G /swapfile
          sudo chmod 600 /swapfile
          sudo mkswap /swapfile
          sudo swapon /swapfile
      - name: clean up
        run: python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: generate standalone devices for AXI4
        run: |
          make StandAloneCLINT DEVICE_BASE_ADDR=0x38000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=CLINT_
          make StandAloneDebugModule DEVICE_BASE_ADDR=0x38020000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=DM_
          make StandAlonePLIC DEVICE_BASE_ADDR=0x3C000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=PLIC_
      - name: generate CHI Issue B XSNoCTop verilog with difftest and filelist
        run: |
          make verilog WITH_CONSTANTIN=0 WITH_CHISELDB=0 CONFIG='XSNoCTopConfig --enable-difftest' ISSUE=B XSTOP_PREFIX=bosc_ JVM_XMX=16g
          rm `find $GITHUB_WORKSPACE/build -name "*.fir"`
          cd $GITHUB_WORKSPACE/build/rtl && find . -name "*.*v" > filelist.f
      - name: acrhive issue B verilog artifacts
        uses: actions/upload-artifact@v4
        with:
          name: xs-issue-b-difftest-verilog
          path: build
      - name: clean up
        run: python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
      - name: generate standalone devices for AXI4
        run: |
          make StandAloneCLINT DEVICE_BASE_ADDR=0x38000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=CLINT_
          make StandAloneDebugModule DEVICE_BASE_ADDR=0x38020000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=DM_
          make StandAlonePLIC DEVICE_BASE_ADDR=0x3C000000 DEVICE_ADDR_WIDTH=32 DEVICE_DATA_WIDTH=64 DEVICE_TL=0 DEVICE_PREFIX=PLIC_
      - name: generate CHI Issue E.b XSNoCTop verilog with difftest and filelist
        run: |
          make verilog WITH_CONSTANTIN=0 WITH_CHISELDB=0 CONFIG='XSNoCTopConfig --enable-difftest' ISSUE=E.b XSTOP_PREFIX=bosc_ JVM_XMX=16g
          rm `find $GITHUB_WORKSPACE/build -name "*.fir"`
          cd $GITHUB_WORKSPACE/build/rtl && find . -name "*.*v" > filelist.f
      - name: acrhive issue E.b verilog artifacts
        uses: actions/upload-artifact@v4
        with:
          name: xs-issue-e-b-difftest-verilog
          path: build
      - name: generate test-jar
        run: |
          python3 $GITHUB_WORKSPACE/scripts/xiangshan.py --clean
          make test-jar
      - name: acrhive test jar artifacts
        uses: actions/upload-artifact@v4
        with:
          name: xsgen
          path: out/xiangshan/test/assembly.dest/out.jar

  check-submodules:
    runs-on: ubuntu-latest
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: false
    timeout-minutes: 5
    name: Check Submodules
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'true'
          fetch-depth: '0'
      - name: check rocket-chip
        run: cd rocket-chip && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check difftest
        run: cd difftest && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check ready-to-run
        run: cd ready-to-run && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check huancun
        run: cd huancun && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check fudian
        run: cd fudian && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/pipeline
      - name: check utility
        run: cd utility && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check yunsuan
        run: cd yunsuan && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check coupledL2
        run: cd coupledL2 && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check openLLC
        run: cd openLLC && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/master
      - name: check src/main/resources/aia
        run: cd src/main/resources/aia && git fetch --all && git merge-base --is-ancestor `git rev-parse HEAD` origin/main

  scalafmt:
    runs-on: ubuntu-latest
    needs: changes
    if: ${{ needs.changes.outputs.core == 'true' }}
    continue-on-error: true
    timeout-minutes: 900
    name: Check Format
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'recursive'
      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '11'
      - run: |
          mkdir -p ~/.local/bin
          sh -c "curl -L https://github.com/com-lihaoyi/mill/releases/download/0.11.7/0.11.7 > ~/.local/bin/mill && chmod +x ~/.local/bin/mill"
          export PATH=~/.local/bin:$PATH
      - run: make check-format
