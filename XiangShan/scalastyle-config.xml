<scalastyle commentFilter="enabled">
 <name>Scalastyle XiangShan configuration</name>

 <!-- Ref: https://scalastyle.github.io/rules-1.0.0.html -->

 <!-- ===== license ===== -->
 <!-- always starts with XiangShan license -->
 <check enabled="true" class="org.scalastyle.file.HeaderMatchesChecker" level="warning">
  <parameters>
   <parameter name="regex">true</parameter>
   <parameter name="header"><![CDATA[(?m)(?:// Copyright \(c\) \d{4}(?:-\d{4})? (?:.+?)\n)+//
// XiangShan is licensed under Mulan PSL v2.
// You can use this software according to the terms and conditions of the Mulan PSL v2.
// You may obtain a copy of Mulan PSL v2 at:
//          https://license.coscl.org.cn/MulanPSL2
//
// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
// EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
// MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
//
// See the Mulan PSL v2 for more details.]]></parameter>
  </parameters>
 </check>

 <!-- ===== file size ===== -->
 <!-- avoid large files (>800 lines, or >20 classes) -->
 <check enabled="true" class="org.scalastyle.file.FileLengthChecker" level="warning">
  <parameters>
   <parameter name="maxFileLength">800</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.scalariform.NumberOfTypesChecker" level="warning">
  <parameters>
   <parameter name="maxTypes">20</parameter>
  </parameters>
 </check>
 <!-- avoid long lines (>120 columns) -->
 <check enabled="true" class="org.scalastyle.file.FileLineLengthChecker" level="warning">
  <parameters>
   <parameter name="maxLineLength">120</parameter> <!-- should be same with .scalafmt.conf maxColumn -->
   <parameter name="tabSize">2</parameter>
   <parameter name="ignoreImports">true</parameter>
  </parameters>
 </check>
 <!-- avoid large classes (>30 methods) -->
 <check enabled="true" class="org.scalastyle.scalariform.NumberOfMethodsInTypeChecker" level="warning">
  <parameters>
   <parameter name="maxMethods">30</parameter>
  </parameters>
 </check>
 <!-- avoid large methods (>8 parameters, or >50 lines) -->
 <check enabled="true" class="org.scalastyle.scalariform.ParameterNumberChecker" level="warning">
  <parameters>
   <parameter name="maxParameters">8</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.scalariform.MethodLengthChecker" level="warning">
  <parameters>
   <parameter name="maxLength">50</parameter>
   <parameter name="ignoreComments">true</parameter>
  </parameters>
 </check>

 <!-- ===== tabs, new lines, and spaces ===== -->
 <!-- avoid tabs -->
 <check enabled="true" class="org.scalastyle.file.FileTabChecker" level="warning"/>
 <!-- always add a new line at EOF -->
 <check enabled="true" class="org.scalastyle.file.NewLineAtEofChecker" level="warning"/>
 <!-- avoid extra spaces from EOL -->
 <check enabled="true" class="org.scalastyle.file.WhitespaceEndOfLineChecker" level="warning"/>
 <!-- always add a space after `//` or `/*` before comments -->
 <check enabled="true" class="org.scalastyle.scalariform.SpaceAfterCommentStartChecker" level="warning"/>
 <!-- check space around operators, ref: https://github.com/scala-ide/scalariform/blob/master/scalariform/src/main/scala/scalariform/lexer/Tokens.scala -->
 <check enabled="true" class="org.scalastyle.scalariform.DisallowSpaceAfterTokenChecker" level="warning">
  <parameters>
   <!-- (, ~, ! -->
   <parameter name="tokens">LPAREN, TILDE, EXCLAMATION</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.scalariform.DisallowSpaceBeforeTokenChecker" level="warning">
  <parameters>
   <!-- :, ,, ) -->
   <parameter name="tokens">COLON, COMMA, RPAREN</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.scalariform.EnsureSingleSpaceAfterTokenChecker" level="warning">
  <parameters>
   <!-- if, match, case, for, while, =>, <-, {, <:, <%:, >:, +, -, *, |, = -->
   <parameter name="tokens">IF, MATCH, CASE, FOR, WHILE, ARROW, LARROW, LBRACE, SUBTYPE, VIEWBOUND, SUPERTYPE, PLUS, MINUS, STAR, PIPE, EQUAL</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.scalariform.EnsureSingleSpaceBeforeTokenChecker" level="warning">
  <parameters>
   <!-- =>, <-, }, <:, <%, >:, +, -, *, |, = -->
   <parameter name="tokens">ARROW, LARROW, RBRACE, SUBTYPE, VIEWBOUND, SUPERTYPE, PLUS, MINUS, STAR, PIPE, EQUAL</parameter>
  </parameters>
 </check>
 <check enabled="true" class="org.scalastyle.file.RegexChecker" level="warning">
  <parameters>
   <!-- :=, :<=, :>=, :<>=, :#=, <>, ===, =/=, <<, >>, <=, >= -->
   <parameter name="regex"><![CDATA[[^ ](:<?#?>?=|<>|=[=/]=|<<|>>|[<>]=)]]></parameter>
  </parameters>
  <customMessage>No space before operators</customMessage>
 </check>
 <check enabled="true" class="org.scalastyle.file.RegexChecker" level="warning">
  <parameters>
   <!-- :=, :<=, :>=, :<>=, :#=, <>, ===, =/=, <<, >>, <=, >= -->
   <parameter name="regex"><![CDATA[(:<?#?>?=|<>|=[=/]=|<<|>>|[<>]=)[^ \n]]]></parameter>
  </parameters>
  <customMessage>No space or newline after operators</customMessage>
 </check>

 <!-- ===== imports ===== -->
 <!-- avoid block imports (`import pkg.{abc, def}`) -->
 <check enabled="true" class="org.scalastyle.scalariform.BlockImportChecker" level="warning"/>
 <!-- avoid wildcard imports (`import pkg._`) -->
 <check enabled="true" class="org.scalastyle.scalariform.UnderscoreImportChecker" level="warning">
  <parameters>
   <!-- except for `chisel3._` and `chisel3.util._` -->
   <parameter name="ignoreRegex">chisel3\._|chisel3\.util\._</parameter>
  </parameters>
 </check>

 <!-- ===== names ===== -->
 <!-- use UpperCamelCase for class names -->
 <check enabled="true" class="org.scalastyle.scalariform.ClassNamesChecker" level="warning">
  <parameters>
   <parameter name="regex">^[A-Z][A-Za-z0-9]*$</parameter>
  </parameters>
 </check>
 <!-- ... and object names -->
 <check enabled="true" class="org.scalastyle.scalariform.ObjectNamesChecker" level="warning">
  <parameters>
   <parameter name="regex">^[A-Z][A-Za-z0-9]*$</parameter>
  </parameters>
 </check>
 <!-- lowerCamelCase for variables and UpperCamelCase for constants -->
 <check enabled="true" class="org.scalastyle.scalariform.FieldNamesChecker" level="warning">
  <parameters>
   <!-- we allow sx_lowerCamelCase for pipeline signals -->
   <parameter name="regex">^(s[0-9]_)?[a-z][A-Za-z0-9]*$</parameter>
   <parameter name="objectFieldRegex">^[A-Z][A-Za-z0-9]*$</parameter>
  </parameters>
 </check>
 <!-- lowerCamelCase for parameters -->
 <check enabled="true" class="org.scalastyle.scalariform.MethodArgumentNamesChecker" level="warning">
  <parameters>
   <parameter name="regex">^[a-z][A-Za-z0-9]*$</parameter>
  </parameters>
 </check>
 <!-- lowerCamelCase or UpperCamelCase for methods, as we also use methods as constants -->
 <check enabled="true" class="org.scalastyle.scalariform.MethodNamesChecker" level="warning">
  <parameters>
   <parameter name="regex">^[A-Za-z0-9]*$</parameter>
   <parameter name="ignoreRegex"><![CDATA[^(\+[&%]?|\-[&%]?|\*|/|%|&|\||\^|<|>|\|\||&&|:=|<>|<=|>=|!=|===|<<|>>|##|unary_(~|\-%?|!))$]]></parameter>
  </parameters>
 </check>
 <!-- pure lower cases for package names -->
 <check enabled="true" class="org.scalastyle.scalariform.PackageNamesChecker" level="warning">
  <parameters>
   <parameter name="regex">^[a-z0-9]*$</parameter>
  </parameters>
 </check>

 <!-- type annotations -->
 <check enabled="true" class="org.scalastyle.scalariform.PublicMethodsHaveTypeChecker" level="warning"/>

 <!-- ===== misc ===== -->
 <!-- warn TODO and FIXME comments, we should fix them ASAP -->
 <check enabled="true" class="org.scalastyle.scalariform.TodoCommentChecker" level="warning"/>
 <!-- avoid define equals without overriding equals -->
 <check enabled="true" class="org.scalastyle.scalariform.CovariantEqualsChecker" level="warning"/>
 <!-- avoid using ';' to end line -->
 <check enabled="true" class="org.scalastyle.file.RegexChecker" level="warning">
  <parameters>
   <parameter name="regex">;\r?\n</parameter>
  </parameters>
  <customMessage>Avoid using ';' to end line</customMessage>
 </check>
</scalastyle>
