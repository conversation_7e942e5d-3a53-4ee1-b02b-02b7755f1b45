# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ master, ci-test, non-inclusive ]
  pull_request:
    branches: [ master, non-inclusive ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  tl-test_L2:
    # The type of runner that the job will run on
    runs-on: ubuntu-24.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2
        with:
          submodules: recursive

      - name: Setup <PERSON>
        uses: olafurpg/setup-scala@v10

      - name: Cache
        id: cache
        uses: coursier/cache-action@v5

      - name: Verilator
        run: sudo apt install verilator libsqlite3-dev

      - name: Setup Mill
        uses: jodersky/setup-mill@v0.2.3
        with:
          mill-version: 0.11.1

      # - name: Check scalafmt
      #   run: make checkformat
        
      - name: Compile
        run: make compile

      - name: Unit test
        run: |
            git clone https://github.com/OpenXiangShan/tl-test -b huancun
            make test-top-l2
            cd ./tl-test
            mkdir build && cd build
            cmake .. -DDUT_DIR=../../build -DTLLOG=1
            make
            ./tlc_test -s $RANDOM

  tl-test_L2L3:
    # The type of runner that the job will run on
    runs-on: ubuntu-24.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2
        with:
          submodules: recursive

      - name: Setup Scala
        uses: olafurpg/setup-scala@v10

      - name: Cache
        id: cache
        uses: coursier/cache-action@v5

      - name: Verilator
        run: sudo apt install verilator libsqlite3-dev

      - name: Setup Mill
        uses: jodersky/setup-mill@v0.2.3
        with:
          mill-version: 0.11.1

      # - name: Check scalafmt
      #   run: make checkformat

      - name: Compile
        run: make compile

      - name: Unit test
        run: |
          git clone https://github.com/OpenXiangShan/tl-test -b huancun
          make test-top-l2l3
          cd ./tl-test
          mkdir build && cd build
          cmake .. -DDUT_DIR=../../build -DTLLOG=1
          make
          ./tlc_test -s $RANDOM
