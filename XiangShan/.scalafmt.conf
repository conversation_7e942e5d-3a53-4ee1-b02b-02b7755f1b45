version = 3.8.1
runner.dialect = scala213
maxColumn = 120
preset = defaultWithAlign

align.tokens."+" = [{
  code = ":"
}]

newlines.source = keep
newlines.afterCurlyLambdaParams = squash

rewrite.rules = [
  RedundantBraces,
  RedundantParens,
  SortModifiers,
  Imports
]
rewrite.redundantBraces.generalExpressions = false
rewrite.imports.expand = true
rewrite.imports.sort = scalastyle
rewrite.trailingCommas.style = never

docstrings.style = keep

project.includePaths = ["glob:**/src/main/scala/xiangshan/frontend/**.scala"]
