name: CI

on:
  pull_request:
    branches:
    - master

name: Mill Continuous Integration
env:
  USER: runner

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: 'true'
      - uses: cachix/install-nix-action@v19
        with:
          install_url: https://releases.nixos.org/nix/nix-2.13.3/install
          nix_path: nixpkgs=channel:nixos-unstable
      - name: run test
        run: |
          nix --experimental-features 'nix-command flakes' develop -c mill -i -j 0 cdetest

  check_format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: 'true'
      - uses: cachix/install-nix-action@v19
        with:
          install_url: https://releases.nixos.org/nix/nix-2.13.3/install
          nix_path: nixpkgs=channel:nixos-unstable
      - name: check format
        run: |
          nix --experimental-features 'nix-command flakes' develop -c mill -i -j 0 "__.checkFormat"
