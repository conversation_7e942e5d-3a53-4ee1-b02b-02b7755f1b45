<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="434.726pt" height="143.808pt" viewBox="0 0 434.726 143.808" version="1.1" style="background-color: white">
<defs>
<g>
<symbol overflow="visible" id="glyph0-0">
<path style="stroke:none;" d=""/>
</symbol>
<symbol overflow="visible" id="glyph0-1">
<path style="stroke:none;" d="M 0.5625 -3.40625 C 0.5625 -1.34375 2.171875 0.21875 4.03125 0.21875 C 5.65625 0.21875 6.625 -1.171875 6.625 -2.328125 C 6.625 -2.421875 6.625 -2.5 6.5 -2.5 C 6.390625 -2.5 6.390625 -2.4375 6.375 -2.328125 C 6.296875 -0.90625 5.234375 -0.09375 4.140625 -0.09375 C 3.53125 -0.09375 1.578125 -0.421875 1.578125 -3.40625 C 1.578125 -6.375 3.53125 -6.71875 4.140625 -6.71875 C 5.21875 -6.71875 6.109375 -5.8125 6.3125 -4.359375 C 6.328125 -4.21875 6.328125 -4.1875 6.46875 -4.1875 C 6.625 -4.1875 6.625 -4.21875 6.625 -4.421875 L 6.625 -6.78125 C 6.625 -6.953125 6.625 -7.03125 6.515625 -7.03125 C 6.484375 -7.03125 6.4375 -7.03125 6.359375 -6.90625 L 5.859375 -6.171875 C 5.5 -6.53125 4.984375 -7.03125 4.03125 -7.03125 C 2.15625 -7.03125 0.5625 -5.4375 0.5625 -3.40625 Z M 0.5625 -3.40625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-2">
<path style="stroke:none;" d="M 2.890625 -3.515625 C 3.703125 -3.78125 4.28125 -4.46875 4.28125 -5.265625 C 4.28125 -6.078125 3.40625 -6.640625 2.453125 -6.640625 C 1.453125 -6.640625 0.6875 -6.046875 0.6875 -5.28125 C 0.6875 -4.953125 0.90625 -4.765625 1.203125 -4.765625 C 1.5 -4.765625 1.703125 -4.984375 1.703125 -5.28125 C 1.703125 -5.765625 1.234375 -5.765625 1.09375 -5.765625 C 1.390625 -6.265625 2.046875 -6.390625 2.40625 -6.390625 C 2.828125 -6.390625 3.375 -6.171875 3.375 -5.28125 C 3.375 -5.15625 3.34375 -4.578125 3.09375 -4.140625 C 2.796875 -3.65625 2.453125 -3.625 2.203125 -3.625 C 2.125 -3.609375 1.890625 -3.59375 1.8125 -3.59375 C 1.734375 -3.578125 1.671875 -3.5625 1.671875 -3.46875 C 1.671875 -3.359375 1.734375 -3.359375 1.90625 -3.359375 L 2.34375 -3.359375 C 3.15625 -3.359375 3.53125 -2.6875 3.53125 -1.703125 C 3.53125 -0.34375 2.84375 -0.0625 2.40625 -0.0625 C 1.96875 -0.0625 1.21875 -0.234375 0.875 -0.8125 C 1.21875 -0.765625 1.53125 -0.984375 1.53125 -1.359375 C 1.53125 -1.71875 1.265625 -1.921875 0.984375 -1.921875 C 0.734375 -1.921875 0.421875 -1.78125 0.421875 -1.34375 C 0.421875 -0.4375 1.34375 0.21875 2.4375 0.21875 C 3.65625 0.21875 4.5625 -0.6875 4.5625 -1.703125 C 4.5625 -2.515625 3.921875 -3.296875 2.890625 -3.515625 Z M 2.890625 -3.515625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-3">
<path style="stroke:none;" d="M 1.265625 -0.765625 L 2.328125 -1.796875 C 3.875 -3.171875 4.46875 -3.703125 4.46875 -4.703125 C 4.46875 -5.84375 3.578125 -6.640625 2.359375 -6.640625 C 1.234375 -6.640625 0.5 -5.71875 0.5 -4.828125 C 0.5 -4.28125 1 -4.28125 1.03125 -4.28125 C 1.203125 -4.28125 1.546875 -4.390625 1.546875 -4.8125 C 1.546875 -5.0625 1.359375 -5.328125 1.015625 -5.328125 C 0.9375 -5.328125 0.921875 -5.328125 0.890625 -5.3125 C 1.109375 -5.96875 1.65625 -6.328125 2.234375 -6.328125 C 3.140625 -6.328125 3.5625 -5.515625 3.5625 -4.703125 C 3.5625 -3.90625 3.078125 -3.125 2.515625 -2.5 L 0.609375 -0.375 C 0.5 -0.265625 0.5 -0.234375 0.5 0 L 4.203125 0 L 4.46875 -1.734375 L 4.234375 -1.734375 C 4.171875 -1.4375 4.109375 -1 4 -0.84375 C 3.9375 -0.765625 3.28125 -0.765625 3.0625 -0.765625 Z M 1.265625 -0.765625 "/>
</symbol>
<symbol overflow="visible" id="glyph0-4">
<path style="stroke:none;" d="M 2.9375 -6.375 C 2.9375 -6.625 2.9375 -6.640625 2.703125 -6.640625 C 2.078125 -6 1.203125 -6 0.890625 -6 L 0.890625 -5.6875 C 1.09375 -5.6875 1.671875 -5.6875 2.1875 -5.953125 L 2.1875 -0.78125 C 2.1875 -0.421875 2.15625 -0.3125 1.265625 -0.3125 L 0.953125 -0.3125 L 0.953125 0 C 1.296875 -0.03125 2.15625 -0.03125 2.5625 -0.03125 C 2.953125 -0.03125 3.828125 -0.03125 4.171875 0 L 4.171875 -0.3125 L 3.859375 -0.3125 C 2.953125 -0.3125 2.9375 -0.421875 2.9375 -0.78125 Z M 2.9375 -6.375 "/>
</symbol>
<symbol overflow="visible" id="glyph0-5">
<path style="stroke:none;" d="M 2.9375 -1.640625 L 2.9375 -0.78125 C 2.9375 -0.421875 2.90625 -0.3125 2.171875 -0.3125 L 1.96875 -0.3125 L 1.96875 0 C 2.375 -0.03125 2.890625 -0.03125 3.3125 -0.03125 C 3.734375 -0.03125 4.25 -0.03125 4.671875 0 L 4.671875 -0.3125 L 4.453125 -0.3125 C 3.71875 -0.3125 3.703125 -0.421875 3.703125 -0.78125 L 3.703125 -1.640625 L 4.6875 -1.640625 L 4.6875 -1.953125 L 3.703125 -1.953125 L 3.703125 -6.484375 C 3.703125 -6.6875 3.703125 -6.75 3.53125 -6.75 C 3.453125 -6.75 3.421875 -6.75 3.34375 -6.625 L 0.28125 -1.953125 L 0.28125 -1.640625 Z M 2.984375 -1.953125 L 0.5625 -1.953125 L 2.984375 -5.671875 Z M 2.984375 -1.953125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-6">
<path style="stroke:none;" d="M 3.890625 -0.78125 L 3.890625 0.109375 L 5.328125 0 L 5.328125 -0.3125 C 4.640625 -0.3125 4.5625 -0.375 4.5625 -0.875 L 4.5625 -4.40625 L 3.09375 -4.296875 L 3.09375 -3.984375 C 3.78125 -3.984375 3.875 -3.921875 3.875 -3.421875 L 3.875 -1.65625 C 3.875 -0.78125 3.390625 -0.109375 2.65625 -0.109375 C 1.828125 -0.109375 1.78125 -0.578125 1.78125 -1.09375 L 1.78125 -4.40625 L 0.3125 -4.296875 L 0.3125 -3.984375 C 1.09375 -3.984375 1.09375 -3.953125 1.09375 -3.078125 L 1.09375 -1.578125 C 1.09375 -0.796875 1.09375 0.109375 2.609375 0.109375 C 3.171875 0.109375 3.609375 -0.171875 3.890625 -0.78125 Z M 3.890625 -0.78125 "/>
</symbol>
<symbol overflow="visible" id="glyph0-7">
<path style="stroke:none;" d="M 1.71875 -3.75 L 1.71875 -4.40625 L 0.28125 -4.296875 L 0.28125 -3.984375 C 0.984375 -3.984375 1.0625 -3.921875 1.0625 -3.484375 L 1.0625 1.171875 C 1.0625 1.625 0.953125 1.625 0.28125 1.625 L 0.28125 1.9375 C 0.625 1.921875 1.140625 1.90625 1.390625 1.90625 C 1.671875 1.90625 2.171875 1.921875 2.515625 1.9375 L 2.515625 1.625 C 1.859375 1.625 1.75 1.625 1.75 1.171875 L 1.75 -0.59375 C 1.796875 -0.421875 2.21875 0.109375 2.96875 0.109375 C 4.15625 0.109375 5.1875 -0.875 5.1875 -2.15625 C 5.1875 -3.421875 4.234375 -4.40625 3.109375 -4.40625 C 2.328125 -4.40625 1.90625 -3.96875 1.71875 -3.75 Z M 1.75 -1.140625 L 1.75 -3.359375 C 2.03125 -3.875 2.515625 -4.15625 3.03125 -4.15625 C 3.765625 -4.15625 4.359375 -3.28125 4.359375 -2.15625 C 4.359375 -0.953125 3.671875 -0.109375 2.9375 -0.109375 C 2.53125 -0.109375 2.15625 -0.3125 1.890625 -0.71875 C 1.75 -0.921875 1.75 -0.9375 1.75 -1.140625 Z M 1.75 -1.140625 "/>
</symbol>
</g>
<clipPath id="clip1">
  <path d="M 0 39 L 117 39 L 117 143.808594 L 0 143.808594 Z M 0 39 "/>
</clipPath>
<clipPath id="clip2">
  <path d="M 27 115 L 59 115 L 59 143.808594 L 27 143.808594 Z M 27 115 "/>
</clipPath>
<clipPath id="clip3">
  <path d="M 87 115 L 119 115 L 119 143.808594 L 87 143.808594 Z M 87 115 "/>
</clipPath>
<clipPath id="clip4">
  <path d="M 175 81 L 263 81 L 263 143.808594 L 175 143.808594 Z M 175 81 "/>
</clipPath>
<clipPath id="clip5">
  <path d="M 173 115 L 205 115 L 205 143.808594 L 173 143.808594 Z M 173 115 "/>
</clipPath>
<clipPath id="clip6">
  <path d="M 233 115 L 265 115 L 265 143.808594 L 233 143.808594 Z M 233 115 "/>
</clipPath>
<clipPath id="clip7">
  <path d="M 408 74 L 434.726562 74 L 434.726562 102 L 408 102 Z M 408 74 "/>
</clipPath>
<clipPath id="clip8">
  <path d="M 378 117 L 405 117 L 405 143.808594 L 378 143.808594 Z M 378 117 "/>
</clipPath>
<clipPath id="clip9">
  <path d="M 318 117 L 346 117 L 346 143.808594 L 318 143.808594 Z M 318 117 "/>
</clipPath>
<clipPath id="clip10">
  <path d="M 316 115 L 348 115 L 348 143.808594 L 316 143.808594 Z M 316 115 "/>
</clipPath>
<clipPath id="clip11">
  <path d="M 376 115 L 407 115 L 407 143.808594 L 376 143.808594 Z M 376 115 "/>
</clipPath>
<clipPath id="clip12">
  <path d="M 406 72 L 434.726562 72 L 434.726562 104 L 406 104 Z M 406 72 "/>
</clipPath>
</defs>
<g id="surface1">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,79.998779%,79.998779%);fill-opacity:1;" d="M 119.550781 101.289062 L 146.015625 101.289062 L 146.015625 74.824219 L 119.550781 74.824219 Z M 119.550781 101.289062 "/>
<g clip-path="url(#clip1)" clip-rule="nonzero">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(79.998779%,79.998779%,100%);fill-opacity:1;" d="M 0 143.808594 L 116.128906 143.808594 L 116.128906 39.199219 L 0 39.199219 Z M 0 143.808594 "/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.816219 0.001375 C 2.816219 1.556063 1.5545 2.817781 -0.0001875 2.817781 C -1.554875 2.817781 -2.816594 1.556063 -2.816594 0.001375 C -2.816594 -1.557219 -1.554875 -2.818937 -0.0001875 -2.818937 C 1.5545 -2.818937 2.816219 -1.557219 2.816219 0.001375 Z M 2.816219 0.001375 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -27.0705 -42.522062 C -27.0705 -40.963469 -28.332219 -39.70175 -29.886906 -39.70175 C -31.4455 -39.70175 -32.707219 -40.963469 -32.707219 -42.522062 C -32.707219 -44.07675 -31.4455 -45.338469 -29.886906 -45.338469 C -28.332219 -45.338469 -27.0705 -44.07675 -27.0705 -42.522062 Z M -27.0705 -42.522062 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.734562 -2.467375 L -28.152531 -40.053312 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -50.062687 -85.041594 C -50.062687 -79.678312 -54.41425 -75.32675 -59.777531 -75.32675 C -65.140812 -75.32675 -69.488469 -79.678312 -69.488469 -85.041594 C -69.488469 -90.404875 -65.140812 -94.752531 -59.777531 -94.752531 C -54.41425 -94.752531 -50.062687 -90.404875 -50.062687 -85.041594 Z M -50.062687 -85.041594 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="7.144" y="91.46"/>
  <use xlink:href="#glyph0-2" x="14.33899" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -31.625187 -44.986906 L -54.078312 -76.932219 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.816219 -85.041594 C 2.816219 -83.483 1.5545 -82.221281 -0.0001875 -82.221281 C -1.554875 -82.221281 -2.816594 -83.483 -2.816594 -85.041594 C -2.816594 -86.596281 -1.554875 -87.858 -0.0001875 -87.858 C 1.5545 -87.858 2.816219 -86.596281 2.816219 -85.041594 Z M 2.816219 -85.041594 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -28.152531 -44.986906 L -1.734562 -82.572844 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<g clip-path="url(#clip2)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -20.175969 -127.561125 C -20.175969 -122.197844 -24.523625 -117.850187 -29.886906 -117.850187 C -35.254094 -117.850187 -39.60175 -122.197844 -39.60175 -127.561125 C -39.60175 -132.924406 -35.254094 -137.272062 -29.886906 -137.272062 C -24.523625 -137.272062 -20.175969 -132.924406 -20.175969 -127.561125 Z M -20.175969 -127.561125 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="37.032" y="133.98"/>
  <use xlink:href="#glyph0-3" x="44.22699" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.734562 -87.510344 L -24.187687 -119.45175 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<g clip-path="url(#clip3)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 39.601375 -127.561125 C 39.601375 -122.197844 35.253719 -117.850187 29.886531 -117.850187 C 24.52325 -117.850187 20.175594 -122.197844 20.175594 -127.561125 C 20.175594 -132.924406 24.52325 -137.272062 29.886531 -137.272062 C 35.253719 -137.272062 39.601375 -132.924406 39.601375 -127.561125 Z M 39.601375 -127.561125 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="96.807" y="133.98"/>
  <use xlink:href="#glyph0-4" x="104.00199" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.734188 -87.510344 L 24.187313 -119.45175 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.734188 -2.467375 L 29.886531 -42.522062 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 69.488094 -85.041594 C 69.488094 -79.678312 65.140438 -75.32675 59.777156 -75.32675 C 54.413875 -75.32675 50.066219 -79.678312 50.066219 -85.041594 C 50.066219 -90.404875 54.413875 -94.752531 59.777156 -94.752531 C 65.140438 -94.752531 69.488094 -90.404875 69.488094 -85.041594 Z M 69.488094 -85.041594 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="126.695" y="91.46"/>
  <use xlink:href="#glyph0-5" x="133.88999" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 29.886531 -42.522062 L 54.077938 -76.932219 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.79701;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 43.320125 -85.799406 L 45.640438 -85.693937 " transform="matrix(1,0,0,-1,73.008,3.017)"/>
<path style="fill:none;stroke-width:0.6376;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.552327 2.072587 C -1.424135 1.296232 -0.00000893605 0.131194 0.388863 -0.00027591 C -0.000444608 -0.130801 -1.42332 -1.29658 -1.553221 -2.072681 " transform="matrix(0.99887,-0.04639,-0.04639,-0.99887,118.64672,88.70917)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="102.817" y="89.922"/>
  <use xlink:href="#glyph0-7" x="108.352221" y="89.922"/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,79.998779%,79.998779%);fill-opacity:1;" d="M 265.566406 101.289062 L 292.03125 101.289062 L 292.03125 74.824219 L 265.566406 74.824219 Z M 265.566406 101.289062 "/>
<g clip-path="url(#clip4)" clip-rule="nonzero">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,79.998779%,79.998779%);fill-opacity:1;" d="M 175.902344 143.808594 L 262.144531 143.808594 L 262.144531 81.71875 L 175.902344 81.71875 Z M 175.902344 143.808594 "/>
</g>
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(79.998779%,79.998779%,100%);fill-opacity:1;" d="M 146.015625 101.289062 L 172.476562 101.289062 L 172.476562 74.824219 L 146.015625 74.824219 Z M 146.015625 101.289062 "/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.816844 0.001375 C 2.816844 1.556063 1.555125 2.817781 0.0004375 2.817781 C -1.558156 2.817781 -2.819875 1.556063 -2.819875 0.001375 C -2.819875 -1.557219 -1.558156 -2.818937 0.0004375 -2.818937 C 1.555125 -2.818937 2.816844 -1.557219 2.816844 0.001375 Z M 2.816844 0.001375 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -27.069875 -42.522062 C -27.069875 -40.963469 -28.331594 -39.70175 -29.890187 -39.70175 C -31.444875 -39.70175 -32.706594 -40.963469 -32.706594 -42.522062 C -32.706594 -44.07675 -31.444875 -45.338469 -29.890187 -45.338469 C -28.331594 -45.338469 -27.069875 -44.07675 -27.069875 -42.522062 Z M -27.069875 -42.522062 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.733937 -2.467375 L -28.151906 -40.053312 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -50.065969 -85.041594 C -50.065969 -79.678312 -54.413625 -75.32675 -59.776906 -75.32675 C -65.140187 -75.32675 -69.487844 -79.678312 -69.487844 -85.041594 C -69.487844 -90.404875 -65.140187 -94.752531 -59.776906 -94.752531 C -54.413625 -94.752531 -50.065969 -90.404875 -50.065969 -85.041594 Z M -50.065969 -85.041594 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="153.159" y="91.46"/>
  <use xlink:href="#glyph0-2" x="160.35399" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -31.624562 -44.986906 L -54.077687 -76.932219 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.816844 -85.041594 C 2.816844 -83.483 1.555125 -82.221281 0.0004375 -82.221281 C -1.558156 -82.221281 -2.819875 -83.483 -2.819875 -85.041594 C -2.819875 -86.596281 -1.558156 -87.858 0.0004375 -87.858 C 1.555125 -87.858 2.816844 -86.596281 2.816844 -85.041594 Z M 2.816844 -85.041594 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -28.151906 -44.986906 L -1.733937 -82.572844 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<g clip-path="url(#clip5)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -20.175344 -127.561125 C -20.175344 -122.197844 -24.523 -117.850187 -29.890187 -117.850187 C -35.253469 -117.850187 -39.601125 -122.197844 -39.601125 -127.561125 C -39.601125 -132.924406 -35.253469 -137.272062 -29.890187 -137.272062 C -24.523 -137.272062 -20.175344 -132.924406 -20.175344 -127.561125 Z M -20.175344 -127.561125 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="183.047" y="133.98"/>
  <use xlink:href="#glyph0-3" x="190.24199" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.733937 -87.510344 L -24.187062 -119.45175 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<g clip-path="url(#clip6)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 39.602 -127.561125 C 39.602 -122.197844 35.250438 -117.850187 29.887156 -117.850187 C 24.523875 -117.850187 20.176219 -122.197844 20.176219 -127.561125 C 20.176219 -132.924406 24.523875 -137.272062 29.887156 -137.272062 C 35.250438 -137.272062 39.602 -132.924406 39.602 -127.561125 Z M 39.602 -127.561125 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="242.823" y="133.98"/>
  <use xlink:href="#glyph0-4" x="250.01799" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.734813 -87.510344 L 24.187938 -119.45175 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.734813 -2.467375 L 29.887156 -42.522062 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 69.488719 -85.041594 C 69.488719 -79.678312 65.141063 -75.32675 59.777781 -75.32675 C 54.4145 -75.32675 50.062938 -79.678312 50.062938 -85.041594 C 50.062938 -90.404875 54.4145 -94.752531 59.777781 -94.752531 C 65.141063 -94.752531 69.488719 -90.404875 69.488719 -85.041594 Z M 69.488719 -85.041594 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="272.711" y="91.46"/>
  <use xlink:href="#glyph0-5" x="279.90599" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 29.887156 -42.522062 L 54.078563 -76.932219 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.79701;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 43.121531 -91.936125 L 45.691844 -90.861906 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.6376;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.554254 2.070567 C -1.423894 1.296771 -0.00201697 0.128622 0.388623 -0.000321229 C 0.0000671968 -0.130409 -1.424088 -1.293236 -1.554262 -2.072723 " transform="matrix(0.92299,-0.38466,-0.38466,-0.92299,264.7154,93.87966)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="248.733" y="95.455"/>
  <use xlink:href="#glyph0-7" x="254.268221" y="95.455"/>
</g>
<path style="fill:none;stroke-width:0.79701;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -46.347219 -90.592375 L -43.773 -91.658781 " transform="matrix(1,0,0,-1,219.023,3.017)"/>
<path style="fill:none;stroke-width:0.6376;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.554137 2.070922 C -1.42365 1.296234 -0.00217825 0.130104 0.389688 0.00061129 C 0.000241977 -0.131038 -1.424347 -1.295903 -1.555003 -2.073012 " transform="matrix(0.92387,0.38252,0.38252,-0.92387,175.24912,94.67572)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="168.756" y="104.942"/>
  <use xlink:href="#glyph0-7" x="174.291221" y="104.942"/>
</g>
<g clip-path="url(#clip7)" clip-rule="nonzero">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,79.998779%,79.998779%);fill-opacity:1;" d="M 408.261719 101.289062 L 434.726562 101.289062 L 434.726562 74.824219 L 408.261719 74.824219 Z M 408.261719 101.289062 "/>
</g>
<g clip-path="url(#clip8)" clip-rule="nonzero">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(100%,79.998779%,79.998779%);fill-opacity:1;" d="M 378.375 143.808594 L 404.839844 143.808594 L 404.839844 117.34375 L 378.375 117.34375 Z M 378.375 143.808594 "/>
</g>
<g clip-path="url(#clip9)" clip-rule="nonzero">
<path style=" stroke:none;fill-rule:nonzero;fill:rgb(79.998779%,79.998779%,100%);fill-opacity:1;" d="M 318.597656 143.808594 L 345.0625 143.808594 L 345.0625 117.34375 L 318.597656 117.34375 Z M 318.597656 143.808594 "/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.817156 0.001375 C 2.817156 1.556063 1.555438 2.817781 0.00075 2.817781 C -1.557844 2.817781 -2.819562 1.556063 -2.819562 0.001375 C -2.819562 -1.557219 -1.557844 -2.818937 0.00075 -2.818937 C 1.555438 -2.818937 2.817156 -1.557219 2.817156 0.001375 Z M 2.817156 0.001375 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -27.069562 -42.522062 C -27.069562 -40.963469 -28.331281 -39.70175 -29.889875 -39.70175 C -31.444562 -39.70175 -32.706281 -40.963469 -32.706281 -42.522062 C -32.706281 -44.07675 -31.444562 -45.338469 -29.889875 -45.338469 C -28.331281 -45.338469 -27.069562 -44.07675 -27.069562 -42.522062 Z M -27.069562 -42.522062 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.733625 -2.467375 L -28.151594 -40.053312 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -50.065656 -85.041594 C -50.065656 -79.678312 -54.413312 -75.32675 -59.776594 -75.32675 C -65.139875 -75.32675 -69.487531 -79.678312 -69.487531 -85.041594 C -69.487531 -90.404875 -65.139875 -94.752531 -59.776594 -94.752531 C -54.413312 -94.752531 -50.065656 -90.404875 -50.065656 -85.041594 Z M -50.065656 -85.041594 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="295.854" y="91.46"/>
  <use xlink:href="#glyph0-2" x="303.04899" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -31.62425 -44.986906 L -54.077375 -76.932219 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 2.817156 -85.041594 C 2.817156 -83.483 1.555438 -82.221281 0.00075 -82.221281 C -1.557844 -82.221281 -2.819562 -83.483 -2.819562 -85.041594 C -2.819562 -86.596281 -1.557844 -87.858 0.00075 -87.858 C 1.555438 -87.858 2.817156 -86.596281 2.817156 -85.041594 Z M 2.817156 -85.041594 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -28.151594 -44.986906 L -1.733625 -82.572844 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<g clip-path="url(#clip10)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -20.175031 -127.561125 C -20.175031 -122.197844 -24.522687 -117.850187 -29.889875 -117.850187 C -35.253156 -117.850187 -39.600812 -122.197844 -39.600812 -127.561125 C -39.600812 -132.924406 -35.253156 -137.272062 -29.889875 -137.272062 C -24.522687 -137.272062 -20.175031 -132.924406 -20.175031 -127.561125 Z M -20.175031 -127.561125 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="325.742" y="133.98"/>
  <use xlink:href="#glyph0-3" x="332.93699" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.733625 -87.510344 L -24.190656 -119.45175 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<g clip-path="url(#clip11)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 39.602313 -127.561125 C 39.602313 -122.197844 35.25075 -117.850187 29.887469 -117.850187 C 24.524188 -117.850187 20.176531 -122.197844 20.176531 -127.561125 C 20.176531 -132.924406 24.524188 -137.272062 29.887469 -137.272062 C 35.25075 -137.272062 39.602313 -132.924406 39.602313 -127.561125 Z M 39.602313 -127.561125 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="385.518" y="133.98"/>
  <use xlink:href="#glyph0-4" x="392.71299" y="133.98"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.735125 -87.510344 L 24.18825 -119.45175 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 1.735125 -2.467375 L 29.887469 -42.522062 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<g clip-path="url(#clip12)" clip-rule="nonzero">
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 69.489031 -85.041594 C 69.489031 -79.678312 65.141375 -75.32675 59.778094 -75.32675 C 54.410906 -75.32675 50.06325 -79.678312 50.06325 -85.041594 C 50.06325 -90.404875 54.410906 -94.752531 59.778094 -94.752531 C 65.141375 -94.752531 69.489031 -90.404875 69.489031 -85.041594 Z M 69.489031 -85.041594 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
</g>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-1" x="415.406" y="91.46"/>
  <use xlink:href="#glyph0-5" x="422.60099" y="91.46"/>
</g>
<path style="fill:none;stroke-width:0.3985;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 29.887469 -42.522062 L 54.078875 -76.932219 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.79701;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 39.18825 -114.32675 L 49.930438 -99.049406 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.6376;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.554741 2.069993 C -1.426036 1.296136 -0.0000829577 0.13079 0.389027 -0.00150586 C 0.00207638 -0.130343 -1.423367 -1.295075 -1.555794 -2.071091 " transform="matrix(0.5752,-0.81798,-0.81798,-0.5752,411.65,102.06735)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="391.458" y="110.592"/>
  <use xlink:href="#glyph0-7" x="396.993221" y="110.592"/>
</g>
<path style="fill:none;stroke-width:0.79701;stroke-linecap:butt;stroke-linejoin:miter;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -16.456281 -127.561125 L 15.949969 -127.561125 " transform="matrix(1,0,0,-1,361.718,3.017)"/>
<path style="fill:none;stroke-width:0.6376;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M -1.553589 2.073744 C -1.424682 1.2964 0.00109875 0.128431 0.387818 -0.000475 C 0.00109875 -0.129381 -1.424682 -1.293444 -1.553589 -2.070787 " transform="matrix(1,0,0,-1,377.66687,130.57765)"/>
<g style="fill:rgb(0%,0%,0%);fill-opacity:1;">
  <use xlink:href="#glyph0-6" x="356.283" y="122.264"/>
  <use xlink:href="#glyph0-7" x="361.818221" y="122.264"/>
</g>
</g>
</svg>
