adapter_khz     10000

interface remote_bitbang
remote_bitbang_host localhost
#$::env(REMOTE_BITBANG_HOST)
remote_bitbang_port $::env(JTAG_VPI_PORT)

set _CHIPNAME riscv
jtag newtap $_CHIPNAME cpu -irlen 5

set _TARGETNAME $_CHIPNAME.cpu
target create $_TARGETNAME riscv -chain-position $_TARGETNAME -rtos hwthread

riscv set_reset_timeout_sec 120
riscv set_command_timeout_sec 120

riscv set_prefer_sba $::env(JTAG_DTM_ENABLE_SBA)

init
halt
echo "Ready for Remote Connections"
