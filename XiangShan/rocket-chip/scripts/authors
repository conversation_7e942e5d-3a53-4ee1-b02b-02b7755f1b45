#! /bin/sh

while read when who; do
case "$who" in
"<PERSON>")   echo <PERSON> ;;
"<PERSON>")      echo <PERSON> ;;
"<PERSON>")          if [ $when -ge 20151101 ]; then echo SiFive; else echo <PERSON>; fi ;;
"<PERSON><PERSON>")  echo Unknown  ;;
"<PERSON>")    if [ $when -ge 20150901 ]; then echo <PERSON>ive; else echo <PERSON>; fi ;;
"<PERSON>")         echo <PERSON> ;;
"<PERSON>")  echo <PERSON> ;;
"<PERSON>")      echo Berkeley ;;
"Daiwei Li")          echo Berkeley ;;
"<PERSON>")    echo <PERSON> ;;
"Donggyu")            echo Berkeley ;;
"<PERSON>gy<PERSON> Kim")        echo <PERSON> ;;
"ducky")              echo <PERSON> ;;
"<PERSON>")          echo <PERSON> ;;
"<PERSON>")        echo <PERSON> ;;
"<PERSON>")         if [ $when -ge 20160501 ]; then echo SiFive; else echo <PERSON>; fi ;;
"<PERSON>")         if [ $when -ge 20160601 -a $when -le 20160819 ]; then echo SiFive; else echo <PERSON>; fi ;;
"<PERSON>y Vo")             echo <PERSON> ;;
"<PERSON><PERSON> YON<PERSON>")        echo Unknown  ;;
"jackkoenig")         echo <PERSON> ;;
"<PERSON>")        echo <PERSON> ;;
"<PERSON>")        echo <PERSON>Five   ;;
"<PERSON>")         echo <PERSON> ;;
"<PERSON>")        echo <PERSON> ;;
"<PERSON>")       echo <PERSON>;;
"<PERSON>")     if [ $when -le 20161127 ]; then echo <PERSON>; else echo Cambridge; fi ;;
"Megan Wachs")        echo SiFive   ;;
"Miquel Moreto")      echo Berkeley ;;
"mwachs5")            echo SiFive   ;;
"Palmer Dabbelt")     echo Berkeley ;;
"Quan Nguyen")        echo Berkeley ;;
"RainerWasserfuhr")   echo Unknown  ;;
"Richard Xia")        echo SiFive   ;;
"Rimas Avizienis")    echo Berkeley ;;
"roman3017")          echo Unknown  ;;
"Sagar Karandikar")   echo Berkeley ;;
"Schuyler Eldridge")  echo Unknown  ;;
"Scott Beamer")       echo Berkeley ;;
"Scott Johnson")      echo SiFive   ;;
"SeungRyeol Lee")     echo LGE      ;;
"Stephen Twigg")      echo Berkeley ;;
"Wei Song")           echo Cambridge;;
"Wesley W. Terpstra") echo SiFive   ;;
"Yunsup Lee")         if [ $when -gt 20150901 ]; then echo SiFive; else echo Berkeley; fi ;;
*)                    echo NoMatch; echo "Missing scripts/authors entry for $who" >&2; exit 1 ;;
esac
done
