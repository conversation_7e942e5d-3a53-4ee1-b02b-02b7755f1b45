{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1689068808, "narHash": "sha256-6ixXo3wt24N/melDWjq70UuHQLxGV8jZvooRanIHXw0=", "owner": "numtide", "repo": "flake-utils", "rev": "919d646de7be200f3bf08cb76ae1f09402b6f9b4", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1691186842, "narHash": "sha256-wxBVCvZUwq+XS4N4t9NqsHV4E64cPVqQ2fdDISpjcw0=", "owner": "NixOS", "repo": "nixpkgs", "rev": "18036c0be90f4e308ae3ebcab0e14aae0336fe42", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}