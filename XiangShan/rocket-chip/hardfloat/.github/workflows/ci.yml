name: CI
on:
  pull_request:

jobs:
  fma-test:
    name: Use testfloat to test hardfloat
    runs-on: ubuntu-latest
    strategy:
      matrix:
        chisel: ["3.5.6", "3.6.0", "5.0.0"]

    steps:
      - uses: actions/checkout@v2
        with:
          submodules: 'true'
      - uses: cachix/install-nix-action@v19
        with:
          install_url: https://releases.nixos.org/nix/nix-2.13.3/install
          nix_path: nixpkgs=channel:nixos-unstable
      - name: run test
        run: |
          nix --experimental-features 'nix-command flakes' develop -c mill -j 0 'hardfloatdut[${{ matrix.chisel }}].test'

  sbt-test:
    name: Use testfloat to test hardfloat (with SBT)
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            java: 8
          - os: ubuntu-latest
            java: 11
          - os: ubuntu-latest
            java: 17
    runs-on: ${{ matrix.os }}
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Setup JDK
      uses: actions/setup-java@v3
      with:
        distribution: temurin
        java-version: ${{ matrix.java }}
        cache: sbt
    - name: Install Verilator
      run: sudo apt-get install -y verilator gcc
    - name: Build and test
      shell: bash
      run: make test