// See LICENSE.SiFive for license details.
// See LICENSE.Berkeley for license details.

package freechips.rocketchip.rocket

import chisel3.util._

/* make EXTENSIONS="rv_* rv64*" inst.chisel */

/* Automatically generated by parse_opcodes */
object Instructions {
  def ADD                = BitPat("b0000000??????????000?????0110011")
  def ADD_UW             = BitPat("b0000100??????????000?????0111011")
  def ADDI               = BitPat("b?????????????????000?????0010011")
  def ADDIW              = BitPat("b?????????????????000?????0011011")
  def ADDW               = BitPat("b0000000??????????000?????0111011")
  def AES64DS            = BitPat("b0011101??????????000?????0110011")
  def AES64DSM           = BitPat("b0011111??????????000?????0110011")
  def AES64ES            = BitPat("b0011001??????????000?????0110011")
  def AES64ESM           = BitPat("b0011011??????????000?????0110011")
  def AES64IM            = BitPat("b001100000000?????001?????0010011")
  def AES64KS1I          = BitPat("b00110001?????????001?????0010011")
  def AES64KS2           = BitPat("b0111111??????????000?????0110011")
  def AMOADD_D           = BitPat("b00000????????????011?????0101111")
  def AMOADD_W           = BitPat("b00000????????????010?????0101111")
  def AMOAND_D           = BitPat("b01100????????????011?????0101111")
  def AMOAND_W           = BitPat("b01100????????????010?????0101111")
  def AMOMAX_D           = BitPat("b10100????????????011?????0101111")
  def AMOMAX_W           = BitPat("b10100????????????010?????0101111")
  def AMOMAXU_D          = BitPat("b11100????????????011?????0101111")
  def AMOMAXU_W          = BitPat("b11100????????????010?????0101111")
  def AMOMIN_D           = BitPat("b10000????????????011?????0101111")
  def AMOMIN_W           = BitPat("b10000????????????010?????0101111")
  def AMOMINU_D          = BitPat("b11000????????????011?????0101111")
  def AMOMINU_W          = BitPat("b11000????????????010?????0101111")
  def AMOOR_D            = BitPat("b01000????????????011?????0101111")
  def AMOOR_W            = BitPat("b01000????????????010?????0101111")
  def AMOSWAP_D          = BitPat("b00001????????????011?????0101111")
  def AMOSWAP_W          = BitPat("b00001????????????010?????0101111")
  def AMOXOR_D           = BitPat("b00100????????????011?????0101111")
  def AMOXOR_W           = BitPat("b00100????????????010?????0101111")
  def AMOCAS_Q           = BitPat("b00101????????????100?????0101111")
  def AMOCAS_D           = BitPat("b00101????????????011?????0101111")
  def AMOCAS_W           = BitPat("b00101????????????010?????0101111")
  def AND                = BitPat("b0000000??????????111?????0110011")
  def ANDI               = BitPat("b?????????????????111?????0010011")
  def ANDN               = BitPat("b0100000??????????111?????0110011")
  def AUIPC              = BitPat("b?????????????????????????0010111")
  def BCLR               = BitPat("b0100100??????????001?????0110011")
  def BCLRI              = BitPat("b010010???????????001?????0010011")
  def BEQ                = BitPat("b?????????????????000?????1100011")
  def BEXT               = BitPat("b0100100??????????101?????0110011")
  def BEXTI              = BitPat("b010010???????????101?????0010011")
  def BGE                = BitPat("b?????????????????101?????1100011")
  def BGEU               = BitPat("b?????????????????111?????1100011")
  def BINV               = BitPat("b0110100??????????001?????0110011")
  def BINVI              = BitPat("b011010???????????001?????0010011")
  def BLT                = BitPat("b?????????????????100?????1100011")
  def BLTU               = BitPat("b?????????????????110?????1100011")
  def BNE                = BitPat("b?????????????????001?????1100011")
  def BREV8              = BitPat("b011010000111?????101?????0010011")
  def BSET               = BitPat("b0010100??????????001?????0110011")
  def BSETI              = BitPat("b001010???????????001?????0010011")
  def C_ADD              = BitPat("b????????????????1001??????????10")
  def C_ADDI             = BitPat("b????????????????000???????????01")
  def C_ADDI16SP         = BitPat("b????????????????011?00010?????01")
  def C_ADDI4SPN         = BitPat("b????????????????000???????????00")
  def C_ADDIW            = BitPat("b????????????????001???????????01")
  def C_ADDW             = BitPat("b????????????????100111???01???01")
  def C_AND              = BitPat("b????????????????100011???11???01")
  def C_ANDI             = BitPat("b????????????????100?10????????01")
  def C_BEQZ             = BitPat("b????????????????110???????????01")
  def C_BNEZ             = BitPat("b????????????????111???????????01")
  def C_EBREAK           = BitPat("b????????????????1001000000000010")
  def C_FLD              = BitPat("b????????????????001???????????00")
  def C_FLDSP            = BitPat("b????????????????001???????????10")
  def C_FSD              = BitPat("b????????????????101???????????00")
  def C_FSDSP            = BitPat("b????????????????101???????????10")
  def C_J                = BitPat("b????????????????101???????????01")
  def C_JALR             = BitPat("b????????????????1001?????0000010")
  def C_JR               = BitPat("b????????????????1000?????0000010")
  def C_LD               = BitPat("b????????????????011???????????00")
  def C_LDSP             = BitPat("b????????????????011???????????10")
  def C_LI               = BitPat("b????????????????010???????????01")
  def C_LUI              = BitPat("b????????????????011???????????01")
  def C_LW               = BitPat("b????????????????010???????????00")
  def C_LWSP             = BitPat("b????????????????010???????????10")
  def C_MV               = BitPat("b????????????????1000??????????10")
  def C_NOP              = BitPat("b????????????????000?00000?????01")
  def C_OR               = BitPat("b????????????????100011???10???01")
  def C_SD               = BitPat("b????????????????111???????????00")
  def C_SDSP             = BitPat("b????????????????111???????????10")
  def C_SLLI             = BitPat("b????????????????000???????????10")
  def C_SRAI             = BitPat("b????????????????100?01????????01")
  def C_SRLI             = BitPat("b????????????????100?00????????01")
  def C_SUB              = BitPat("b????????????????100011???00???01")
  def C_SUBW             = BitPat("b????????????????100111???00???01")
  def C_SW               = BitPat("b????????????????110???????????00")
  def C_SWSP             = BitPat("b????????????????110???????????10")
  def C_XOR              = BitPat("b????????????????100011???01???01")
  def CBO_CLEAN          = BitPat("b000000000001?????010000000001111")
  def CBO_FLUSH          = BitPat("b000000000010?????010000000001111")
  def CBO_INVAL          = BitPat("b000000000000?????010000000001111")
  def CBO_ZERO           = BitPat("b000000000100?????010000000001111")
  def CLMUL              = BitPat("b0000101??????????001?????0110011")
  def CLMULH             = BitPat("b0000101??????????011?????0110011")
  def CLMULR             = BitPat("b0000101??????????010?????0110011")
  def CLZ                = BitPat("b011000000000?????001?????0010011")
  def CLZW               = BitPat("b011000000000?????001?????0011011")
  def CPOP               = BitPat("b011000000010?????001?????0010011")
  def CPOPW              = BitPat("b011000000010?????001?????0011011")
  def CSRRC              = BitPat("b?????????????????011?????1110011")
  def CSRRCI             = BitPat("b?????????????????111?????1110011")
  def CSRRS              = BitPat("b?????????????????010?????1110011")
  def CSRRSI             = BitPat("b?????????????????110?????1110011")
  def CSRRW              = BitPat("b?????????????????001?????1110011")
  def CSRRWI             = BitPat("b?????????????????101?????1110011")
  def CZERO_EQZ          = BitPat("b0000111??????????101?????0110011")
  def CZERO_NEZ          = BitPat("b0000111??????????111?????0110011")
  def CTZ                = BitPat("b011000000001?????001?????0010011")
  def CTZW               = BitPat("b011000000001?????001?????0011011")
  def DIV                = BitPat("b0000001??????????100?????0110011")
  def DIVU               = BitPat("b0000001??????????101?????0110011")
  def DIVUW              = BitPat("b0000001??????????101?????0111011")
  def DIVW               = BitPat("b0000001??????????100?????0111011")
  def DRET               = BitPat("b01111011001000000000000001110011")
  def EBREAK             = BitPat("b00000000000100000000000001110011")
  def ECALL              = BitPat("b00000000000000000000000001110011")
  def FADD_D             = BitPat("b0000001??????????????????1010011")
  def FADD_H             = BitPat("b0000010??????????????????1010011")
  def FADD_Q             = BitPat("b0000011??????????????????1010011")
  def FADD_S             = BitPat("b0000000??????????????????1010011")
  def FCLASS_D           = BitPat("b111000100000?????001?????1010011")
  def FCLASS_H           = BitPat("b111001000000?????001?????1010011")
  def FCLASS_Q           = BitPat("b111001100000?????001?????1010011")
  def FCLASS_S           = BitPat("b111000000000?????001?????1010011")
  def FCVT_D_H           = BitPat("b010000100010?????????????1010011")
  def FCVT_D_L           = BitPat("b110100100010?????????????1010011")
  def FCVT_D_LU          = BitPat("b110100100011?????????????1010011")
  def FCVT_D_Q           = BitPat("b010000100011?????????????1010011")
  def FCVT_D_S           = BitPat("b010000100000?????????????1010011")
  def FCVT_D_W           = BitPat("b110100100000?????????????1010011")
  def FCVT_D_WU          = BitPat("b110100100001?????????????1010011")
  def FCVT_H_D           = BitPat("b010001000001?????????????1010011")
  def FCVT_H_L           = BitPat("b110101000010?????????????1010011")
  def FCVT_H_LU          = BitPat("b110101000011?????????????1010011")
  def FCVT_H_Q           = BitPat("b010001000011?????????????1010011")
  def FCVT_H_S           = BitPat("b010001000000?????????????1010011")
  def FCVT_H_W           = BitPat("b110101000000?????????????1010011")
  def FCVT_H_WU          = BitPat("b110101000001?????????????1010011")
  def FCVT_L_D           = BitPat("b110000100010?????????????1010011")
  def FCVT_L_H           = BitPat("b110001000010?????????????1010011")
  def FCVT_L_Q           = BitPat("b110001100010?????????????1010011")
  def FCVT_L_S           = BitPat("b110000000010?????????????1010011")
  def FCVT_LU_D          = BitPat("b110000100011?????????????1010011")
  def FCVT_LU_H          = BitPat("b110001000011?????????????1010011")
  def FCVT_LU_Q          = BitPat("b110001100011?????????????1010011")
  def FCVT_LU_S          = BitPat("b110000000011?????????????1010011")
  def FCVT_Q_D           = BitPat("b010001100001?????????????1010011")
  def FCVT_Q_H           = BitPat("b010001100010?????????????1010011")
  def FCVT_Q_L           = BitPat("b110101100010?????????????1010011")
  def FCVT_Q_LU          = BitPat("b110101100011?????????????1010011")
  def FCVT_Q_S           = BitPat("b010001100000?????????????1010011")
  def FCVT_Q_W           = BitPat("b110101100000?????????????1010011")
  def FCVT_Q_WU          = BitPat("b110101100001?????????????1010011")
  def FCVT_S_D           = BitPat("b010000000001?????????????1010011")
  def FCVT_S_H           = BitPat("b010000000010?????????????1010011")
  def FCVT_S_L           = BitPat("b110100000010?????????????1010011")
  def FCVT_S_LU          = BitPat("b110100000011?????????????1010011")
  def FCVT_S_Q           = BitPat("b010000000011?????????????1010011")
  def FCVT_S_W           = BitPat("b110100000000?????????????1010011")
  def FCVT_S_WU          = BitPat("b110100000001?????????????1010011")
  def FCVT_W_D           = BitPat("b110000100000?????????????1010011")
  def FCVT_W_H           = BitPat("b110001000000?????????????1010011")
  def FCVT_W_Q           = BitPat("b110001100000?????????????1010011")
  def FCVT_W_S           = BitPat("b110000000000?????????????1010011")
  def FCVT_WU_D          = BitPat("b110000100001?????????????1010011")
  def FCVT_WU_H          = BitPat("b110001000001?????????????1010011")
  def FCVT_WU_Q          = BitPat("b110001100001?????????????1010011")
  def FCVT_WU_S          = BitPat("b110000000001?????????????1010011")
  def FROUND_S           = BitPat("b010000000100?????????????1010011")
  def FROUND_D           = BitPat("b010000100100?????????????1010011")
  def FROUND_H           = BitPat("b010001000100?????????????1010011")
  def FROUNDNX_S         = BitPat("b010000000101?????????????1010011")
  def FROUNDNX_D         = BitPat("b010000100101?????????????1010011")
  def FROUNDNX_H         = BitPat("b010001000101?????????????1010011")
  def FCVTMOD_W_D        = BitPat("b110000101000?????001?????1010011")
  def FDIV_D             = BitPat("b0001101??????????????????1010011")
  def FDIV_H             = BitPat("b0001110??????????????????1010011")
  def FDIV_Q             = BitPat("b0001111??????????????????1010011")
  def FDIV_S             = BitPat("b0001100??????????????????1010011")
  def FENCE              = BitPat("b?????????????????000?????0001111")
  def FENCE_I            = BitPat("b?????????????????001?????0001111")
  def FEQ_D              = BitPat("b1010001??????????010?????1010011")
  def FEQ_H              = BitPat("b1010010??????????010?????1010011")
  def FEQ_Q              = BitPat("b1010011??????????010?????1010011")
  def FEQ_S              = BitPat("b1010000??????????010?????1010011")
  def FLD                = BitPat("b?????????????????011?????0000111")
  def FLE_D              = BitPat("b1010001??????????000?????1010011")
  def FLE_H              = BitPat("b1010010??????????000?????1010011")
  def FLE_Q              = BitPat("b1010011??????????000?????1010011")
  def FLE_S              = BitPat("b1010000??????????000?????1010011")
  def FLH                = BitPat("b?????????????????001?????0000111")
  def FLQ                = BitPat("b?????????????????100?????0000111")
  def FLT_D              = BitPat("b1010001??????????001?????1010011")
  def FLT_H              = BitPat("b1010010??????????001?????1010011")
  def FLT_Q              = BitPat("b1010011??????????001?????1010011")
  def FLT_S              = BitPat("b1010000??????????001?????1010011")
  def FLW                = BitPat("b?????????????????010?????0000111")
  def FLEQ_S             = BitPat("b1010000??????????100?????1010011")
  def FLEQ_D             = BitPat("b1010001??????????100?????1010011")
  def FLEQ_H             = BitPat("b1010010??????????100?????1010011")
  def FLTQ_S             = BitPat("b1010000??????????101?????1010011")
  def FLTQ_D             = BitPat("b1010001??????????101?????1010011")
  def FLTQ_H             = BitPat("b1010010??????????101?????1010011")
  def FMADD_D            = BitPat("b?????01??????????????????1000011")
  def FMADD_H            = BitPat("b?????10??????????????????1000011")
  def FMADD_Q            = BitPat("b?????11??????????????????1000011")
  def FMADD_S            = BitPat("b?????00??????????????????1000011")
  def FMAX_D             = BitPat("b0010101??????????001?????1010011")
  def FMAX_H             = BitPat("b0010110??????????001?????1010011")
  def FMAX_Q             = BitPat("b0010111??????????001?????1010011")
  def FMAX_S             = BitPat("b0010100??????????001?????1010011")
  def FMIN_D             = BitPat("b0010101??????????000?????1010011")
  def FMIN_H             = BitPat("b0010110??????????000?????1010011")
  def FMIN_Q             = BitPat("b0010111??????????000?????1010011")
  def FMIN_S             = BitPat("b0010100??????????000?????1010011")
  def FMAXM_S            = BitPat("b0010100??????????011?????1010011")
  def FMAXM_D            = BitPat("b0010101??????????011?????1010011")
  def FMAXM_H            = BitPat("b0010110??????????011?????1010011")
  def FMINM_S            = BitPat("b0010100??????????010?????1010011")
  def FMINM_D            = BitPat("b0010101??????????010?????1010011")
  def FMINM_H            = BitPat("b0010110??????????010?????1010011")
  def FMSUB_D            = BitPat("b?????01??????????????????1000111")
  def FMSUB_H            = BitPat("b?????10??????????????????1000111")
  def FMSUB_Q            = BitPat("b?????11??????????????????1000111")
  def FMSUB_S            = BitPat("b?????00??????????????????1000111")
  def FMUL_D             = BitPat("b0001001??????????????????1010011")
  def FMUL_H             = BitPat("b0001010??????????????????1010011")
  def FMUL_Q             = BitPat("b0001011??????????????????1010011")
  def FMUL_S             = BitPat("b0001000??????????????????1010011")
  def FMV_D_X            = BitPat("b111100100000?????000?????1010011")
  def FMV_H_X            = BitPat("b111101000000?????000?????1010011")
  def FMV_W_X            = BitPat("b111100000000?????000?????1010011")
  def FMV_X_D            = BitPat("b111000100000?????000?????1010011")
  def FMV_X_H            = BitPat("b111001000000?????000?????1010011")
  def FMV_X_W            = BitPat("b111000000000?????000?????1010011")
  def FLI_S              = BitPat("b111100000001?????000?????1010011")
  def FLI_D              = BitPat("b111100100001?????000?????1010011")
  def FLI_H              = BitPat("b111101000001?????000?????1010011")
  def FNMADD_D           = BitPat("b?????01??????????????????1001111")
  def FNMADD_H           = BitPat("b?????10??????????????????1001111")
  def FNMADD_Q           = BitPat("b?????11??????????????????1001111")
  def FNMADD_S           = BitPat("b?????00??????????????????1001111")
  def FNMSUB_D           = BitPat("b?????01??????????????????1001011")
  def FNMSUB_H           = BitPat("b?????10??????????????????1001011")
  def FNMSUB_Q           = BitPat("b?????11??????????????????1001011")
  def FNMSUB_S           = BitPat("b?????00??????????????????1001011")
  def FSD                = BitPat("b?????????????????011?????0100111")
  def FSGNJ_D            = BitPat("b0010001??????????000?????1010011")
  def FSGNJ_H            = BitPat("b0010010??????????000?????1010011")
  def FSGNJ_Q            = BitPat("b0010011??????????000?????1010011")
  def FSGNJ_S            = BitPat("b0010000??????????000?????1010011")
  def FSGNJN_D           = BitPat("b0010001??????????001?????1010011")
  def FSGNJN_H           = BitPat("b0010010??????????001?????1010011")
  def FSGNJN_Q           = BitPat("b0010011??????????001?????1010011")
  def FSGNJN_S           = BitPat("b0010000??????????001?????1010011")
  def FSGNJX_D           = BitPat("b0010001??????????010?????1010011")
  def FSGNJX_H           = BitPat("b0010010??????????010?????1010011")
  def FSGNJX_Q           = BitPat("b0010011??????????010?????1010011")
  def FSGNJX_S           = BitPat("b0010000??????????010?????1010011")
  def FSH                = BitPat("b?????????????????001?????0100111")
  def FSQ                = BitPat("b?????????????????100?????0100111")
  def FSQRT_D            = BitPat("b010110100000?????????????1010011")
  def FSQRT_H            = BitPat("b010111000000?????????????1010011")
  def FSQRT_Q            = BitPat("b010111100000?????????????1010011")
  def FSQRT_S            = BitPat("b010110000000?????????????1010011")
  def FSUB_D             = BitPat("b0000101??????????????????1010011")
  def FSUB_H             = BitPat("b0000110??????????????????1010011")
  def FSUB_Q             = BitPat("b0000111??????????????????1010011")
  def FSUB_S             = BitPat("b0000100??????????????????1010011")
  def FSW                = BitPat("b?????????????????010?????0100111")
  def HFENCE_GVMA        = BitPat("b0110001??????????000000001110011")
  def HFENCE_VVMA        = BitPat("b0010001??????????000000001110011")
  def HINVAL_GVMA        = BitPat("b0110011??????????000000001110011")
  def HINVAL_VVMA        = BitPat("b0010011??????????000000001110011")
  def HLV_B              = BitPat("b011000000000?????100?????1110011")
  def HLV_BU             = BitPat("b011000000001?????100?????1110011")
  def HLV_D              = BitPat("b011011000000?????100?????1110011")
  def HLV_H              = BitPat("b011001000000?????100?????1110011")
  def HLV_HU             = BitPat("b011001000001?????100?????1110011")
  def HLV_W              = BitPat("b011010000000?????100?????1110011")
  def HLV_WU             = BitPat("b011010000001?????100?????1110011")
  def HLVX_HU            = BitPat("b011001000011?????100?????1110011")
  def HLVX_WU            = BitPat("b011010000011?????100?????1110011")
  def HSV_B              = BitPat("b0110001??????????100000001110011")
  def HSV_D              = BitPat("b0110111??????????100000001110011")
  def HSV_H              = BitPat("b0110011??????????100000001110011")
  def HSV_W              = BitPat("b0110101??????????100000001110011")
  def JAL                = BitPat("b?????????????????????????1101111")
  def JALR               = BitPat("b?????????????????000?????1100111")
  def LB                 = BitPat("b?????????????????000?????0000011")
  def LBU                = BitPat("b?????????????????100?????0000011")
  def LD                 = BitPat("b?????????????????011?????0000011")
  def LH                 = BitPat("b?????????????????001?????0000011")
  def LHU                = BitPat("b?????????????????101?????0000011")
  def LR_D               = BitPat("b00010??00000?????011?????0101111")
  def LR_W               = BitPat("b00010??00000?????010?????0101111")
  def LUI                = BitPat("b?????????????????????????0110111")
  def LW                 = BitPat("b?????????????????010?????0000011")
  def LWU                = BitPat("b?????????????????110?????0000011")
  def MAX                = BitPat("b0000101??????????110?????0110011")
  def MAXU               = BitPat("b0000101??????????111?????0110011")
  def MIN                = BitPat("b0000101??????????100?????0110011")
  def MINU               = BitPat("b0000101??????????101?????0110011")
  def MRET               = BitPat("b00110000001000000000000001110011")
  def MUL                = BitPat("b0000001??????????000?????0110011")
  def MULH               = BitPat("b0000001??????????001?????0110011")
  def MULHSU             = BitPat("b0000001??????????010?????0110011")
  def MULHU              = BitPat("b0000001??????????011?????0110011")
  def MULW               = BitPat("b0000001??????????000?????0111011")
  def OR                 = BitPat("b0000000??????????110?????0110011")
  def ORC_B              = BitPat("b001010000111?????101?????0010011")
  def ORI                = BitPat("b?????????????????110?????0010011")
  def ORN                = BitPat("b0100000??????????110?????0110011")
  def PACK               = BitPat("b0000100??????????100?????0110011")
  def PACKH              = BitPat("b0000100??????????111?????0110011")
  def PACKW              = BitPat("b0000100??????????100?????0111011")
  def PAUSE              = BitPat("b00000001000000000000000000001111")
  def REM                = BitPat("b0000001??????????110?????0110011")
  def REMU               = BitPat("b0000001??????????111?????0110011")
  def REMUW              = BitPat("b0000001??????????111?????0111011")
  def REMW               = BitPat("b0000001??????????110?????0111011")
  def REV8               = BitPat("b011010111000?????101?????0010011")
  def ROL                = BitPat("b0110000??????????001?????0110011")
  def ROLW               = BitPat("b0110000??????????001?????0111011")
  def ROR                = BitPat("b0110000??????????101?????0110011")
  def RORI               = BitPat("b011000???????????101?????0010011")
  def RORIW              = BitPat("b0110000??????????101?????0011011")
  def RORW               = BitPat("b0110000??????????101?????0111011")
  def SB                 = BitPat("b?????????????????000?????0100011")
  def SC_D               = BitPat("b00011????????????011?????0101111")
  def SC_W               = BitPat("b00011????????????010?????0101111")
  def SD                 = BitPat("b?????????????????011?????0100011")
  def SEXT_B             = BitPat("b011000000100?????001?????0010011")
  def SEXT_H             = BitPat("b011000000101?????001?????0010011")
  def SFENCE_INVAL_IR    = BitPat("b00011000000100000000000001110011")
  def SFENCE_VMA         = BitPat("b0001001??????????000000001110011")
  def SFENCE_W_INVAL     = BitPat("b00011000000000000000000001110011")
  def SH                 = BitPat("b?????????????????001?????0100011")
  def SH1ADD             = BitPat("b0010000??????????010?????0110011")
  def SH1ADD_UW          = BitPat("b0010000??????????010?????0111011")
  def SH2ADD             = BitPat("b0010000??????????100?????0110011")
  def SH2ADD_UW          = BitPat("b0010000??????????100?????0111011")
  def SH3ADD             = BitPat("b0010000??????????110?????0110011")
  def SH3ADD_UW          = BitPat("b0010000??????????110?????0111011")
  def SHA256SIG0         = BitPat("b000100000010?????001?????0010011")
  def SHA256SIG1         = BitPat("b000100000011?????001?????0010011")
  def SHA256SUM0         = BitPat("b000100000000?????001?????0010011")
  def SHA256SUM1         = BitPat("b000100000001?????001?????0010011")
  def SHA512SIG0         = BitPat("b000100000110?????001?????0010011")
  def SHA512SIG1         = BitPat("b000100000111?????001?????0010011")
  def SHA512SUM0         = BitPat("b000100000100?????001?????0010011")
  def SHA512SUM1         = BitPat("b000100000101?????001?????0010011")
  def SINVAL_VMA         = BitPat("b0001011??????????000000001110011")
  def SLL                = BitPat("b0000000??????????001?????0110011")
  def SLLI               = BitPat("b000000???????????001?????0010011")
  def SLLI_UW            = BitPat("b000010???????????001?????0011011")
  def SLLIW              = BitPat("b0000000??????????001?????0011011")
  def SLLW               = BitPat("b0000000??????????001?????0111011")
  def SLT                = BitPat("b0000000??????????010?????0110011")
  def SLTI               = BitPat("b?????????????????010?????0010011")
  def SLTIU              = BitPat("b?????????????????011?????0010011")
  def SLTU               = BitPat("b0000000??????????011?????0110011")
  def SM3P0              = BitPat("b000100001000?????001?????0010011")
  def SM3P1              = BitPat("b000100001001?????001?????0010011")
  def SM4ED              = BitPat("b??11000??????????000?????0110011")
  def SM4ED0             = BitPat("b0011000??????????000?????0110011")
  def SM4ED1             = BitPat("b0111000??????????000?????0110011")
  def SM4ED2             = BitPat("b1011000??????????000?????0110011")
  def SM4ED3             = BitPat("b1111000??????????000?????0110011")
  def SM4KS              = BitPat("b??11010??????????000?????0110011")
  def SM4KS0             = BitPat("b0011010??????????000?????0110011")
  def SM4KS1             = BitPat("b0111010??????????000?????0110011")
  def SM4KS2             = BitPat("b1011010??????????000?????0110011")
  def SM4KS3             = BitPat("b1111010??????????000?????0110011")
  def SRA                = BitPat("b0100000??????????101?????0110011")
  def SRAI               = BitPat("b010000???????????101?????0010011")
  def SRAIW              = BitPat("b0100000??????????101?????0011011")
  def SRAW               = BitPat("b0100000??????????101?????0111011")
  def SRET               = BitPat("b00010000001000000000000001110011")
  def SRL                = BitPat("b0000000??????????101?????0110011")
  def SRLI               = BitPat("b000000???????????101?????0010011")
  def SRLIW              = BitPat("b0000000??????????101?????0011011")
  def SRLW               = BitPat("b0000000??????????101?????0111011")
  def SUB                = BitPat("b0100000??????????000?????0110011")
  def SUBW               = BitPat("b0100000??????????000?????0111011")
  def SW                 = BitPat("b?????????????????010?????0100011")
  def VAADD_VV           = BitPat("b001001???????????010?????1010111")
  def VAADD_VX           = BitPat("b001001???????????110?????1010111")
  def VAADDU_VV          = BitPat("b001000???????????010?????1010111")
  def VAADDU_VX          = BitPat("b001000???????????110?????1010111")
  def VADC_VIM           = BitPat("b0100000??????????011?????1010111")
  def VADC_VVM           = BitPat("b0100000??????????000?????1010111")
  def VADC_VXM           = BitPat("b0100000??????????100?????1010111")
  def VADD_VI            = BitPat("b000000???????????011?????1010111")
  def VADD_VV            = BitPat("b000000???????????000?????1010111")
  def VADD_VX            = BitPat("b000000???????????100?????1010111")
  def VAMOADDEI16_V      = BitPat("b00000????????????101?????0101111")
  def VAMOADDEI32_V      = BitPat("b00000????????????110?????0101111")
  def VAMOADDEI64_V      = BitPat("b00000????????????111?????0101111")
  def VAMOADDEI8_V       = BitPat("b00000????????????000?????0101111")
  def VAMOANDEI16_V      = BitPat("b01100????????????101?????0101111")
  def VAMOANDEI32_V      = BitPat("b01100????????????110?????0101111")
  def VAMOANDEI64_V      = BitPat("b01100????????????111?????0101111")
  def VAMOANDEI8_V       = BitPat("b01100????????????000?????0101111")
  def VAMOMAXEI16_V      = BitPat("b10100????????????101?????0101111")
  def VAMOMAXEI32_V      = BitPat("b10100????????????110?????0101111")
  def VAMOMAXEI64_V      = BitPat("b10100????????????111?????0101111")
  def VAMOMAXEI8_V       = BitPat("b10100????????????000?????0101111")
  def VAMOMAXUEI16_V     = BitPat("b11100????????????101?????0101111")
  def VAMOMAXUEI32_V     = BitPat("b11100????????????110?????0101111")
  def VAMOMAXUEI64_V     = BitPat("b11100????????????111?????0101111")
  def VAMOMAXUEI8_V      = BitPat("b11100????????????000?????0101111")
  def VAMOMINEI16_V      = BitPat("b10000????????????101?????0101111")
  def VAMOMINEI32_V      = BitPat("b10000????????????110?????0101111")
  def VAMOMINEI64_V      = BitPat("b10000????????????111?????0101111")
  def VAMOMINEI8_V       = BitPat("b10000????????????000?????0101111")
  def VAMOMINUEI16_V     = BitPat("b11000????????????101?????0101111")
  def VAMOMINUEI32_V     = BitPat("b11000????????????110?????0101111")
  def VAMOMINUEI64_V     = BitPat("b11000????????????111?????0101111")
  def VAMOMINUEI8_V      = BitPat("b11000????????????000?????0101111")
  def VAMOOREI16_V       = BitPat("b01000????????????101?????0101111")
  def VAMOOREI32_V       = BitPat("b01000????????????110?????0101111")
  def VAMOOREI64_V       = BitPat("b01000????????????111?????0101111")
  def VAMOOREI8_V        = BitPat("b01000????????????000?????0101111")
  def VAMOSWAPEI16_V     = BitPat("b00001????????????101?????0101111")
  def VAMOSWAPEI32_V     = BitPat("b00001????????????110?????0101111")
  def VAMOSWAPEI64_V     = BitPat("b00001????????????111?????0101111")
  def VAMOSWAPEI8_V      = BitPat("b00001????????????000?????0101111")
  def VAMOXOREI16_V      = BitPat("b00100????????????101?????0101111")
  def VAMOXOREI32_V      = BitPat("b00100????????????110?????0101111")
  def VAMOXOREI64_V      = BitPat("b00100????????????111?????0101111")
  def VAMOXOREI8_V       = BitPat("b00100????????????000?????0101111")
  def VAND_VI            = BitPat("b001001???????????011?????1010111")
  def VAND_VV            = BitPat("b001001???????????000?????1010111")
  def VAND_VX            = BitPat("b001001???????????100?????1010111")
  def VASUB_VV           = BitPat("b001011???????????010?????1010111")
  def VASUB_VX           = BitPat("b001011???????????110?????1010111")
  def VASUBU_VV          = BitPat("b001010???????????010?????1010111")
  def VASUBU_VX          = BitPat("b001010???????????110?????1010111")
  def VCOMPRESS_VM       = BitPat("b0101111??????????010?????1010111")
  def VCPOP_M            = BitPat("b010000??????10000010?????1010111")
  def VDIV_VV            = BitPat("b100001???????????010?????1010111")
  def VDIV_VX            = BitPat("b100001???????????110?????1010111")
  def VDIVU_VV           = BitPat("b100000???????????010?????1010111")
  def VDIVU_VX           = BitPat("b100000???????????110?????1010111")
  def VFADD_VF           = BitPat("b000000???????????101?????1010111")
  def VFADD_VV           = BitPat("b000000???????????001?????1010111")
  def VFCLASS_V          = BitPat("b010011??????10000001?????1010111")
  def VFCVT_F_X_V        = BitPat("b010010??????00011001?????1010111")
  def VFCVT_F_XU_V       = BitPat("b010010??????00010001?????1010111")
  def VFCVT_RTZ_X_F_V    = BitPat("b010010??????00111001?????1010111")
  def VFCVT_RTZ_XU_F_V   = BitPat("b010010??????00110001?????1010111")
  def VFCVT_X_F_V        = BitPat("b010010??????00001001?????1010111")
  def VFCVT_XU_F_V       = BitPat("b010010??????00000001?????1010111")
  def VFDIV_VF           = BitPat("b100000???????????101?????1010111")
  def VFDIV_VV           = BitPat("b100000???????????001?????1010111")
  def VFIRST_M           = BitPat("b010000??????10001010?????1010111")
  def VFMACC_VF          = BitPat("b101100???????????101?????1010111")
  def VFMACC_VV          = BitPat("b101100???????????001?????1010111")
  def VFMADD_VF          = BitPat("b101000???????????101?????1010111")
  def VFMADD_VV          = BitPat("b101000???????????001?????1010111")
  def VFMAX_VF           = BitPat("b000110???????????101?????1010111")
  def VFMAX_VV           = BitPat("b000110???????????001?????1010111")
  def VFMERGE_VFM        = BitPat("b0101110??????????101?????1010111")
  def VFMIN_VF           = BitPat("b000100???????????101?????1010111")
  def VFMIN_VV           = BitPat("b000100???????????001?????1010111")
  def VFMSAC_VF          = BitPat("b101110???????????101?????1010111")
  def VFMSAC_VV          = BitPat("b101110???????????001?????1010111")
  def VFMSUB_VF          = BitPat("b101010???????????101?????1010111")
  def VFMSUB_VV          = BitPat("b101010???????????001?????1010111")
  def VFMUL_VF           = BitPat("b100100???????????101?????1010111")
  def VFMUL_VV           = BitPat("b100100???????????001?????1010111")
  def VFMV_F_S           = BitPat("b0100001?????00000001?????1010111")
  def VFMV_S_F           = BitPat("b010000100000?????101?????1010111")
  def VFMV_V_F           = BitPat("b010111100000?????101?????1010111")
  def VFNCVT_F_F_W       = BitPat("b010010??????10100001?????1010111")
  def VFNCVT_F_X_W       = BitPat("b010010??????10011001?????1010111")
  def VFNCVT_F_XU_W      = BitPat("b010010??????10010001?????1010111")
  def VFNCVT_ROD_F_F_W   = BitPat("b010010??????10101001?????1010111")
  def VFNCVT_RTZ_X_F_W   = BitPat("b010010??????10111001?????1010111")
  def VFNCVT_RTZ_XU_F_W  = BitPat("b010010??????10110001?????1010111")
  def VFNCVT_X_F_W       = BitPat("b010010??????10001001?????1010111")
  def VFNCVT_XU_F_W      = BitPat("b010010??????10000001?????1010111")
  def VFNMACC_VF         = BitPat("b101101???????????101?????1010111")
  def VFNMACC_VV         = BitPat("b101101???????????001?????1010111")
  def VFNMADD_VF         = BitPat("b101001???????????101?????1010111")
  def VFNMADD_VV         = BitPat("b101001???????????001?????1010111")
  def VFNMSAC_VF         = BitPat("b101111???????????101?????1010111")
  def VFNMSAC_VV         = BitPat("b101111???????????001?????1010111")
  def VFNMSUB_VF         = BitPat("b101011???????????101?????1010111")
  def VFNMSUB_VV         = BitPat("b101011???????????001?????1010111")
  def VFRDIV_VF          = BitPat("b100001???????????101?????1010111")
  def VFREC7_V           = BitPat("b010011??????00101001?????1010111")
  def VFREDMAX_VS        = BitPat("b000111???????????001?????1010111")
  def VFREDMIN_VS        = BitPat("b000101???????????001?????1010111")
  def VFREDOSUM_VS       = BitPat("b000011???????????001?????1010111")
  def VFREDUSUM_VS       = BitPat("b000001???????????001?????1010111")
  def VFRSQRT7_V         = BitPat("b010011??????00100001?????1010111")
  def VFRSUB_VF          = BitPat("b100111???????????101?????1010111")
  def VFSGNJ_VF          = BitPat("b001000???????????101?????1010111")
  def VFSGNJ_VV          = BitPat("b001000???????????001?????1010111")
  def VFSGNJN_VF         = BitPat("b001001???????????101?????1010111")
  def VFSGNJN_VV         = BitPat("b001001???????????001?????1010111")
  def VFSGNJX_VF         = BitPat("b001010???????????101?????1010111")
  def VFSGNJX_VV         = BitPat("b001010???????????001?????1010111")
  def VFSLIDE1DOWN_VF    = BitPat("b001111???????????101?????1010111")
  def VFSLIDE1UP_VF      = BitPat("b001110???????????101?????1010111")
  def VFSQRT_V           = BitPat("b010011??????00000001?????1010111")
  def VFSUB_VF           = BitPat("b000010???????????101?????1010111")
  def VFSUB_VV           = BitPat("b000010???????????001?????1010111")
  def VFWADD_VF          = BitPat("b110000???????????101?????1010111")
  def VFWADD_VV          = BitPat("b110000???????????001?????1010111")
  def VFWADD_WF          = BitPat("b110100???????????101?????1010111")
  def VFWADD_WV          = BitPat("b110100???????????001?????1010111")
  def VFWCVT_F_F_V       = BitPat("b010010??????01100001?????1010111")
  def VFWCVT_F_X_V       = BitPat("b010010??????01011001?????1010111")
  def VFWCVT_F_XU_V      = BitPat("b010010??????01010001?????1010111")
  def VFWCVT_RTZ_X_F_V   = BitPat("b010010??????01111001?????1010111")
  def VFWCVT_RTZ_XU_F_V  = BitPat("b010010??????01110001?????1010111")
  def VFWCVT_X_F_V       = BitPat("b010010??????01001001?????1010111")
  def VFWCVT_XU_F_V      = BitPat("b010010??????01000001?????1010111")
  def VFWMACC_VF         = BitPat("b111100???????????101?????1010111")
  def VFWMACC_VV         = BitPat("b111100???????????001?????1010111")
  def VFWMSAC_VF         = BitPat("b111110???????????101?????1010111")
  def VFWMSAC_VV         = BitPat("b111110???????????001?????1010111")
  def VFWMUL_VF          = BitPat("b111000???????????101?????1010111")
  def VFWMUL_VV          = BitPat("b111000???????????001?????1010111")
  def VFWNMACC_VF        = BitPat("b111101???????????101?????1010111")
  def VFWNMACC_VV        = BitPat("b111101???????????001?????1010111")
  def VFWNMSAC_VF        = BitPat("b111111???????????101?????1010111")
  def VFWNMSAC_VV        = BitPat("b111111???????????001?????1010111")
  def VFWREDOSUM_VS      = BitPat("b110011???????????001?????1010111")
  def VFWREDUSUM_VS      = BitPat("b110001???????????001?????1010111")
  def VFWSUB_VF          = BitPat("b110010???????????101?????1010111")
  def VFWSUB_VV          = BitPat("b110010???????????001?????1010111")
  def VFWSUB_WF          = BitPat("b110110???????????101?????1010111")
  def VFWSUB_WV          = BitPat("b110110???????????001?????1010111")
  def VID_V              = BitPat("b010100?0000010001010?????1010111")
  def VIOTA_M            = BitPat("b010100??????10000010?????1010111")
  def VL1RE16_V          = BitPat("b000000101000?????101?????0000111")
  def VL1RE32_V          = BitPat("b000000101000?????110?????0000111")
  def VL1RE64_V          = BitPat("b000000101000?????111?????0000111")
  def VL1RE8_V           = BitPat("b000000101000?????000?????0000111")
  def VL2RE16_V          = BitPat("b001000101000?????101?????0000111")
  def VL2RE32_V          = BitPat("b001000101000?????110?????0000111")
  def VL2RE64_V          = BitPat("b001000101000?????111?????0000111")
  def VL2RE8_V           = BitPat("b001000101000?????000?????0000111")
  def VL4RE16_V          = BitPat("b011000101000?????101?????0000111")
  def VL4RE32_V          = BitPat("b011000101000?????110?????0000111")
  def VL4RE64_V          = BitPat("b011000101000?????111?????0000111")
  def VL4RE8_V           = BitPat("b011000101000?????000?????0000111")
  def VL8RE16_V          = BitPat("b111000101000?????101?????0000111")
  def VL8RE32_V          = BitPat("b111000101000?????110?????0000111")
  def VL8RE64_V          = BitPat("b111000101000?????111?????0000111")
  def VL8RE8_V           = BitPat("b111000101000?????000?????0000111")
  def VLE1024_V          = BitPat("b???100?00000?????111?????0000111")
  def VLE1024FF_V        = BitPat("b???100?10000?????111?????0000111")
  def VLE128_V           = BitPat("b???100?00000?????000?????0000111")
  def VLE128FF_V         = BitPat("b???100?10000?????000?????0000111")
  def VLE16_V            = BitPat("b???000?00000?????101?????0000111")
  def VLE16FF_V          = BitPat("b???000?10000?????101?????0000111")
  def VLE256_V           = BitPat("b???100?00000?????101?????0000111")
  def VLE256FF_V         = BitPat("b???100?10000?????101?????0000111")
  def VLE32_V            = BitPat("b???000?00000?????110?????0000111")
  def VLE32FF_V          = BitPat("b???000?10000?????110?????0000111")
  def VLE512_V           = BitPat("b???100?00000?????110?????0000111")
  def VLE512FF_V         = BitPat("b???100?10000?????110?????0000111")
  def VLE64_V            = BitPat("b???000?00000?????111?????0000111")
  def VLE64FF_V          = BitPat("b???000?10000?????111?????0000111")
  def VLE8_V             = BitPat("b???000?00000?????000?????0000111")
  def VLE8FF_V           = BitPat("b???000?10000?????000?????0000111")
  def VLM_V              = BitPat("b000000101011?????000?????0000111")
  def VLOXEI1024_V       = BitPat("b???111???????????111?????0000111")
  def VLOXEI128_V        = BitPat("b???111???????????000?????0000111")
  def VLOXEI16_V         = BitPat("b???011???????????101?????0000111")
  def VLOXEI256_V        = BitPat("b???111???????????101?????0000111")
  def VLOXEI32_V         = BitPat("b???011???????????110?????0000111")
  def VLOXEI512_V        = BitPat("b???111???????????110?????0000111")
  def VLOXEI64_V         = BitPat("b???011???????????111?????0000111")
  def VLOXEI8_V          = BitPat("b???011???????????000?????0000111")
  def VLSE1024_V         = BitPat("b???110???????????111?????0000111")
  def VLSE128_V          = BitPat("b???110???????????000?????0000111")
  def VLSE16_V           = BitPat("b???010???????????101?????0000111")
  def VLSE256_V          = BitPat("b???110???????????101?????0000111")
  def VLSE32_V           = BitPat("b???010???????????110?????0000111")
  def VLSE512_V          = BitPat("b???110???????????110?????0000111")
  def VLSE64_V           = BitPat("b???010???????????111?????0000111")
  def VLSE8_V            = BitPat("b???010???????????000?????0000111")
  def VLUXEI1024_V       = BitPat("b???101???????????111?????0000111")
  def VLUXEI128_V        = BitPat("b???101???????????000?????0000111")
  def VLUXEI16_V         = BitPat("b???001???????????101?????0000111")
  def VLUXEI256_V        = BitPat("b???101???????????101?????0000111")
  def VLUXEI32_V         = BitPat("b???001???????????110?????0000111")
  def VLUXEI512_V        = BitPat("b???101???????????110?????0000111")
  def VLUXEI64_V         = BitPat("b???001???????????111?????0000111")
  def VLUXEI8_V          = BitPat("b???001???????????000?????0000111")
  def VMACC_VV           = BitPat("b101101???????????010?????1010111")
  def VMACC_VX           = BitPat("b101101???????????110?????1010111")
  def VMADC_VI           = BitPat("b0100011??????????011?????1010111")
  def VMADC_VIM          = BitPat("b0100010??????????011?????1010111")
  def VMADC_VV           = BitPat("b0100011??????????000?????1010111")
  def VMADC_VVM          = BitPat("b0100010??????????000?????1010111")
  def VMADC_VX           = BitPat("b0100011??????????100?????1010111")
  def VMADC_VXM          = BitPat("b0100010??????????100?????1010111")
  def VMADD_VV           = BitPat("b101001???????????010?????1010111")
  def VMADD_VX           = BitPat("b101001???????????110?????1010111")
  def VMAND_MM           = BitPat("b0110011??????????010?????1010111")
  def VMANDN_MM          = BitPat("b0110001??????????010?????1010111")
  def VMAX_VV            = BitPat("b000111???????????000?????1010111")
  def VMAX_VX            = BitPat("b000111???????????100?????1010111")
  def VMAXU_VV           = BitPat("b000110???????????000?????1010111")
  def VMAXU_VX           = BitPat("b000110???????????100?????1010111")
  def VMERGE_VIM         = BitPat("b0101110??????????011?????1010111")
  def VMERGE_VVM         = BitPat("b0101110??????????000?????1010111")
  def VMERGE_VXM         = BitPat("b0101110??????????100?????1010111")
  def VMFEQ_VF           = BitPat("b011000???????????101?????1010111")
  def VMFEQ_VV           = BitPat("b011000???????????001?????1010111")
  def VMFGE_VF           = BitPat("b011111???????????101?????1010111")
  def VMFGT_VF           = BitPat("b011101???????????101?????1010111")
  def VMFLE_VF           = BitPat("b011001???????????101?????1010111")
  def VMFLE_VV           = BitPat("b011001???????????001?????1010111")
  def VMFLT_VF           = BitPat("b011011???????????101?????1010111")
  def VMFLT_VV           = BitPat("b011011???????????001?????1010111")
  def VMFNE_VF           = BitPat("b011100???????????101?????1010111")
  def VMFNE_VV           = BitPat("b011100???????????001?????1010111")
  def VMIN_VV            = BitPat("b000101???????????000?????1010111")
  def VMIN_VX            = BitPat("b000101???????????100?????1010111")
  def VMINU_VV           = BitPat("b000100???????????000?????1010111")
  def VMINU_VX           = BitPat("b000100???????????100?????1010111")
  def VMNAND_MM          = BitPat("b0111011??????????010?????1010111")
  def VMNOR_MM           = BitPat("b0111101??????????010?????1010111")
  def VMOR_MM            = BitPat("b0110101??????????010?????1010111")
  def VMORN_MM           = BitPat("b0111001??????????010?????1010111")
  def VMSBC_VV           = BitPat("b0100111??????????000?????1010111")
  def VMSBC_VVM          = BitPat("b0100110??????????000?????1010111")
  def VMSBC_VX           = BitPat("b0100111??????????100?????1010111")
  def VMSBC_VXM          = BitPat("b0100110??????????100?????1010111")
  def VMSBF_M            = BitPat("b010100??????00001010?????1010111")
  def VMSEQ_VI           = BitPat("b011000???????????011?????1010111")
  def VMSEQ_VV           = BitPat("b011000???????????000?????1010111")
  def VMSEQ_VX           = BitPat("b011000???????????100?????1010111")
  def VMSGT_VI           = BitPat("b011111???????????011?????1010111")
  def VMSGT_VX           = BitPat("b011111???????????100?????1010111")
  def VMSGTU_VI          = BitPat("b011110???????????011?????1010111")
  def VMSGTU_VX          = BitPat("b011110???????????100?????1010111")
  def VMSIF_M            = BitPat("b010100??????00011010?????1010111")
  def VMSLE_VI           = BitPat("b011101???????????011?????1010111")
  def VMSLE_VV           = BitPat("b011101???????????000?????1010111")
  def VMSLE_VX           = BitPat("b011101???????????100?????1010111")
  def VMSLEU_VI          = BitPat("b011100???????????011?????1010111")
  def VMSLEU_VV          = BitPat("b011100???????????000?????1010111")
  def VMSLEU_VX          = BitPat("b011100???????????100?????1010111")
  def VMSLT_VV           = BitPat("b011011???????????000?????1010111")
  def VMSLT_VX           = BitPat("b011011???????????100?????1010111")
  def VMSLTU_VV          = BitPat("b011010???????????000?????1010111")
  def VMSLTU_VX          = BitPat("b011010???????????100?????1010111")
  def VMSNE_VI           = BitPat("b011001???????????011?????1010111")
  def VMSNE_VV           = BitPat("b011001???????????000?????1010111")
  def VMSNE_VX           = BitPat("b011001???????????100?????1010111")
  def VMSOF_M            = BitPat("b010100??????00010010?????1010111")
  def VMUL_VV            = BitPat("b100101???????????010?????1010111")
  def VMUL_VX            = BitPat("b100101???????????110?????1010111")
  def VMULH_VV           = BitPat("b100111???????????010?????1010111")
  def VMULH_VX           = BitPat("b100111???????????110?????1010111")
  def VMULHSU_VV         = BitPat("b100110???????????010?????1010111")
  def VMULHSU_VX         = BitPat("b100110???????????110?????1010111")
  def VMULHU_VV          = BitPat("b100100???????????010?????1010111")
  def VMULHU_VX          = BitPat("b100100???????????110?????1010111")
  def VMV1R_V            = BitPat("b1001111?????00000011?????1010111")
  def VMV2R_V            = BitPat("b1001111?????00001011?????1010111")
  def VMV4R_V            = BitPat("b1001111?????00011011?????1010111")
  def VMV8R_V            = BitPat("b1001111?????00111011?????1010111")
  def VMV_S_X            = BitPat("b010000100000?????110?????1010111")
  def VMV_V_I            = BitPat("b010111100000?????011?????1010111")
  def VMV_V_V            = BitPat("b010111100000?????000?????1010111")
  def VMV_V_X            = BitPat("b010111100000?????100?????1010111")
  def VMV_X_S            = BitPat("b0100001?????00000010?????1010111")
  def VMXNOR_MM          = BitPat("b0111111??????????010?????1010111")
  def VMXOR_MM           = BitPat("b0110111??????????010?????1010111")
  def VNCLIP_WI          = BitPat("b101111???????????011?????1010111")
  def VNCLIP_WV          = BitPat("b101111???????????000?????1010111")
  def VNCLIP_WX          = BitPat("b101111???????????100?????1010111")
  def VNCLIPU_WI         = BitPat("b101110???????????011?????1010111")
  def VNCLIPU_WV         = BitPat("b101110???????????000?????1010111")
  def VNCLIPU_WX         = BitPat("b101110???????????100?????1010111")
  def VNMSAC_VV          = BitPat("b101111???????????010?????1010111")
  def VNMSAC_VX          = BitPat("b101111???????????110?????1010111")
  def VNMSUB_VV          = BitPat("b101011???????????010?????1010111")
  def VNMSUB_VX          = BitPat("b101011???????????110?????1010111")
  def VNSRA_WI           = BitPat("b101101???????????011?????1010111")
  def VNSRA_WV           = BitPat("b101101???????????000?????1010111")
  def VNSRA_WX           = BitPat("b101101???????????100?????1010111")
  def VNSRL_WI           = BitPat("b101100???????????011?????1010111")
  def VNSRL_WV           = BitPat("b101100???????????000?????1010111")
  def VNSRL_WX           = BitPat("b101100???????????100?????1010111")
  def VOR_VI             = BitPat("b001010???????????011?????1010111")
  def VOR_VV             = BitPat("b001010???????????000?????1010111")
  def VOR_VX             = BitPat("b001010???????????100?????1010111")
  def VREDAND_VS         = BitPat("b000001???????????010?????1010111")
  def VREDMAX_VS         = BitPat("b000111???????????010?????1010111")
  def VREDMAXU_VS        = BitPat("b000110???????????010?????1010111")
  def VREDMIN_VS         = BitPat("b000101???????????010?????1010111")
  def VREDMINU_VS        = BitPat("b000100???????????010?????1010111")
  def VREDOR_VS          = BitPat("b000010???????????010?????1010111")
  def VREDSUM_VS         = BitPat("b000000???????????010?????1010111")
  def VREDXOR_VS         = BitPat("b000011???????????010?????1010111")
  def VREM_VV            = BitPat("b100011???????????010?????1010111")
  def VREM_VX            = BitPat("b100011???????????110?????1010111")
  def VREMU_VV           = BitPat("b100010???????????010?????1010111")
  def VREMU_VX           = BitPat("b100010???????????110?????1010111")
  def VRGATHER_VI        = BitPat("b001100???????????011?????1010111")
  def VRGATHER_VV        = BitPat("b001100???????????000?????1010111")
  def VRGATHER_VX        = BitPat("b001100???????????100?????1010111")
  def VRGATHEREI16_VV    = BitPat("b001110???????????000?????1010111")
  def VRSUB_VI           = BitPat("b000011???????????011?????1010111")
  def VRSUB_VX           = BitPat("b000011???????????100?????1010111")
  def VS1R_V             = BitPat("b000000101000?????000?????0100111")
  def VS2R_V             = BitPat("b001000101000?????000?????0100111")
  def VS4R_V             = BitPat("b011000101000?????000?????0100111")
  def VS8R_V             = BitPat("b111000101000?????000?????0100111")
  def VSADD_VI           = BitPat("b100001???????????011?????1010111")
  def VSADD_VV           = BitPat("b100001???????????000?????1010111")
  def VSADD_VX           = BitPat("b100001???????????100?????1010111")
  def VSADDU_VI          = BitPat("b100000???????????011?????1010111")
  def VSADDU_VV          = BitPat("b100000???????????000?????1010111")
  def VSADDU_VX          = BitPat("b100000???????????100?????1010111")
  def VSBC_VVM           = BitPat("b0100100??????????000?????1010111")
  def VSBC_VXM           = BitPat("b0100100??????????100?????1010111")
  def VSE1024_V          = BitPat("b???100?00000?????111?????0100111")
  def VSE128_V           = BitPat("b???100?00000?????000?????0100111")
  def VSE16_V            = BitPat("b???000?00000?????101?????0100111")
  def VSE256_V           = BitPat("b???100?00000?????101?????0100111")
  def VSE32_V            = BitPat("b???000?00000?????110?????0100111")
  def VSE512_V           = BitPat("b???100?00000?????110?????0100111")
  def VSE64_V            = BitPat("b???000?00000?????111?????0100111")
  def VSE8_V             = BitPat("b???000?00000?????000?????0100111")
  def VSETIVLI           = BitPat("b11???????????????111?????1010111")
  def VSETVL             = BitPat("b1000000??????????111?????1010111")
  def VSETVLI            = BitPat("b0????????????????111?????1010111")
  def VSEXT_VF2          = BitPat("b010010??????00111010?????1010111")
  def VSEXT_VF4          = BitPat("b010010??????00101010?????1010111")
  def VSEXT_VF8          = BitPat("b010010??????00011010?????1010111")
  def VSLIDE1DOWN_VX     = BitPat("b001111???????????110?????1010111")
  def VSLIDE1UP_VX       = BitPat("b001110???????????110?????1010111")
  def VSLIDEDOWN_VI      = BitPat("b001111???????????011?????1010111")
  def VSLIDEDOWN_VX      = BitPat("b001111???????????100?????1010111")
  def VSLIDEUP_VI        = BitPat("b001110???????????011?????1010111")
  def VSLIDEUP_VX        = BitPat("b001110???????????100?????1010111")
  def VSLL_VI            = BitPat("b100101???????????011?????1010111")
  def VSLL_VV            = BitPat("b100101???????????000?????1010111")
  def VSLL_VX            = BitPat("b100101???????????100?????1010111")
  def VSM_V              = BitPat("b000000101011?????000?????0100111")
  def VSMUL_VV           = BitPat("b100111???????????000?????1010111")
  def VSMUL_VX           = BitPat("b100111???????????100?????1010111")
  def VSOXEI1024_V       = BitPat("b???111???????????111?????0100111")
  def VSOXEI128_V        = BitPat("b???111???????????000?????0100111")
  def VSOXEI16_V         = BitPat("b???011???????????101?????0100111")
  def VSOXEI256_V        = BitPat("b???111???????????101?????0100111")
  def VSOXEI32_V         = BitPat("b???011???????????110?????0100111")
  def VSOXEI512_V        = BitPat("b???111???????????110?????0100111")
  def VSOXEI64_V         = BitPat("b???011???????????111?????0100111")
  def VSOXEI8_V          = BitPat("b???011???????????000?????0100111")
  def VSRA_VI            = BitPat("b101001???????????011?????1010111")
  def VSRA_VV            = BitPat("b101001???????????000?????1010111")
  def VSRA_VX            = BitPat("b101001???????????100?????1010111")
  def VSRL_VI            = BitPat("b101000???????????011?????1010111")
  def VSRL_VV            = BitPat("b101000???????????000?????1010111")
  def VSRL_VX            = BitPat("b101000???????????100?????1010111")
  def VSSE1024_V         = BitPat("b???110???????????111?????0100111")
  def VSSE128_V          = BitPat("b???110???????????000?????0100111")
  def VSSE16_V           = BitPat("b???010???????????101?????0100111")
  def VSSE256_V          = BitPat("b???110???????????101?????0100111")
  def VSSE32_V           = BitPat("b???010???????????110?????0100111")
  def VSSE512_V          = BitPat("b???110???????????110?????0100111")
  def VSSE64_V           = BitPat("b???010???????????111?????0100111")
  def VSSE8_V            = BitPat("b???010???????????000?????0100111")
  def VSSRA_VI           = BitPat("b101011???????????011?????1010111")
  def VSSRA_VV           = BitPat("b101011???????????000?????1010111")
  def VSSRA_VX           = BitPat("b101011???????????100?????1010111")
  def VSSRL_VI           = BitPat("b101010???????????011?????1010111")
  def VSSRL_VV           = BitPat("b101010???????????000?????1010111")
  def VSSRL_VX           = BitPat("b101010???????????100?????1010111")
  def VSSUB_VV           = BitPat("b100011???????????000?????1010111")
  def VSSUB_VX           = BitPat("b100011???????????100?????1010111")
  def VSSUBU_VV          = BitPat("b100010???????????000?????1010111")
  def VSSUBU_VX          = BitPat("b100010???????????100?????1010111")
  def VSUB_VV            = BitPat("b000010???????????000?????1010111")
  def VSUB_VX            = BitPat("b000010???????????100?????1010111")
  def VSUXEI1024_V       = BitPat("b???101???????????111?????0100111")
  def VSUXEI128_V        = BitPat("b???101???????????000?????0100111")
  def VSUXEI16_V         = BitPat("b???001???????????101?????0100111")
  def VSUXEI256_V        = BitPat("b???101???????????101?????0100111")
  def VSUXEI32_V         = BitPat("b???001???????????110?????0100111")
  def VSUXEI512_V        = BitPat("b???101???????????110?????0100111")
  def VSUXEI64_V         = BitPat("b???001???????????111?????0100111")
  def VSUXEI8_V          = BitPat("b???001???????????000?????0100111")
  def VWADD_VV           = BitPat("b110001???????????010?????1010111")
  def VWADD_VX           = BitPat("b110001???????????110?????1010111")
  def VWADD_WV           = BitPat("b110101???????????010?????1010111")
  def VWADD_WX           = BitPat("b110101???????????110?????1010111")
  def VWADDU_VV          = BitPat("b110000???????????010?????1010111")
  def VWADDU_VX          = BitPat("b110000???????????110?????1010111")
  def VWADDU_WV          = BitPat("b110100???????????010?????1010111")
  def VWADDU_WX          = BitPat("b110100???????????110?????1010111")
  def VWMACC_VV          = BitPat("b111101???????????010?????1010111")
  def VWMACC_VX          = BitPat("b111101???????????110?????1010111")
  def VWMACCSU_VV        = BitPat("b111111???????????010?????1010111")
  def VWMACCSU_VX        = BitPat("b111111???????????110?????1010111")
  def VWMACCU_VV         = BitPat("b111100???????????010?????1010111")
  def VWMACCU_VX         = BitPat("b111100???????????110?????1010111")
  def VWMACCUS_VX        = BitPat("b111110???????????110?????1010111")
  def VWMUL_VV           = BitPat("b111011???????????010?????1010111")
  def VWMUL_VX           = BitPat("b111011???????????110?????1010111")
  def VWMULSU_VV         = BitPat("b111010???????????010?????1010111")
  def VWMULSU_VX         = BitPat("b111010???????????110?????1010111")
  def VWMULU_VV          = BitPat("b111000???????????010?????1010111")
  def VWMULU_VX          = BitPat("b111000???????????110?????1010111")
  def VWREDSUM_VS        = BitPat("b110001???????????000?????1010111")
  def VWREDSUMU_VS       = BitPat("b110000???????????000?????1010111")
  def VWSUB_VV           = BitPat("b110011???????????010?????1010111")
  def VWSUB_VX           = BitPat("b110011???????????110?????1010111")
  def VWSUB_WV           = BitPat("b110111???????????010?????1010111")
  def VWSUB_WX           = BitPat("b110111???????????110?????1010111")
  def VWSUBU_VV          = BitPat("b110010???????????010?????1010111")
  def VWSUBU_VX          = BitPat("b110010???????????110?????1010111")
  def VWSUBU_WV          = BitPat("b110110???????????010?????1010111")
  def VWSUBU_WX          = BitPat("b110110???????????110?????1010111")
  def VXOR_VI            = BitPat("b001011???????????011?????1010111")
  def VXOR_VV            = BitPat("b001011???????????000?????1010111")
  def VXOR_VX            = BitPat("b001011???????????100?????1010111")
  def VZEXT_VF2          = BitPat("b010010??????00110010?????1010111")
  def VZEXT_VF4          = BitPat("b010010??????00100010?????1010111")
  def VZEXT_VF8          = BitPat("b010010??????00010010?????1010111")
  def WFI                = BitPat("b00010000010100000000000001110011")
  def WRS_NTO            = BitPat("b00000000110100000000000001110011")
  def WRS_STO            = BitPat("b00000001110100000000000001110011")
  def XNOR               = BitPat("b0100000??????????100?????0110011")
  def XOR                = BitPat("b0000000??????????100?????0110011")
  def XORI               = BitPat("b?????????????????100?????0010011")
  def XPERM4             = BitPat("b0010100??????????010?????0110011")
  def XPERM8             = BitPat("b0010100??????????100?????0110011")
  def ZEXT_H             = BitPat("b000010000000?????100?????0111011")

}
object Causes {
  val misaligned_fetch = 0x0
  val fetch_access = 0x1
  val illegal_instruction = 0x2
  val breakpoint = 0x3
  val misaligned_load = 0x4
  val load_access = 0x5
  val misaligned_store = 0x6
  val store_access = 0x7
  val user_ecall = 0x8
  val supervisor_ecall = 0x9
  val virtual_supervisor_ecall = 0xa
  val machine_ecall = 0xb
  val fetch_page_fault = 0xc
  val load_page_fault = 0xd
  val store_page_fault = 0xf
  val fetch_guest_page_fault = 0x14
  val load_guest_page_fault = 0x15
  val virtual_instruction = 0x16
  val store_guest_page_fault = 0x17
  val all = {
    val res = collection.mutable.ArrayBuffer[Int]()
    res += misaligned_fetch
    res += fetch_access
    res += illegal_instruction
    res += breakpoint
    res += misaligned_load
    res += load_access
    res += misaligned_store
    res += store_access
    res += user_ecall
    res += supervisor_ecall
    res += virtual_supervisor_ecall
    res += machine_ecall
    res += fetch_page_fault
    res += load_page_fault
    res += store_page_fault
    res += fetch_guest_page_fault
    res += load_guest_page_fault
    res += virtual_instruction
    res += store_guest_page_fault
    res.toArray
  }
}
object CSRs {
  val fflags = 0x1
  val frm = 0x2
  val fcsr = 0x3
  val vstart = 0x8
  val vxsat = 0x9
  val vxrm = 0xa
  val vcsr = 0xf
  val seed = 0x15
  val jvt = 0x17
  val cycle = 0xc00
  val time = 0xc01
  val instret = 0xc02
  val hpmcounter3 = 0xc03
  val hpmcounter4 = 0xc04
  val hpmcounter5 = 0xc05
  val hpmcounter6 = 0xc06
  val hpmcounter7 = 0xc07
  val hpmcounter8 = 0xc08
  val hpmcounter9 = 0xc09
  val hpmcounter10 = 0xc0a
  val hpmcounter11 = 0xc0b
  val hpmcounter12 = 0xc0c
  val hpmcounter13 = 0xc0d
  val hpmcounter14 = 0xc0e
  val hpmcounter15 = 0xc0f
  val hpmcounter16 = 0xc10
  val hpmcounter17 = 0xc11
  val hpmcounter18 = 0xc12
  val hpmcounter19 = 0xc13
  val hpmcounter20 = 0xc14
  val hpmcounter21 = 0xc15
  val hpmcounter22 = 0xc16
  val hpmcounter23 = 0xc17
  val hpmcounter24 = 0xc18
  val hpmcounter25 = 0xc19
  val hpmcounter26 = 0xc1a
  val hpmcounter27 = 0xc1b
  val hpmcounter28 = 0xc1c
  val hpmcounter29 = 0xc1d
  val hpmcounter30 = 0xc1e
  val hpmcounter31 = 0xc1f
  val vl = 0xc20
  val vtype = 0xc21
  val vlenb = 0xc22
  val sstatus = 0x100
  val sedeleg = 0x102
  val sideleg = 0x103
  val sie = 0x104
  val stvec = 0x105
  val scounteren = 0x106
  val senvcfg = 0x10a
  val sstateen0 = 0x10c
  val sstateen1 = 0x10d
  val sstateen2 = 0x10e
  val sstateen3 = 0x10f
  val sscratch = 0x140
  val sepc = 0x141
  val scause = 0x142
  val stval = 0x143
  val sip = 0x144
  val stimecmp = 0x14d
  val siselect = 0x150
  val sireg = 0x151
  val stopei = 0x15c
  val satp = 0x180
  val scontext = 0x5a8
  val vsstatus = 0x200
  val vsie = 0x204
  val vstvec = 0x205
  val vsscratch = 0x240
  val vsepc = 0x241
  val vscause = 0x242
  val vstval = 0x243
  val vsip = 0x244
  val vstimecmp = 0x24d
  val vsiselect = 0x250
  val vsireg = 0x251
  val vstopei = 0x25c
  val vsatp = 0x280
  val hstatus = 0x600
  val hedeleg = 0x602
  val hideleg = 0x603
  val hie = 0x604
  val htimedelta = 0x605
  val hcounteren = 0x606
  val hgeie = 0x607
  val hvien = 0x608
  val hvictl = 0x609
  val henvcfg = 0x60a
  val hstateen0 = 0x60c
  val hstateen1 = 0x60d
  val hstateen2 = 0x60e
  val hstateen3 = 0x60f
  val htval = 0x643
  val hip = 0x644
  val hvip = 0x645
  val hviprio1 = 0x646
  val hviprio2 = 0x647
  val htinst = 0x64a
  val hgatp = 0x680
  val hcontext = 0x6a8
  val hgeip = 0xe12
  val vstopi = 0xeb0
  val scountovf = 0xda0
  val stopi = 0xdb0
  val utvt = 0x7
  val unxti = 0x45
  val uintstatus = 0x46
  val uscratchcsw = 0x48
  val uscratchcswl = 0x49
  val stvt = 0x107
  val snxti = 0x145
  val sintstatus = 0x146
  val sscratchcsw = 0x148
  val sscratchcswl = 0x149
  val mtvt = 0x307
  val mnxti = 0x345
  val mintstatus = 0x346
  val mscratchcsw = 0x348
  val mscratchcswl = 0x349
  val mstatus = 0x300
  val misa = 0x301
  val medeleg = 0x302
  val mideleg = 0x303
  val mie = 0x304
  val mtvec = 0x305
  val mcounteren = 0x306
  val mvien = 0x308
  val mvip = 0x309
  val menvcfg = 0x30a
  val mstateen0 = 0x30c
  val mstateen1 = 0x30d
  val mstateen2 = 0x30e
  val mstateen3 = 0x30f
  val mcountinhibit = 0x320
  val mscratch = 0x340
  val mepc = 0x341
  val mcause = 0x342
  val mtval = 0x343
  val mip = 0x344
  val mtinst = 0x34a
  val mtval2 = 0x34b
  val miselect = 0x350
  val mireg = 0x351
  val mtopei = 0x35c
  val pmpcfg0 = 0x3a0
  val pmpcfg1 = 0x3a1
  val pmpcfg2 = 0x3a2
  val pmpcfg3 = 0x3a3
  val pmpcfg4 = 0x3a4
  val pmpcfg5 = 0x3a5
  val pmpcfg6 = 0x3a6
  val pmpcfg7 = 0x3a7
  val pmpcfg8 = 0x3a8
  val pmpcfg9 = 0x3a9
  val pmpcfg10 = 0x3aa
  val pmpcfg11 = 0x3ab
  val pmpcfg12 = 0x3ac
  val pmpcfg13 = 0x3ad
  val pmpcfg14 = 0x3ae
  val pmpcfg15 = 0x3af
  val pmpaddr0 = 0x3b0
  val pmpaddr1 = 0x3b1
  val pmpaddr2 = 0x3b2
  val pmpaddr3 = 0x3b3
  val pmpaddr4 = 0x3b4
  val pmpaddr5 = 0x3b5
  val pmpaddr6 = 0x3b6
  val pmpaddr7 = 0x3b7
  val pmpaddr8 = 0x3b8
  val pmpaddr9 = 0x3b9
  val pmpaddr10 = 0x3ba
  val pmpaddr11 = 0x3bb
  val pmpaddr12 = 0x3bc
  val pmpaddr13 = 0x3bd
  val pmpaddr14 = 0x3be
  val pmpaddr15 = 0x3bf
  val pmpaddr16 = 0x3c0
  val pmpaddr17 = 0x3c1
  val pmpaddr18 = 0x3c2
  val pmpaddr19 = 0x3c3
  val pmpaddr20 = 0x3c4
  val pmpaddr21 = 0x3c5
  val pmpaddr22 = 0x3c6
  val pmpaddr23 = 0x3c7
  val pmpaddr24 = 0x3c8
  val pmpaddr25 = 0x3c9
  val pmpaddr26 = 0x3ca
  val pmpaddr27 = 0x3cb
  val pmpaddr28 = 0x3cc
  val pmpaddr29 = 0x3cd
  val pmpaddr30 = 0x3ce
  val pmpaddr31 = 0x3cf
  val pmpaddr32 = 0x3d0
  val pmpaddr33 = 0x3d1
  val pmpaddr34 = 0x3d2
  val pmpaddr35 = 0x3d3
  val pmpaddr36 = 0x3d4
  val pmpaddr37 = 0x3d5
  val pmpaddr38 = 0x3d6
  val pmpaddr39 = 0x3d7
  val pmpaddr40 = 0x3d8
  val pmpaddr41 = 0x3d9
  val pmpaddr42 = 0x3da
  val pmpaddr43 = 0x3db
  val pmpaddr44 = 0x3dc
  val pmpaddr45 = 0x3dd
  val pmpaddr46 = 0x3de
  val pmpaddr47 = 0x3df
  val pmpaddr48 = 0x3e0
  val pmpaddr49 = 0x3e1
  val pmpaddr50 = 0x3e2
  val pmpaddr51 = 0x3e3
  val pmpaddr52 = 0x3e4
  val pmpaddr53 = 0x3e5
  val pmpaddr54 = 0x3e6
  val pmpaddr55 = 0x3e7
  val pmpaddr56 = 0x3e8
  val pmpaddr57 = 0x3e9
  val pmpaddr58 = 0x3ea
  val pmpaddr59 = 0x3eb
  val pmpaddr60 = 0x3ec
  val pmpaddr61 = 0x3ed
  val pmpaddr62 = 0x3ee
  val pmpaddr63 = 0x3ef
  val mseccfg = 0x747
  val tselect = 0x7a0
  val tdata1 = 0x7a1
  val tdata2 = 0x7a2
  val tdata3 = 0x7a3
  val tinfo = 0x7a4
  val tcontrol = 0x7a5
  val mcontext = 0x7a8
  val mscontext = 0x7aa
  val dcsr = 0x7b0
  val dpc = 0x7b1
  val dscratch0 = 0x7b2
  val dscratch1 = 0x7b3
  val mcycle = 0xb00
  val minstret = 0xb02
  val mhpmcounter3 = 0xb03
  val mhpmcounter4 = 0xb04
  val mhpmcounter5 = 0xb05
  val mhpmcounter6 = 0xb06
  val mhpmcounter7 = 0xb07
  val mhpmcounter8 = 0xb08
  val mhpmcounter9 = 0xb09
  val mhpmcounter10 = 0xb0a
  val mhpmcounter11 = 0xb0b
  val mhpmcounter12 = 0xb0c
  val mhpmcounter13 = 0xb0d
  val mhpmcounter14 = 0xb0e
  val mhpmcounter15 = 0xb0f
  val mhpmcounter16 = 0xb10
  val mhpmcounter17 = 0xb11
  val mhpmcounter18 = 0xb12
  val mhpmcounter19 = 0xb13
  val mhpmcounter20 = 0xb14
  val mhpmcounter21 = 0xb15
  val mhpmcounter22 = 0xb16
  val mhpmcounter23 = 0xb17
  val mhpmcounter24 = 0xb18
  val mhpmcounter25 = 0xb19
  val mhpmcounter26 = 0xb1a
  val mhpmcounter27 = 0xb1b
  val mhpmcounter28 = 0xb1c
  val mhpmcounter29 = 0xb1d
  val mhpmcounter30 = 0xb1e
  val mhpmcounter31 = 0xb1f
  val mhpmevent3 = 0x323
  val mhpmevent4 = 0x324
  val mhpmevent5 = 0x325
  val mhpmevent6 = 0x326
  val mhpmevent7 = 0x327
  val mhpmevent8 = 0x328
  val mhpmevent9 = 0x329
  val mhpmevent10 = 0x32a
  val mhpmevent11 = 0x32b
  val mhpmevent12 = 0x32c
  val mhpmevent13 = 0x32d
  val mhpmevent14 = 0x32e
  val mhpmevent15 = 0x32f
  val mhpmevent16 = 0x330
  val mhpmevent17 = 0x331
  val mhpmevent18 = 0x332
  val mhpmevent19 = 0x333
  val mhpmevent20 = 0x334
  val mhpmevent21 = 0x335
  val mhpmevent22 = 0x336
  val mhpmevent23 = 0x337
  val mhpmevent24 = 0x338
  val mhpmevent25 = 0x339
  val mhpmevent26 = 0x33a
  val mhpmevent27 = 0x33b
  val mhpmevent28 = 0x33c
  val mhpmevent29 = 0x33d
  val mhpmevent30 = 0x33e
  val mhpmevent31 = 0x33f
  val mvendorid = 0xf11
  val marchid = 0xf12
  val mimpid = 0xf13
  val mhartid = 0xf14
  val mconfigptr = 0xf15
  val mtopi = 0xfb0
  val sieh = 0x114
  val siph = 0x154
  val stimecmph = 0x15d
  val vsieh = 0x214
  val vsiph = 0x254
  val vstimecmph = 0x25d
  val htimedeltah = 0x615
  val hidelegh = 0x613
  val hvienh = 0x618
  val henvcfgh = 0x61a
  val hviph = 0x655
  val hviprio1h = 0x656
  val hviprio2h = 0x657
  val hstateen0h = 0x61c
  val hstateen1h = 0x61d
  val hstateen2h = 0x61e
  val hstateen3h = 0x61f
  val cycleh = 0xc80
  val timeh = 0xc81
  val instreth = 0xc82
  val hpmcounter3h = 0xc83
  val hpmcounter4h = 0xc84
  val hpmcounter5h = 0xc85
  val hpmcounter6h = 0xc86
  val hpmcounter7h = 0xc87
  val hpmcounter8h = 0xc88
  val hpmcounter9h = 0xc89
  val hpmcounter10h = 0xc8a
  val hpmcounter11h = 0xc8b
  val hpmcounter12h = 0xc8c
  val hpmcounter13h = 0xc8d
  val hpmcounter14h = 0xc8e
  val hpmcounter15h = 0xc8f
  val hpmcounter16h = 0xc90
  val hpmcounter17h = 0xc91
  val hpmcounter18h = 0xc92
  val hpmcounter19h = 0xc93
  val hpmcounter20h = 0xc94
  val hpmcounter21h = 0xc95
  val hpmcounter22h = 0xc96
  val hpmcounter23h = 0xc97
  val hpmcounter24h = 0xc98
  val hpmcounter25h = 0xc99
  val hpmcounter26h = 0xc9a
  val hpmcounter27h = 0xc9b
  val hpmcounter28h = 0xc9c
  val hpmcounter29h = 0xc9d
  val hpmcounter30h = 0xc9e
  val hpmcounter31h = 0xc9f
  val mstatush = 0x310
  val midelegh = 0x313
  val mieh = 0x314
  val mvienh = 0x318
  val mviph = 0x319
  val menvcfgh = 0x31a
  val mstateen0h = 0x31c
  val mstateen1h = 0x31d
  val mstateen2h = 0x31e
  val mstateen3h = 0x31f
  val miph = 0x354
  val mhpmevent3h = 0x723
  val mhpmevent4h = 0x724
  val mhpmevent5h = 0x725
  val mhpmevent6h = 0x726
  val mhpmevent7h = 0x727
  val mhpmevent8h = 0x728
  val mhpmevent9h = 0x729
  val mhpmevent10h = 0x72a
  val mhpmevent11h = 0x72b
  val mhpmevent12h = 0x72c
  val mhpmevent13h = 0x72d
  val mhpmevent14h = 0x72e
  val mhpmevent15h = 0x72f
  val mhpmevent16h = 0x730
  val mhpmevent17h = 0x731
  val mhpmevent18h = 0x732
  val mhpmevent19h = 0x733
  val mhpmevent20h = 0x734
  val mhpmevent21h = 0x735
  val mhpmevent22h = 0x736
  val mhpmevent23h = 0x737
  val mhpmevent24h = 0x738
  val mhpmevent25h = 0x739
  val mhpmevent26h = 0x73a
  val mhpmevent27h = 0x73b
  val mhpmevent28h = 0x73c
  val mhpmevent29h = 0x73d
  val mhpmevent30h = 0x73e
  val mhpmevent31h = 0x73f
  val mnscratch = 0x740
  val mnepc = 0x741
  val mncause = 0x742
  val mnstatus = 0x744
  val mseccfgh = 0x757
  val mcycleh = 0xb80
  val minstreth = 0xb82
  val mhpmcounter3h = 0xb83
  val mhpmcounter4h = 0xb84
  val mhpmcounter5h = 0xb85
  val mhpmcounter6h = 0xb86
  val mhpmcounter7h = 0xb87
  val mhpmcounter8h = 0xb88
  val mhpmcounter9h = 0xb89
  val mhpmcounter10h = 0xb8a
  val mhpmcounter11h = 0xb8b
  val mhpmcounter12h = 0xb8c
  val mhpmcounter13h = 0xb8d
  val mhpmcounter14h = 0xb8e
  val mhpmcounter15h = 0xb8f
  val mhpmcounter16h = 0xb90
  val mhpmcounter17h = 0xb91
  val mhpmcounter18h = 0xb92
  val mhpmcounter19h = 0xb93
  val mhpmcounter20h = 0xb94
  val mhpmcounter21h = 0xb95
  val mhpmcounter22h = 0xb96
  val mhpmcounter23h = 0xb97
  val mhpmcounter24h = 0xb98
  val mhpmcounter25h = 0xb99
  val mhpmcounter26h = 0xb9a
  val mhpmcounter27h = 0xb9b
  val mhpmcounter28h = 0xb9c
  val mhpmcounter29h = 0xb9d
  val mhpmcounter30h = 0xb9e
  val mhpmcounter31h = 0xb9f
  val all = {
    val res = collection.mutable.ArrayBuffer[Int]()
    res += fflags
    res += frm
    res += fcsr
    res += vstart
    res += vxsat
    res += vxrm
    res += vcsr
    res += seed
    res += jvt
    res += cycle
    res += time
    res += instret
    res += hpmcounter3
    res += hpmcounter4
    res += hpmcounter5
    res += hpmcounter6
    res += hpmcounter7
    res += hpmcounter8
    res += hpmcounter9
    res += hpmcounter10
    res += hpmcounter11
    res += hpmcounter12
    res += hpmcounter13
    res += hpmcounter14
    res += hpmcounter15
    res += hpmcounter16
    res += hpmcounter17
    res += hpmcounter18
    res += hpmcounter19
    res += hpmcounter20
    res += hpmcounter21
    res += hpmcounter22
    res += hpmcounter23
    res += hpmcounter24
    res += hpmcounter25
    res += hpmcounter26
    res += hpmcounter27
    res += hpmcounter28
    res += hpmcounter29
    res += hpmcounter30
    res += hpmcounter31
    res += vl
    res += vtype
    res += vlenb
    res += sstatus
    res += sedeleg
    res += sideleg
    res += sie
    res += stvec
    res += scounteren
    res += senvcfg
    res += sstateen0
    res += sstateen1
    res += sstateen2
    res += sstateen3
    res += sscratch
    res += sepc
    res += scause
    res += stval
    res += sip
    res += stimecmp
    res += siselect
    res += sireg
    res += stopei
    res += satp
    res += scontext
    res += vsstatus
    res += vsie
    res += vstvec
    res += vsscratch
    res += vsepc
    res += vscause
    res += vstval
    res += vsip
    res += vstimecmp
    res += vsiselect
    res += vsireg
    res += vstopei
    res += vsatp
    res += hstatus
    res += hedeleg
    res += hideleg
    res += hie
    res += htimedelta
    res += hcounteren
    res += hgeie
    res += hvien
    res += hvictl
    res += henvcfg
    res += hstateen0
    res += hstateen1
    res += hstateen2
    res += hstateen3
    res += htval
    res += hip
    res += hvip
    res += hviprio1
    res += hviprio2
    res += htinst
    res += hgatp
    res += hcontext
    res += hgeip
    res += vstopi
    res += scountovf
    res += stopi
    res += utvt
    res += unxti
    res += uintstatus
    res += uscratchcsw
    res += uscratchcswl
    res += stvt
    res += snxti
    res += sintstatus
    res += sscratchcsw
    res += sscratchcswl
    res += mtvt
    res += mnxti
    res += mintstatus
    res += mscratchcsw
    res += mscratchcswl
    res += mstatus
    res += misa
    res += medeleg
    res += mideleg
    res += mie
    res += mtvec
    res += mcounteren
    res += mvien
    res += mvip
    res += menvcfg
    res += mstateen0
    res += mstateen1
    res += mstateen2
    res += mstateen3
    res += mcountinhibit
    res += mscratch
    res += mepc
    res += mcause
    res += mtval
    res += mip
    res += mtinst
    res += mtval2
    res += miselect
    res += mireg
    res += mtopei
    res += pmpcfg0
    res += pmpcfg1
    res += pmpcfg2
    res += pmpcfg3
    res += pmpcfg4
    res += pmpcfg5
    res += pmpcfg6
    res += pmpcfg7
    res += pmpcfg8
    res += pmpcfg9
    res += pmpcfg10
    res += pmpcfg11
    res += pmpcfg12
    res += pmpcfg13
    res += pmpcfg14
    res += pmpcfg15
    res += pmpaddr0
    res += pmpaddr1
    res += pmpaddr2
    res += pmpaddr3
    res += pmpaddr4
    res += pmpaddr5
    res += pmpaddr6
    res += pmpaddr7
    res += pmpaddr8
    res += pmpaddr9
    res += pmpaddr10
    res += pmpaddr11
    res += pmpaddr12
    res += pmpaddr13
    res += pmpaddr14
    res += pmpaddr15
    res += pmpaddr16
    res += pmpaddr17
    res += pmpaddr18
    res += pmpaddr19
    res += pmpaddr20
    res += pmpaddr21
    res += pmpaddr22
    res += pmpaddr23
    res += pmpaddr24
    res += pmpaddr25
    res += pmpaddr26
    res += pmpaddr27
    res += pmpaddr28
    res += pmpaddr29
    res += pmpaddr30
    res += pmpaddr31
    res += pmpaddr32
    res += pmpaddr33
    res += pmpaddr34
    res += pmpaddr35
    res += pmpaddr36
    res += pmpaddr37
    res += pmpaddr38
    res += pmpaddr39
    res += pmpaddr40
    res += pmpaddr41
    res += pmpaddr42
    res += pmpaddr43
    res += pmpaddr44
    res += pmpaddr45
    res += pmpaddr46
    res += pmpaddr47
    res += pmpaddr48
    res += pmpaddr49
    res += pmpaddr50
    res += pmpaddr51
    res += pmpaddr52
    res += pmpaddr53
    res += pmpaddr54
    res += pmpaddr55
    res += pmpaddr56
    res += pmpaddr57
    res += pmpaddr58
    res += pmpaddr59
    res += pmpaddr60
    res += pmpaddr61
    res += pmpaddr62
    res += pmpaddr63
    res += mseccfg
    res += tselect
    res += tdata1
    res += tdata2
    res += tdata3
    res += tinfo
    res += tcontrol
    res += mcontext
    res += mscontext
    res += dcsr
    res += dpc
    res += dscratch0
    res += dscratch1
    res += mcycle
    res += minstret
    res += mhpmcounter3
    res += mhpmcounter4
    res += mhpmcounter5
    res += mhpmcounter6
    res += mhpmcounter7
    res += mhpmcounter8
    res += mhpmcounter9
    res += mhpmcounter10
    res += mhpmcounter11
    res += mhpmcounter12
    res += mhpmcounter13
    res += mhpmcounter14
    res += mhpmcounter15
    res += mhpmcounter16
    res += mhpmcounter17
    res += mhpmcounter18
    res += mhpmcounter19
    res += mhpmcounter20
    res += mhpmcounter21
    res += mhpmcounter22
    res += mhpmcounter23
    res += mhpmcounter24
    res += mhpmcounter25
    res += mhpmcounter26
    res += mhpmcounter27
    res += mhpmcounter28
    res += mhpmcounter29
    res += mhpmcounter30
    res += mhpmcounter31
    res += mhpmevent3
    res += mhpmevent4
    res += mhpmevent5
    res += mhpmevent6
    res += mhpmevent7
    res += mhpmevent8
    res += mhpmevent9
    res += mhpmevent10
    res += mhpmevent11
    res += mhpmevent12
    res += mhpmevent13
    res += mhpmevent14
    res += mhpmevent15
    res += mhpmevent16
    res += mhpmevent17
    res += mhpmevent18
    res += mhpmevent19
    res += mhpmevent20
    res += mhpmevent21
    res += mhpmevent22
    res += mhpmevent23
    res += mhpmevent24
    res += mhpmevent25
    res += mhpmevent26
    res += mhpmevent27
    res += mhpmevent28
    res += mhpmevent29
    res += mhpmevent30
    res += mhpmevent31
    res += mvendorid
    res += marchid
    res += mimpid
    res += mhartid
    res += mconfigptr
    res += mtopi
    res.toArray
  }
  val all32 = {
    val res = collection.mutable.ArrayBuffer(all:_*)
    res += sieh
    res += siph
    res += stimecmph
    res += vsieh
    res += vsiph
    res += vstimecmph
    res += htimedeltah
    res += hidelegh
    res += hvienh
    res += henvcfgh
    res += hviph
    res += hviprio1h
    res += hviprio2h
    res += hstateen0h
    res += hstateen1h
    res += hstateen2h
    res += hstateen3h
    res += cycleh
    res += timeh
    res += instreth
    res += hpmcounter3h
    res += hpmcounter4h
    res += hpmcounter5h
    res += hpmcounter6h
    res += hpmcounter7h
    res += hpmcounter8h
    res += hpmcounter9h
    res += hpmcounter10h
    res += hpmcounter11h
    res += hpmcounter12h
    res += hpmcounter13h
    res += hpmcounter14h
    res += hpmcounter15h
    res += hpmcounter16h
    res += hpmcounter17h
    res += hpmcounter18h
    res += hpmcounter19h
    res += hpmcounter20h
    res += hpmcounter21h
    res += hpmcounter22h
    res += hpmcounter23h
    res += hpmcounter24h
    res += hpmcounter25h
    res += hpmcounter26h
    res += hpmcounter27h
    res += hpmcounter28h
    res += hpmcounter29h
    res += hpmcounter30h
    res += hpmcounter31h
    res += mstatush
    res += midelegh
    res += mieh
    res += mvienh
    res += mviph
    res += menvcfgh
    res += mstateen0h
    res += mstateen1h
    res += mstateen2h
    res += mstateen3h
    res += miph
    res += mhpmevent3h
    res += mhpmevent4h
    res += mhpmevent5h
    res += mhpmevent6h
    res += mhpmevent7h
    res += mhpmevent8h
    res += mhpmevent9h
    res += mhpmevent10h
    res += mhpmevent11h
    res += mhpmevent12h
    res += mhpmevent13h
    res += mhpmevent14h
    res += mhpmevent15h
    res += mhpmevent16h
    res += mhpmevent17h
    res += mhpmevent18h
    res += mhpmevent19h
    res += mhpmevent20h
    res += mhpmevent21h
    res += mhpmevent22h
    res += mhpmevent23h
    res += mhpmevent24h
    res += mhpmevent25h
    res += mhpmevent26h
    res += mhpmevent27h
    res += mhpmevent28h
    res += mhpmevent29h
    res += mhpmevent30h
    res += mhpmevent31h
    res += mnscratch
    res += mnepc
    res += mncause
    res += mnstatus
    res += mseccfgh
    res += mcycleh
    res += minstreth
    res += mhpmcounter3h
    res += mhpmcounter4h
    res += mhpmcounter5h
    res += mhpmcounter6h
    res += mhpmcounter7h
    res += mhpmcounter8h
    res += mhpmcounter9h
    res += mhpmcounter10h
    res += mhpmcounter11h
    res += mhpmcounter12h
    res += mhpmcounter13h
    res += mhpmcounter14h
    res += mhpmcounter15h
    res += mhpmcounter16h
    res += mhpmcounter17h
    res += mhpmcounter18h
    res += mhpmcounter19h
    res += mhpmcounter20h
    res += mhpmcounter21h
    res += mhpmcounter22h
    res += mhpmcounter23h
    res += mhpmcounter24h
    res += mhpmcounter25h
    res += mhpmcounter26h
    res += mhpmcounter27h
    res += mhpmcounter28h
    res += mhpmcounter29h
    res += mhpmcounter30h
    res += mhpmcounter31h
    res.toArray
  }
}
