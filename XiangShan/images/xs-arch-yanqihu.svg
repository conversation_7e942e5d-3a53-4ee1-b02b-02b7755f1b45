<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="3118px" height="3033px" viewBox="-0.5 -0.5 3118 3033" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2021-06-08T14:44:28.072Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/13.3.1 Chrome/83.0.4103.119 Electron/9.0.5 Safari/537.36&quot; etag=&quot;wM582Ls5sY21x1f_Bkiy&quot; version=&quot;13.3.1&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;xs-整体微结构-多彩版&quot; id=&quot;xSjQYj8qTl4sxZwAIWEt&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro);&#xa;</style></defs><g><rect x="402" y="2250" width="792" height="210" rx="31.5" ry="31.5" fill="#ffe6cc" stroke="none" pointer-events="all"/><rect x="1222.89" y="1605" width="660" height="645" rx="96.75" ry="96.75" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="1222.89" y="2010" width="661.11" height="630" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="564" y="2490" width="1320" height="540" rx="81" ry="81" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="1914" y="1620" width="1200" height="600" rx="90" ry="90" fill="#dae8fc" stroke="none" pointer-events="all"/><rect x="3" y="1620" width="1191" height="600" rx="90" ry="90" fill="#f8cecc" stroke="none" pointer-events="all"/><rect x="0" y="1042.5" width="3114" height="547.5" rx="82.13" ry="82.13" fill="#e1d5e7" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1036px; height: 1px; padding-top: 439px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><br /><br /><br /><br /><br /></div></div></div></foreignObject><text x="2" y="444" fill="#000000" font-family="Helvetica" font-size="18px">...</text></switch></g><rect x="3" y="0" width="3111" height="1020" rx="153" ry="153" fill="#d5e8d4" stroke="none" pointer-events="all"/><path d="M 928.5 330 L 963 330 L 963 231.18 L 1168.9 231.18" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1184.65 231.18 L 1163.65 241.68 L 1168.9 231.18 L 1163.65 220.68 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="783" y="180" width="291" height="150" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 95px; height: 1px; padding-top: 85px; margin-left: 262px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 14px"><span style="font-size: 14px">ITLB</span><br style="font-size: 14px" /><font style="font-size: 14px">32 entries</font><br style="font-size: 14px" /></font></div></div></div></foreignObject><text x="310" y="89" fill="#000000" font-family="Helvetica" font-size="14px" text-anchor="middle">ITLB...</text></switch></g><path d="M 1411.5 557.22 L 1411.59 630 L 1563 630 L 1563 670.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1563 686.65 L 1552.5 665.65 L 1563 670.9 L 1573.5 665.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1411.5 557.22 L 1411.59 630 L 765 630 L 765 790.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 765 806.65 L 754.5 785.65 L 765 790.9 L 775.5 785.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1188" y="180" width="447" height="377.22" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 147px; height: 1px; padding-top: 123px; margin-left: 397px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">Instruction</span><br /><span style="font-size: 20px">Cache</span><br /><font><font style="font-size: 16px"><br />16 KB</font><br /><span style="font-size: 16px">4-way</span><br /></font></font></div></div></div></foreignObject><text x="471" y="126" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction...</text></switch></g><path d="M 1569 847.5 L 1563 847.41 L 1563 880.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1563 896.65 L 1552.5 875.65 L 1563 880.9 L 1573.5 875.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1104" y="690" width="930" height="157.5" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 256px; margin-left: 369px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px">Instruction Buffer</font><br /><font style="font-size: 16px">48 entries</font></font></div></div></div></foreignObject><text x="523" y="260" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction Buffer...</text></switch></g><path d="M 1563 990 L 1563 1090.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1563 1106.65 L 1552.5 1085.65 L 1563 1090.9 L 1573.5 1085.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1096.5" y="900" width="933" height="90" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 309px; height: 1px; padding-top: 315px; margin-left: 367px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">6-way Decoder</font></div></div></div></foreignObject><text x="521" y="319" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">6-way Decoder</text></switch></g><path d="M 1635 1837.5 L 1635 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1635 1931.65 L 1624.5 1910.65 L 1635 1915.9 L 1645.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1575" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 526px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ST<br />RS0</div></div></div></foreignObject><text x="545" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ST...</text></switch></g><path d="M 1788 1837.5 L 1788 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1788 1931.65 L 1777.5 1910.65 L 1788 1915.9 L 1798.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1728" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 577px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ST<br />RS1</div></div></div></foreignObject><text x="596" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ST...</text></switch></g><path d="M 2301 1837.5 L 2301 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2301 1931.65 L 2290.5 1910.65 L 2301 1915.9 L 2311.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2241" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 748px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU<br />RS0</div></div></div></foreignObject><text x="767" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU...</text></switch></g><path d="M 2454 1837.5 L 2454 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2454 1931.65 L 2443.5 1910.65 L 2454 1915.9 L 2464.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2394" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 799px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU<br />RS1</div></div></div></foreignObject><text x="818" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU...</text></switch></g><path d="M 2607 1837.5 L 2607 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2607 1931.65 L 2596.5 1910.65 L 2607 1915.9 L 2617.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2547" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 850px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU<br />RS2</div></div></div></foreignObject><text x="869" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU...</text></switch></g><path d="M 2148 1837.5 L 2148 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2148 1931.65 L 2137.5 1910.65 L 2148 1915.9 L 2158.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2088" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 697px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MDU<br />RS1</div></div></div></foreignObject><text x="716" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU...</text></switch></g><path d="M 1635 2055 L 1635 2400 L 1635 2410.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1635 2426.65 L 1624.5 2405.65 L 1635 2410.9 L 1645.5 2405.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1575" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 526px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">AGU<br />ST</div></div></div></foreignObject><text x="545" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 1788 2055 L 1788.18 2430 L 1788 2410.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1788 2426.65 L 1777.5 2405.65 L 1788 2410.9 L 1798.5 2405.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1728" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 577px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">AGU<br />ST</div></div></div></foreignObject><text x="596" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 2301 2055 L 2301 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2241" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 748px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU</div></div></div></foreignObject><text x="767" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2454 2055 L 2454.18 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2394" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 799px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU</div></div></div></foreignObject><text x="818" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2607 2055 L 2607 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2547" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 850px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU</div></div></div></foreignObject><text x="869" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2148 2055 L 2148.18 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2088" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 697px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MDU</div></div></div></foreignObject><text x="716" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU</text></switch></g><path d="M 2421 2190 L 3054.18 2190 L 3054.18 1777.41 L 3022.1 1777.47" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3006.35 1777.49 L 3027.34 1766.96 L 3022.1 1777.47 L 3027.37 1787.96 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2421 2190 L 3054.18 2190 L 3054.18 1455.18 L 3022.1 1455.07" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3006.35 1455.01 L 3027.39 1444.58 L 3022.1 1455.07 L 3027.32 1465.58 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1203" y="1170" width="480" height="390" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 455px; margin-left: 402px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">ReOrder Buffer</span><br /><font style="font-size: 16px">192 entires</font></font></div></div></div></foreignObject><text x="481" y="459" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ReOrder Buffer...</text></switch></g><rect x="1287" y="2430" width="270" height="240" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 850px; margin-left: 430px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px">Load Queue</font><br /><font style="font-size: 14px">64 entries</font></font></div></div></div></foreignObject><text x="474" y="854" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Load Queue...</text></switch></g><path d="M 823.12 2316 L 823.24 2280 L 1284.18 2280 L 1284.05 2233.1" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1284.01 2217.35 L 1294.57 2238.33 L 1284.05 2233.1 L 1273.57 2238.38 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="602.25" y="2316" width="441.75" height="120" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 145px; height: 1px; padding-top: 792px; margin-left: 202px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px"><b>STLB &amp; PTW</b></font><br /><font style="font-size: 16px">4096 entries</font><br /></font></div></div></div></foreignObject><text x="274" y="796" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">STLB &amp; PTW...</text></switch></g><path d="M 2297.61 120 L 2308.41 120 L 2308.47 154.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2308.49 170.65 L 2297.96 149.66 L 2308.47 154.9 L 2318.96 149.63 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1749" y="174" width="1119" height="480" fill="#ffe6cc" stroke="#d79b00" stroke-width="3" pointer-events="all"/><rect x="2305.41" y="517.41" width="463.68" height="106.59" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 190px; margin-left: 769px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">RAS<br /></font></div></div></div></foreignObject><text x="846" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">RAS&#xa;</text></switch></g><rect x="2305.41" y="234" width="463.68" height="89.07" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 93px; margin-left: 769px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">uBTB</font></div></div></div></foreignObject><text x="846" y="96" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">uBTB</text></switch></g><rect x="1841.73" y="357.34" width="466.77" height="122.51" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 154px; height: 1px; padding-top: 140px; margin-left: 615px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">BTB</span><br /></font></div></div></div></foreignObject><text x="692" y="143" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">BTB&#xa;</text></switch></g><rect x="1844.82" y="517.41" width="460.59" height="106.59" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 152px; height: 1px; padding-top: 190px; margin-left: 616px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">TAGE-SC-L</font></div></div></div></foreignObject><text x="692" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">TAGE-SC-L</text></switch></g><rect x="2308.5" y="357.34" width="460.58" height="122.51" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 152px; height: 1px; padding-top: 140px; margin-left: 771px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">BIM</font></div></div></div></foreignObject><text x="846" y="143" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">BIM</text></switch></g><rect x="1758.01" y="155.61" width="401.85" height="78.4" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 132px; height: 1px; padding-top: 59px; margin-left: 588px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><span style="color: rgb(0 , 0 , 0) ; font-style: normal ; font-weight: 400 ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; display: inline ; float: none"><font style="font-size: 16px">Branch Predictor</font></span></div></div></div></foreignObject><text x="588" y="71" fill="#000000" font-family="Helvetica" font-size="12px">Branch Predictor</text></switch></g><rect x="1841.73" y="234" width="463.68" height="89.07" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 93px; margin-left: 615px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">GHR</font></div></div></div></foreignObject><text x="691" y="96" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">GHR</text></switch></g><rect x="474" y="810" width="579" height="180" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 191px; height: 1px; padding-top: 300px; margin-left: 159px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px">Fetch Target Queue</font><br /><font style="font-size: 16px">48 entries</font></font></div></div></div></foreignObject><text x="255" y="304" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Fetch Target Queue...</text></switch></g><rect x="1726.5" y="1350" width="1276.5" height="210" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 424px; height: 1px; padding-top: 485px; margin-left: 577px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">INT Physical Register File</span><br /><font style="font-size: 16px">160 entries</font></font></div></div></div></foreignObject><text x="788" y="489" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">INT Physical Register File...</text></switch></g><rect x="1726.5" y="1170" width="706.5" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 233px; height: 1px; padding-top: 415px; margin-left: 577px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">Integer Rename Table</font></div></div></div></foreignObject><text x="693" y="419" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Integer Rename Table</text></switch></g><rect x="453" y="1170" width="703.5" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 233px; height: 1px; padding-top: 415px; margin-left: 152px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">FP Rename Table</font></div></div></div></foreignObject><text x="268" y="419" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FP Rename Table</text></switch></g><rect x="183" y="1350" width="973.5" height="210" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 323px; height: 1px; padding-top: 485px; margin-left: 62px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">FP Physical Register File</span><br /><font style="font-size: 16px">160 entries</font></font></div></div></div></foreignObject><text x="223" y="489" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FP Physical Register File...</text></switch></g><path d="M 2760 1837.5 L 2760 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2760 1931.65 L 2749.5 1910.65 L 2760 1915.9 L 2770.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2700" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 901px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU<br />RS3</div></div></div></foreignObject><text x="920" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU...</text></switch></g><path d="M 2760 2055 L 2760.18 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2700" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 901px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">ALU</div></div></div></foreignObject><text x="920" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 1482 1837.5 L 1482 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1482 1931.65 L 1471.5 1910.65 L 1482 1915.9 L 1492.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1422" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 475px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">LD<br />RS1</div></div></div></foreignObject><text x="494" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">LD...</text></switch></g><path d="M 1482 2055 L 1482.18 2430 L 1482 2410.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1482 2426.65 L 1471.5 2405.65 L 1482 2410.9 L 1492.5 2405.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1422" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 475px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">AGU<br />LD</div></div></div></foreignObject><text x="494" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 1329 1837.5 L 1329 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1329 1931.65 L 1318.5 1910.65 L 1329 1915.9 L 1339.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1269" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 424px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">LD<br />RS0</div></div></div></foreignObject><text x="443" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">LD...</text></switch></g><rect x="1269" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 424px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">AGU<br />LD</div></div></div></foreignObject><text x="443" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 1995 1837.5 L 1995 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1995 1931.65 L 1984.5 1910.65 L 1995 1915.9 L 2005.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1935" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 646px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MDU<br />RS0</div></div></div></foreignObject><text x="665" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU...</text></switch></g><path d="M 1995 2055 L 1995 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1935" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 646px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MDU</div></div></div></foreignObject><text x="665" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU</text></switch></g><path d="M 2913 1837.5 L 2913 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2913 1931.65 L 2902.5 1910.65 L 2913 1915.9 L 2923.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2853" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 952px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MISC<br />RS</div></div></div></foreignObject><text x="971" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MISC...</text></switch></g><path d="M 2913 2055 L 2913 2123.29 L 2419.59 2123.29 L 2419.52 2172.74" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2419.5 2188.49 L 2409.03 2167.48 L 2419.52 2172.74 L 2430.03 2167.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2853" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 952px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MISC</div></div></div></foreignObject><text x="971" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MISC</text></switch></g><path d="M 1719 2670 L 1719 2700 L 1515 2700 L 1515 2745.18 L 1515.54 2725.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1515.87 2741.65 L 1504.94 2720.87 L 1515.54 2725.9 L 1525.93 2720.43 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1584" y="2430" width="270" height="240" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 850px; margin-left: 529px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px">Store Queue</font><br /><font style="font-size: 14px">48 entries</font></font></div></div></div></foreignObject><text x="573" y="854" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Store Queue...</text></switch></g><path d="M 1515.94 2895 L 1503 2895.18 L 1503 2937.88 L 1116.59 2937.78" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1100.84 2937.78 L 1121.84 2927.29 L 1116.59 2937.78 L 1121.83 2948.29 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1287" y="2745" width="457.89" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 940px; margin-left: 430px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><font style="font-size: 20px">Store Buffer</font><br /><font style="font-size: 14px">16 x 64B</font></font></div></div></div></foreignObject><text x="505" y="944" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Store Buffer...</text></switch></g><path d="M 1098 2776.5 L 1098.18 2700 L 1134.18 2700 L 1134.18 2610 L 1284.18 2610 L 1264.9 2610" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1280.65 2610 L 1259.65 2620.5 L 1264.9 2610 L 1259.65 2599.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="654" y="2553" width="444" height="447" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 146px; height: 1px; padding-top: 926px; margin-left: 219px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font><span style="font-size: 20px">Data Cache<br /></span><br /><font style="font-size: 16px">32 KB</font><br /><font style="font-size: 16px">8-way<br /></font></font></div></div></div></foreignObject><text x="292" y="929" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Data Cache...</text></switch></g><rect x="1044" y="2940" width="320.25" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 105px; height: 1px; padding-top: 990px; margin-left: 349px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 12px">64B/cycle</font></div></div></div></foreignObject><text x="401" y="994" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">64B/cycle</text></switch></g><rect x="1013.25" y="2340" width="330" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 790px; margin-left: 339px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 12px">8B/cycle</font></div></div></div></foreignObject><text x="393" y="794" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">8B/cycle</text></switch></g><path d="M 273 1837.5 L 273 1897.41 L 273 1875.18 L 273 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 273 1931.65 L 262.5 1910.65 L 273 1915.9 L 283.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="213" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 72px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC<br />RS0</div></div></div></foreignObject><text x="91" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC...</text></switch></g><path d="M 423 1837.5 L 423 1897.41 L 423 1875.18 L 423 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 423 1931.65 L 412.5 1910.65 L 423 1915.9 L 433.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="363" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 122px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC<br />RS1</div></div></div></foreignObject><text x="141" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC...</text></switch></g><path d="M 573 1837.5 L 573 1897.41 L 573 1875.18 L 573 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 573 1931.65 L 562.5 1910.65 L 573 1915.9 L 583.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="513" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 172px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC<br />RS2</div></div></div></foreignObject><text x="191" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC...</text></switch></g><path d="M 723 1837.5 L 723 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 723 1931.65 L 712.5 1910.65 L 723 1915.9 L 733.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="663" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 222px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC<br />RS3</div></div></div></foreignObject><text x="241" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC...</text></switch></g><path d="M 873 1837.5 L 873 1897.41 L 873 1875.18 L 873 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 873 1931.65 L 862.5 1910.65 L 873 1915.9 L 883.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="813" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 272px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMISC<br />RS0</div></div></div></foreignObject><text x="291" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC...</text></switch></g><path d="M 1023 1837.5 L 1023 1897.41 L 1023 1875.18 L 1023 1915.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1023 1931.65 L 1012.5 1910.65 L 1023 1915.9 L 1033.5 1910.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="963" y="1717.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 593px; margin-left: 322px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMISC<br />RS1</div></div></div></foreignObject><text x="341" y="596" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC...</text></switch></g><path d="M 273 2055 L 273 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="213" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 72px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC</div></div></div></foreignObject><text x="91" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 423 2055 L 423 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="363" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 122px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC</div></div></div></foreignObject><text x="141" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 573 2055 L 573 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="513" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 172px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC</div></div></div></foreignObject><text x="191" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 723 2055 L 723 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="663" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 222px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMAC</div></div></div></foreignObject><text x="241" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 873 2055 L 873 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="813" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 272px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMISC</div></div></div></foreignObject><text x="291" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC</text></switch></g><path d="M 1023 2055 L 1023 2123.29 L 480.18 2123.29 L 480.05 2172.76" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480.01 2188.51 L 469.56 2167.48 L 480.05 2172.76 L 490.56 2167.53 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="963" y="1935" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 665px; margin-left: 322px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FMISC</div></div></div></foreignObject><text x="341" y="669" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC</text></switch></g><path d="M 480 2130 L 480.18 2190 L 123 2190 L 123 1777.41 L 163.9 1777.47" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 179.65 1777.5 L 158.63 1787.96 L 163.9 1777.47 L 158.66 1766.96 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 480 2130 L 480.18 2190 L 123 2190 L 123 1455.18 L 163.9 1455.06" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 179.65 1455.01 L 158.68 1465.57 L 163.9 1455.06 L 158.62 1444.57 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2162.22 75 L 928.41 75.18 L 928.41 160.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 928.41 176.65 L 917.91 155.65 L 928.41 160.9 L 938.91 155.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2769.09 278.54 L 2814.18 278.47 L 2814.18 75.18 L 2452.1 75.01" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2436.35 75 L 2457.36 64.51 L 2452.1 75.01 L 2457.35 85.51 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2769.08 418.6 L 2769 387.18 L 2814.18 387.18 L 2814.18 75.18 L 2452.1 75.01" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2436.35 75 L 2457.36 64.51 L 2452.1 75.01 L 2457.35 85.51 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2769.09 570.7 L 2769 543.18 L 2814.18 543.18 L 2814.18 75.18 L 2452.1 75.01" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2436.35 75 L 2457.36 64.51 L 2452.1 75.01 L 2457.35 85.51 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 688.5 536.45 L 938.29 536.47 L 938.29 368.82 L 1168.9 368.63" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1184.65 368.61 L 1163.65 379.13 L 1168.9 368.63 L 1163.64 358.13 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="157.5" y="300" width="531" height="472.89" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 175px; height: 1px; padding-top: 179px; margin-left: 54px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">L1plus Cache<br /><br />128KB<br />8-way<br /></font></div></div></div></foreignObject><text x="141" y="182" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">L1plus Cache...</text></switch></g><path d="M 1178.25 2400 L 1178.25 2400" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2162.22 75 L 1411.59 75.18 L 1411.52 160.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1411.5 176.65 L 1401.02 155.64 L 1411.52 160.9 L 1422.02 155.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2162.22" y="30" width="270.78" height="90" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 25px; margin-left: 722px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 20px">PC</font></div></div></div></foreignObject><text x="766" y="29" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">PC</text></switch></g><path d="M 1329 2055 L 1329 2430 L 1329 2410.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1329 2426.65 L 1318.5 2405.65 L 1329 2410.9 L 1339.5 2405.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1269" y="2092.5" width="585" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 193px; height: 1px; padding-top: 718px; margin-left: 424px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 14px"><font style="font-size: 14px">DTLB</font><br /><font style="font-size: 14px">32 entries</font><br /></font></div></div></div></foreignObject><text x="521" y="721" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB...</text></switch></g><path d="M 876 2553 L 858.18 2553.18 L 858.18 2523.18 L 1254.18 2523.18 L 1254.18 2313.18 L 1462.9 2313.01" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1478.65 2313 L 1457.65 2323.52 L 1462.9 2313.01 L 1457.64 2302.52 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1044" y="2553" width="317.25" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 104px; height: 1px; padding-top: 861px; margin-left: 349px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font style="font-size: 12px">64B/cycle</font></div></div></div></foreignObject><text x="401" y="865" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">64B/cycle</text></switch></g><rect x="63" y="1080" width="792" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 262px; height: 1px; padding-top: 370px; margin-left: 22px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Rename &amp; Dispatch</div></div></div></foreignObject><text x="153" y="377" fill="#000000" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Rename &amp; Dispatch</text></switch></g><rect x="15" y="60" width="519" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 171px; height: 1px; padding-top: 30px; margin-left: 6px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Frontend</div></div></div></foreignObject><text x="92" y="37" fill="#000000" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Frontend</text></switch></g><rect x="667.89" y="2140.5" width="555" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 183px; height: 1px; padding-top: 726px; margin-left: 224px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Float Block</div></div></div></foreignObject><text x="315" y="733" fill="#000000" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Float Block</text></switch></g><rect x="1899" y="2127" width="549.78" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 721px; margin-left: 634px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Integer Block</div></div></div></foreignObject><text x="725" y="728" fill="#000000" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Integer Block</text></switch></g><rect x="174" y="1687.5" width="2829" height="180" fill="none" stroke="#000000" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="1298.61" y="2943" width="549.78" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 993px; margin-left: 434px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Memory Block</div></div></div></foreignObject><text x="525" y="1000" fill="#000000" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Memory Block</text></switch></g><rect x="3" y="2250" width="531" height="780" rx="79.65" ry="79.65" fill="#ffe6cc" stroke="none" pointer-events="all"/><path d="M 474 2857.5 L 474.18 2778 L 564.18 2778 L 634.9 2776.82" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 650.65 2776.56 L 629.82 2787.41 L 634.9 2776.82 L 629.47 2766.41 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="63" y="2430" width="411" height="570" fill="none" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 135px; height: 1px; padding-top: 905px; margin-left: 22px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">L2 Cache<br /><br /><span style="font-weight: normal ; font-size: 16px">1MB<br />8-way</span></div></div></div></foreignObject><text x="89" y="912" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">L2 Cache...</text></switch></g><path d="M 86.43 2428.29 L 86.29 2277.18 L 84.18 536.47 L 138.4 536.45" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 154.15 536.45 L 133.15 546.95 L 138.4 536.45 L 133.14 525.95 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 384 2430 L 384.18 2376 L 583.15 2376" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 598.9 2376 L 577.9 2386.5 L 583.15 2376 L 577.9 2365.5 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 602.25 2346 L 54.18 2346 L 54.18 255.18 L 763.9 255" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 779.65 255 L 758.65 265.51 L 763.9 255 L 758.64 244.51 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>