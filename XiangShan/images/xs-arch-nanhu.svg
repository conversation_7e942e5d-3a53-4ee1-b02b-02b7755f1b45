<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="3486px" height="3723px" viewBox="-0.5 -0.5 3486 3723" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2022-03-22T02:24:06.749Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/16.5.1 Chrome/96.0.4664.110 Electron/16.0.7 Safari/537.36&quot; etag=&quot;jd9VOD06ADdCi5yL7BYP&quot; version=&quot;16.5.1&quot; type=&quot;device&quot; pages=&quot;2&quot;&gt;&lt;diagram name=&quot;xs-整体微结构-多彩版&quot; id=&quot;xSjQYj8qTl4sxZwAIWEt&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;memblock&quot; id=&quot;-_N_g4Llr-yANnlNv5cw&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro);&#xa;</style></defs><g><rect x="1258.89" y="1980" width="961.11" height="570" rx="85.5" ry="85.5" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="1258.89" y="2310" width="961.11" height="630" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="30" y="2610" width="2190" height="630" rx="94.5" ry="94.5" fill="#fff2cc" stroke="none" pointer-events="all"/><rect x="2250" y="1980" width="1200" height="636" rx="95.4" ry="95.4" fill="#dae8fc" stroke="none" pointer-events="all"/><rect x="30" y="1980" width="1200" height="600" rx="90" ry="90" fill="#f8cecc" stroke="none" pointer-events="all"/><rect x="30" y="1350" width="3423" height="600" rx="90" ry="90" fill="#e1d5e7" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1139px; height: 1px; padding-top: 550px; margin-left: 12px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><br /><br /><br /><br /><br /></div></div></div></foreignObject><text x="12" y="555" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px">...</text></switch></g><rect x="30" y="30" width="2523" height="1290" rx="193.5" ry="193.5" fill="#d5e8d4" stroke="none" pointer-events="all"/><path d="M 165 1177.5 L 165 1209 L 105 1209 L 105 2727 L 580.9 2727" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 596.65 2727 L 575.65 2737.5 L 580.9 2727 L 575.65 2716.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="165" y="1080" width="441" height="195" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 145px; height: 1px; padding-top: 393px; margin-left: 56px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 20px">ITLB</font><br style="font-size: 14px" /><font style="font-size: 14px">32 4k-page entries<br /></font>8 superpage entries<br style="font-size: 14px" /></font></div></div></div></foreignObject><text x="129" y="397" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">ITLB...</text></switch></g><path d="M 606 885 L 606 795 L 886.9 795" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 902.65 795 L 881.65 805.5 L 886.9 795 L 881.65 784.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="165" y="690" width="441" height="390" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 145px; height: 1px; padding-top: 295px; margin-left: 56px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Instruction</span><br /><span style="font-size: 20px">Cache</span><br /><font><font style="font-size: 16px"><br />128 KB, </font><span style="font-size: 16px">8 way</span><br /></font></font></div></div></div></foreignObject><text x="129" y="299" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction...</text></switch></g><path d="M 1836 795 L 1966.9 795" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1982.65 795 L 1961.65 805.5 L 1966.9 795 L 1961.65 784.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 906 795 L 720 795 L 625.1 795" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 609.35 795 L 630.35 784.5 L 625.1 795 L 630.35 805.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1371 900 L 1371 940.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1371 956.65 L 1360.5 935.65 L 1371 940.9 L 1381.5 935.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="906" y="690" width="930" height="210" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 265px; margin-left: 303px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">Instruction Fetch Unit<br /><br /><br /></font></div></div></div></foreignObject><text x="457" y="269" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction Fetch Unit...</text></switch></g><path d="M 1746 2137.5 L 1671 2137.5 L 1671 2215.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1671 2231.65 L 1660.5 2210.65 L 1671 2215.9 L 1681.5 2210.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1611" y="2017.5" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 538px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">STA RS<br />16 * 2</div></div></div></foreignObject><text x="582" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">STA RS...</text></switch></g><path d="M 1824 2137.5 L 1824 2215.9" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1824 2231.65 L 1813.5 2210.65 L 1824 2215.9 L 1834.5 2210.65 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2637 2136 L 2637 2214.4" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2637 2230.15 L 2626.5 2209.15 L 2637 2214.4 L 2647.5 2209.15 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2790 2136 L 2790 2214.4" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2790 2230.15 L 2779.5 2209.15 L 2790 2214.4 L 2800.5 2209.15 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2943 2136 L 2943 2214.4" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2943 2230.15 L 2932.5 2209.15 L 2943 2214.4 L 2953.5 2209.15 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2484 2136 L 2484 2214.4" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2484 2230.15 L 2473.5 2209.15 L 2484 2214.4 L 2494.5 2209.15 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1611" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 538px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AGU<br />ST</div></div></div></foreignObject><text x="557" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><rect x="1764" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 589px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AGU<br />ST</div></div></div></foreignObject><text x="608" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 2637 2353.5 L 2637 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2577" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 860px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU</div></div></div></foreignObject><text x="879" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2790 2353.5 L 2790 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2730" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 911px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU</div></div></div></foreignObject><text x="930" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2943 2353.5 L 2943 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2883" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 962px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU</div></div></div></foreignObject><text x="981" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 2484 2353.5 L 2484 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="#000000" stroke="#000000" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2424" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 809px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">MDU</div></div></div></foreignObject><text x="828" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU</text></switch></g><path d="M 2757 2488.5 L 3420 2488.5 L 3420 2085 L 3372 2085 L 3379.09 2084.72" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3363.35 2085.35 L 3383.91 2074.02 L 3379.09 2084.72 L 3384.75 2095 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2613" y="900" width="480" height="390" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 365px; margin-left: 872px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">ReOrder Buffer</span><br /><font style="font-size: 16px">256 entires</font></font></div></div></div></foreignObject><text x="951" y="369" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ReOrder Buffer...</text></switch></g><rect x="1308" y="2586" width="282" height="354" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 92px; height: 1px; padding-top: 921px; margin-left: 437px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 20px">Load Queue</font><br /><font style="font-size: 14px">80 entries</font></font></div></div></div></foreignObject><text x="483" y="925" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Load Queue...</text></switch></g><rect x="2031" y="1680" width="757.5" height="210" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 251px; height: 1px; padding-top: 595px; margin-left: 678px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">INT Physical Register File</span><br /><font style="font-size: 16px">192 entries</font></font></div></div></div></foreignObject><text x="803" y="599" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">INT Physical Register File...</text></switch></g><rect x="2031" y="1500" width="757.5" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 251px; height: 1px; padding-top: 525px; margin-left: 678px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">Integer Rename Table</font></div></div></div></foreignObject><text x="803" y="529" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Integer Rename Table</text></switch></g><rect x="753" y="1500" width="712.5" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 236px; height: 1px; padding-top: 525px; margin-left: 252px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">FP Rename Table</font></div></div></div></foreignObject><text x="370" y="529" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FP Rename Table</text></switch></g><rect x="753" y="1680" width="712.5" height="210" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 236px; height: 1px; padding-top: 595px; margin-left: 252px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">FP Physical Register File</span><br /><font style="font-size: 16px">192 entries</font></font></div></div></div></foreignObject><text x="370" y="599" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FP Physical Register File...</text></switch></g><path d="M 3095.22 2137.08 L 3095.85 2214.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3095.97 2230.15 L 3085.3 2209.23 L 3095.85 2214.4 L 3106.3 2209.06 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2580" y="2016" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 692px; margin-left: 861px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU RS<br />16 * 2</div></div></div></foreignObject><text x="905" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU RS...</text></switch></g><path d="M 3096 2353.5 L 3096 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="3036" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 1013px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU</div></div></div></foreignObject><text x="1032" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU</text></switch></g><path d="M 1518 2137.5 L 1518 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1518 2231.65 L 1507.5 2210.65 L 1518 2215.9 L 1528.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1458" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 487px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AGU<br />LD</div></div></div></foreignObject><text x="506" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 1365 2160 L 1365 2130 L 1365 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1365 2231.65 L 1354.5 2210.65 L 1365 2215.9 L 1375.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1305" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 436px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AGU<br />LD</div></div></div></foreignObject><text x="455" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">AGU...</text></switch></g><path d="M 2410.5 2136 L 2331 2136 L 2331 2214.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2331 2230.15 L 2320.5 2209.15 L 2331 2214.4 L 2341.5 2209.15 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2271" y="2016" width="279" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 91px; height: 1px; padding-top: 692px; margin-left: 758px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">MDU RS<br />16 *2</div></div></div></foreignObject><text x="804" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU RS...</text></switch></g><path d="M 2331 2353.5 L 2331 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="2271" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 758px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">MDU</div></div></div></foreignObject><text x="777" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">MDU</text></switch></g><path d="M 3249 2136 L 3249 2214.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 3249 2230.15 L 3238.5 2209.15 L 3249 2214.4 L 3259.5 2209.15 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="3189" y="2016" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 692px; margin-left: 1064px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">MISC<br />16</div></div></div></foreignObject><text x="1083" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">MISC...</text></switch></g><path d="M 3249 2353.5 L 3249 2421.9 L 2755.5 2421.9 L 2755.5 2471.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2755.5 2486.99 L 2745 2465.99 L 2755.5 2471.24 L 2766 2465.99 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="3189" y="2233.5" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 1064px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">MISC</div></div></div></foreignObject><text x="1083" y="768" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">MISC</text></switch></g><path d="M 1905 2730 L 1905 2767.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1905 2783.65 L 1894.5 2762.65 L 1905 2767.9 L 1915.5 2762.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1620" y="2586" width="570" height="144" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 886px; margin-left: 541px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 20px">Store Queue</font><br /><font style="font-size: 14px">64 entries</font></font></div></div></div></foreignObject><text x="635" y="890" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Store Queue...</text></switch></g><rect x="1620" y="2787" width="570" height="150" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 954px; margin-left: 541px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 17px">Committed Store Buffer</font><br /><font style="font-size: 14px">16 x 64B</font></font></div></div></div></foreignObject><text x="635" y="958" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Committed Store Buffer...</text></switch></g><path d="M 1738.5 3210 L 1735.2 3210 L 1735.25 3281.95" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1735.26 3297.7 L 1724.74 3276.7 L 1735.25 3281.95 L 1745.74 3276.69 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1287" y="3003" width="903" height="207" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 299px; height: 1px; padding-top: 1036px; margin-left: 430px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Data Cache</span><br /><font style="font-size: 16px">128 KB, </font><font style="font-size: 16px">8 way, 8 bank<br /></font></font></div></div></div></foreignObject><text x="580" y="1039" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Data Cache...</text></switch></g><rect x="2250" y="2637" width="1163.25" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 386px; height: 1px; padding-top: 889px; margin-left: 752px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">* 128 4K-page entries, 8 </font>4K-page/superpage entries</div></div></div></foreignObject><text x="752" y="893" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">* 128 4K-page entries, 8 4K-page/superpage entries</text></switch></g><path d="M 384 2137.5 L 309 2137.5 L 309 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 309 2231.65 L 298.5 2210.65 L 309 2215.9 L 319.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="249" y="2017.5" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 84px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC RS<br />16 * 2</div></div></div></foreignObject><text x="128" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC RS...</text></switch></g><path d="M 459 2137.5 L 459 2197.5 L 459 2175 L 459 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 459 2231.65 L 448.5 2210.65 L 459 2215.9 L 469.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 609 2137.5 L 609 2197.5 L 609 2175 L 609 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 609 2231.65 L 598.5 2210.65 L 609 2215.9 L 619.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 759 2137.5 L 759 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 759 2231.65 L 748.5 2210.65 L 759 2215.9 L 769.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="849" y="2017.5" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 284px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMISC RS<br />16 * 2</div></div></div></foreignObject><text x="328" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC RS...</text></switch></g><path d="M 1059 2137.5 L 1059 2197.5 L 1059 2175 L 1059 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1059 2231.65 L 1048.5 2210.65 L 1059 2215.9 L 1069.5 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 309 2355 L 309 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="249" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 84px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC</div></div></div></foreignObject><text x="103" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 459 2355 L 459 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="399" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 134px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC</div></div></div></foreignObject><text x="153" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 609 2355 L 609 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="549" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 184px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC</div></div></div></foreignObject><text x="203" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 759 2355 L 759 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="699" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 234px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC</div></div></div></foreignObject><text x="253" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC</text></switch></g><path d="M 909 2355 L 909 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="849" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 284px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMISC</div></div></div></foreignObject><text x="303" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC</text></switch></g><path d="M 1059 2355 L 1059 2423.4 L 516 2423.4 L 516 2472.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 516 2488.51 L 505.5 2467.51 L 516 2472.76 L 526.5 2467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="999" y="2235" width="120" height="120" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 765px; margin-left: 334px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMISC</div></div></div></foreignObject><text x="353" y="769" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMISC</text></switch></g><path d="M 516 2430 L 516 2490 L 159 2490 L 159 2077.5 L 199.9 2077.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 215.65 2077.5 L 194.65 2088 L 199.9 2077.5 L 194.65 2067 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1746.39" y="3222" width="203.61" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 66px; height: 1px; padding-top: 1084px; margin-left: 583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">32B / cycle</font></div></div></div></foreignObject><text x="616" y="1088" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">32B / cycle</text></switch></g><rect x="111" y="1380" width="792" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 262px; height: 1px; padding-top: 470px; margin-left: 38px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Rename &amp; Dispatch</div></div></div></foreignObject><text x="169" y="477" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Rename &amp; Dispatch</text></switch></g><rect x="54" y="90" width="519" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 171px; height: 1px; padding-top: 40px; margin-left: 19px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Frontend</div></div></div></foreignObject><text x="105" y="47" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Frontend</text></switch></g><rect x="706.5" y="2490" width="555" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 183px; height: 1px; padding-top: 842px; margin-left: 237px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Float Block</div></div></div></foreignObject><text x="328" y="849" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Float Block</text></switch></g><rect x="2271" y="2518.5" width="549.78" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 852px; margin-left: 758px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Integer Block</div></div></div></foreignObject><text x="849" y="859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Integer Block</text></switch></g><rect x="60" y="3150" width="549.78" height="72" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 181px; height: 1px; padding-top: 1062px; margin-left: 21px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Memory Block</div></div></div></foreignObject><text x="112" y="1069" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle" font-weight="bold">Memory Block</text></switch></g><path d="M 1395 3450 L 1395 3520.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1395 3536.65 L 1384.5 3515.65 L 1395 3520.9 L 1405.5 3515.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="600" y="3300" width="1590" height="150" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 528px; height: 1px; padding-top: 1125px; margin-left: 201px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><span style="font-weight: normal"><font style="font-size: 20px">L2 Cache</font><br /></span><span style="font-size: 16px"><span style="font-weight: normal">1 MB, 8 way, 4 bank</span><br /></span></div></div></div></foreignObject><text x="465" y="1129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">L2 Cache...</text></switch></g><rect x="1914" y="2017.5" width="271.11" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 639px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">STD RS<br />16 * 2</div></div></div></foreignObject><text x="683" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">STD RS...</text></switch></g><rect x="1308" y="2017.5" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 437px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">LD RS<br />16 * 2</div></div></div></foreignObject><text x="481" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">LD RS...</text></switch></g><path d="M 1364.1 2398.5 L 1364.1 2355 L 1364.13 2566.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1364.13 2582.65 L 1353.63 2561.65 L 1364.13 2566.9 L 1374.63 2561.64 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1517.4 2398.5 L 1517.4 2353.5 L 1517.51 2565.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1517.52 2581.15 L 1507.01 2560.15 L 1517.51 2565.4 L 1528.01 2560.14 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1671 2398.5 L 1671 2355 L 1671.03 2566.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1671.03 2582.65 L 1660.53 2561.65 L 1671.03 2566.9 L 1681.53 2561.64 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1823.4 2398.5 L 1823.4 2353.5 L 1823.51 2565.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1823.52 2581.15 L 1813.01 2560.15 L 1823.51 2565.4 L 1834.01 2560.14 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1531.89" y="1500" width="435" height="390" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 143px; height: 1px; padding-top: 565px; margin-left: 512px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Memory</span><br /><font style="font-size: 16px"><span style="font-size: 20px">Dispatch Queue</span><br />16 entries<br />6i4o<br /></font></font></div></div></div></foreignObject><text x="583" y="569" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Memory...</text></switch></g><rect x="2841" y="1500" width="435" height="390" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 143px; height: 1px; padding-top: 565px; margin-left: 948px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Integer<br />Dispatch Queue</span><br /><font style="font-size: 16px">16 entries<br />6i4o<br /></font></font></div></div></div></foreignObject><text x="1020" y="569" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Integer...</text></switch></g><rect x="252" y="1500" width="435" height="390" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 143px; height: 1px; padding-top: 565px; margin-left: 85px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Float<br />Dispatch Queue</span><br /><font style="font-size: 16px">16 entries<br />6i4o<br /></font></font></div></div></div></foreignObject><text x="157" y="569" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Float...</text></switch></g><path d="M 1980 2178.06 L 1980 2137.5 L 1980 2566.96" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1980 2582.71 L 1969.5 2561.71 L 1980 2566.96 L 1990.5 2561.71 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 2100 2176.56 L 2100 2136 L 2100 2565.46" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 2100 2581.21 L 2089.5 2560.21 L 2100 2565.46 L 2110.5 2560.21 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1371 1117.5 L 1371 1165.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1371 1181.65 L 1360.5 1160.65 L 1371 1165.9 L 1381.5 1160.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="906" y="960" width="930" height="157.5" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 346px; margin-left: 303px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 20px">Instruction Buffer</font><br /><font style="font-size: 16px">48 entries</font></font></div></div></div></foreignObject><text x="457" y="350" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction Buffer...</text></switch></g><path d="M 1371 577.5 L 1371 630 L 387 630 L 387 670.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 387 686.65 L 376.5 665.65 L 387 670.9 L 397.5 665.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1371 577.5 L 1371 670.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1371 686.65 L 1360.5 665.65 L 1371 670.9 L 1381.5 665.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1603.5 420 L 1621.8 420 L 1621.8 346.67" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1621.8 330.92 L 1632.3 351.92 L 1621.8 346.67 L 1611.3 351.92 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1836 498.75 L 1896 498.9 L 1936.9 498.63" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1952.65 498.52 L 1931.72 509.16 L 1936.9 498.63 L 1931.58 488.16 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 786 498.5 L 846 498.6 L 886.9 498.7" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 902.65 498.74 L 881.62 509.19 L 886.9 498.7 L 881.67 488.19 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="906" y="420" width="930" height="157.5" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 166px; margin-left: 303px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><span style="font-size: 20px">Fetch Target Queue</span><br /><font style="font-size: 16px">64 entries</font></font></div></div></div></foreignObject><text x="457" y="170" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Fetch Target Queue...</text></switch></g><path d="M 1371 1275 L 1371 1360.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1371 1376.65 L 1360.5 1355.65 L 1371 1360.9 L 1381.5 1355.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="906" y="1185" width="930" height="90" fill="#7ea6e0" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 308px; height: 1px; padding-top: 410px; margin-left: 303px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">6-way Decoder</font></div></div></div></foreignObject><text x="457" y="414" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">6-way Decoder</text></switch></g><rect x="1015.5" y="780" width="330" height="90" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 275px; margin-left: 340px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">Predecode</span></div></div></div></foreignObject><text x="394" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Predecode</text></switch></g><rect x="1387.5" y="780" width="330" height="90" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 275px; margin-left: 464px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">BP Checker</span></div></div></div></foreignObject><text x="518" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">BP Checker</text></switch></g><path d="M 1986 795 L 1855.1 795" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1839.35 795 L 1860.35 784.5 L 1855.1 795 L 1860.35 805.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1841.25" y="795" width="150" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 280px; margin-left: 615px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">8 Bytes</div></div></div></foreignObject><text x="639" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">8 Bytes</text></switch></g><rect x="606" y="795" width="300" height="105" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 283px; margin-left: 203px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">2 cachelines<br />(2 x 64 Bytes)</div></div></div></foreignObject><text x="252" y="286" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">2 cachelines...</text></switch></g><rect x="1986" y="690" width="441" height="210" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 145px; height: 1px; padding-top: 265px; margin-left: 663px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font><span style="font-size: 20px">Instruction</span><br /><span style="font-size: 20px">Uncache</span><br /></font></font></div></div></div></foreignObject><text x="736" y="269" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction...</text></switch></g><rect x="1107" y="330" width="270" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 125px; margin-left: 370px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">predict result</div></div></div></foreignObject><text x="414" y="129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">predict result</text></switch></g><rect x="2031" y="1362" width="759" height="90" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 251px; height: 1px; padding-top: 469px; margin-left: 678px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">Move Elimination</span></div></div></div></foreignObject><text x="804" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Move Elimination</text></switch></g><rect x="1377" y="900" width="189" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 310px; margin-left: 460px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">32 Bytes</div></div></div></foreignObject><text x="491" y="314" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">32 Bytes</text></switch></g><rect x="1377" y="1110" width="189" height="75" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 383px; margin-left: 460px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">6 Instrs</div></div></div></foreignObject><text x="491" y="386" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">6 Instrs</text></switch></g><rect x="1377" y="1260" width="189" height="75" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 433px; margin-left: 460px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">6 Uops</div></div></div></foreignObject><text x="491" y="436" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">6 Uops</text></switch></g><path d="M 1371 330 L 1371 390 L 1371 360 L 1371 400.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1371 416.65 L 1360.5 395.65 L 1371 400.9 L 1381.5 395.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1377" y="577.5" width="189" height="112.5" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 211px; margin-left: 460px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FTQ Req</div></div></div></foreignObject><text x="491" y="215" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FTQ Req</text></switch></g><rect x="1656" y="330" width="270" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 125px; margin-left: 553px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">redirect / update</div></div></div></foreignObject><text x="597" y="129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">redirect / upda...</text></switch></g><rect x="587.25" y="60" width="1567.5" height="270" fill="#ffe6cc" stroke="#d79b00" stroke-width="3" pointer-events="all"/><rect x="636" y="90" width="270" height="60" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 40px; margin-left: 213px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">uBTB</font></div></div></div></foreignObject><text x="257" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">uBTB</text></switch></g><rect x="587.25" y="251.61" width="498.75" height="78.39" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 164px; height: 1px; padding-top: 91px; margin-left: 198px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(0 , 0 , 0) ; font-style: normal ; font-weight: 400 ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; display: inline ; float: none"><font style="font-size: 16px">Branch Prediction Unit</font></span></div></div></div></foreignObject><text x="198" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Branch Prediction Unit</text></switch></g><rect x="936" y="90" width="270" height="150" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 55px; margin-left: 313px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">FTB</span></div></div></div></foreignObject><text x="357" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FTB</text></switch></g><rect x="1236" y="90" width="270" height="150" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 55px; margin-left: 413px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">TAGE</span></div></div></div></foreignObject><text x="457" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">TAGE</text></switch></g><rect x="1536" y="90" width="270" height="210" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 65px; margin-left: 513px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">SC<br /></span></div></div></div></foreignObject><text x="557" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">SC&#xa;</text></switch></g><rect x="1836" y="90" width="270" height="210" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 65px; margin-left: 613px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">ITTAGE<br /></span></div></div></div></foreignObject><text x="657" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ITTAGE&#xa;</text></switch></g><rect x="3150" y="1410" width="210" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 480px; margin-left: 1051px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Scheduler</div></div></div></foreignObject><text x="1085" y="484" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">Scheduler</text></switch></g><rect x="465" y="453.75" width="339" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 111px; height: 1px; padding-top: 166px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Backend<br />Redirect / Update</div></div></div></foreignObject><text x="212" y="170" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Backend...</text></switch></g><rect x="636" y="180" width="270" height="60" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 70px; margin-left: 213px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">RAS</font></div></div></div></foreignObject><text x="257" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">RAS</text></switch></g><path d="M 1395 3540 L 1395 3469.1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1395 3453.35 L 1405.5 3474.35 L 1395 3469.1 L 1384.5 3474.35 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="600" y="3540" width="1590" height="180" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 528px; height: 1px; padding-top: 1210px; margin-left: 201px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;"><span style="font-weight: normal"><font style="font-size: 20px">L3 Cache</font><br /></span><span style="font-weight: normal ; font-size: 16px">6 MB, 6 way, 4 bank</span></div></div></div></foreignObject><text x="465" y="1214" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">L3 Cache...</text></switch></g><path d="M 908.55 2137.5 L 908.55 2215.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 908.55 2231.65 L 898.05 2210.65 L 908.55 2215.9 L 919.05 2210.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1950" y="450" width="270" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 165px; margin-left: 651px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">PC read result for Backend</div></div></div></foreignObject><text x="695" y="169" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PC read result...</text></switch></g><rect x="1305" y="2398.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 820px; margin-left: 436px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DTLB*</div></div></div></foreignObject><text x="455" y="823" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB*</text></switch></g><path d="M 1364.13 2385 L 1365 2398.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1458" y="2398.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 820px; margin-left: 487px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DTLB*</div></div></div></foreignObject><text x="506" y="823" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB*</text></switch></g><path d="M 1517.52 2383.5 L 1518 2398.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1611" y="2398.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 820px; margin-left: 538px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DTLB*</div></div></div></foreignObject><text x="557" y="823" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB*</text></switch></g><path d="M 1671.03 2385 L 1671 2398.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><rect x="1764" y="2398.5" width="120" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 820px; margin-left: 589px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DTLB*</div></div></div></foreignObject><text x="608" y="823" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB*</text></switch></g><path d="M 1823.52 2383.5 L 1824 2398.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 889.5 3207 L 889.4 3282.1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 889.38 3297.85 L 878.91 3276.83 L 889.4 3282.1 L 899.91 3276.86 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="600" y="3000" width="579" height="207" fill="#ffb570" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 191px; height: 1px; padding-top: 1035px; margin-left: 201px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font><font style="font-size: 20px">L2TLB &amp; PTW</font><br /><font style="font-size: 16px">2048 </font><span style="font-size: 16px">entries</span><br /></font></div></div></div></foreignObject><text x="297" y="1038" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">L2TLB &amp; PTW...</text></switch></g><rect x="909" y="2667" width="276" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 90px; height: 1px; padding-top: 909px; margin-left: 304px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 14px"><font style="font-size: 14px">DTLB Repeater</font><br /></font></div></div></div></foreignObject><text x="349" y="913" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">DTLB Repeater&#xa;</text></switch></g><rect x="1457.64" y="2943" width="189" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 991px; margin-left: 487px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">32B / cycle</font></div></div></div></foreignObject><text x="517" y="995" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">32B / cycle</text></switch></g><path d="M 1905 2937 L 1905 2986.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1905 3002.65 L 1894.5 2981.65 L 1905 2986.9 L 1915.5 2981.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1442.58 3006 L 1442.58 2956.1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1442.58 2940.35 L 1453.08 2961.35 L 1442.58 2956.1 L 1432.08 2961.35 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="600" y="2667" width="276" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 90px; height: 1px; padding-top: 909px; margin-left: 201px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 14px"><font style="font-size: 14px">ITLB Repeater</font><br /></font></div></div></div></foreignObject><text x="246" y="913" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ITLB Repeater&#xa;</text></switch></g><rect x="1917" y="2724" width="243" height="63" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 79px; height: 1px; padding-top: 919px; margin-left: 640px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">2 * 8B / cycle</font></div></div></div></foreignObject><text x="680" y="922" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">2 * 8B / cycle</text></switch></g><rect x="1917" y="2938.5" width="189" height="63" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 990px; margin-left: 640px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">64B / cycle</font></div></div></div></foreignObject><text x="671" y="994" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">64B / cycle</text></switch></g><path d="M 1047 2787 L 1047 2850 L 1047.5 2983.59" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1047.55 2999.34 L 1036.98 2978.38 L 1047.5 2983.59 L 1057.98 2978.3 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 738 2787 L 744.9 2787 L 744.76 2980.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 744.75 2996.65 L 734.27 2975.64 L 744.76 2980.9 L 755.27 2975.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 888.99 3300 L 889.09 3224.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 889.11 3209.15 L 899.58 3230.17 L 889.09 3224.9 L 878.58 3230.14 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 1731 3298.05 L 1734.3 3298.2 L 1734.25 3226.1" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1734.24 3210.35 L 1744.76 3231.35 L 1734.25 3226.1 L 1723.76 3231.36 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="549" y="2017.5" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 693px; margin-left: 184px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FMAC RS<br />16 * 2</div></div></div></foreignObject><text x="228" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">FMAC RS...</text></switch></g><rect x="2883" y="2016" width="270" height="120" fill="#cda2be" stroke="#000000" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 692px; margin-left: 962px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ALU RS<br />16 * 2</div></div></div></foreignObject><text x="1006" y="696" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">ALU RS...</text></switch></g><rect x="1836" y="1185" width="534.75" height="90" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 176px; height: 1px; padding-top: 410px; margin-left: 613px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">Instruction Fusion</span></div></div></div></foreignObject><text x="701" y="414" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Instruction Fusion</text></switch></g><rect x="2190" y="3300" width="450" height="150" fill="#dae8fc" stroke="#6c8ebf" stroke-width="3" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 1125px; margin-left: 731px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 20px">Best-Offset<br />Prefetch<br /></span></div></div></div></foreignObject><text x="805" y="1129" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Best-Offset...</text></switch></g><rect x="1416.18" y="3450" width="203.61" height="90" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 66px; height: 1px; padding-top: 1165px; margin-left: 473px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px">32B / cycle</font></div></div></div></foreignObject><text x="506" y="1169" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">32B / cycle</text></switch></g><rect x="210" y="1470" width="3150" height="690" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><path d="M 1290 2460 L 1260 2460 L 1260 2727 L 1204.1 2727" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1188.35 2727 L 1209.35 2716.5 L 1204.1 2727 L 1209.35 2737.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="1290" y="2370" width="615" height="180" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="0" y="0" width="3480" height="3480" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="3003" y="30" width="480" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(3)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 158px; height: 1px; padding-top: 20px; margin-left: 1003px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 20px">XiangShan Core</font></div></div></div></foreignObject><text x="1003" y="24" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">XiangShan Core</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>