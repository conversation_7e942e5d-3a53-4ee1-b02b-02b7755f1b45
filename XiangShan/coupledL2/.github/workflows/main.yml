# This file describes the GitHub Actions workflow for continuous integration of CoupledL2

name: CI

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ master, chi-coupledl2 ]
  pull_request:
    branches: [ master, chi-coupledl2 ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  tl-test_L2:
    # The type of runner that the job will run on
    runs-on: ubuntu-22.04

    # Environments
    env:
      RUN_ARCHIVE: coupledL2-tl-test-new-run-${{ github.sha }}
      RUN_ARCHIVE_TL: coupledL2-tl-test-new-run-${{ github.sha }}-tilelink.tar.gz
      RUN_ARCHIVE_CHI: coupledL2-tl-test-new-run-${{ github.sha }}-chi.tar.gz

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2
        with:
          submodules: recursive

      - name: Setup Clang 17
        run: |
          wget https://apt.llvm.org/llvm.sh
          chmod u+x llvm.sh
          sudo ./llvm.sh 17

      - name: Setup Verilator
        run: |
          wget https://raw.githubusercontent.com/OpenXiangShan/xs-env/master/install-verilator.sh
          chmod u+x install-verilator.sh
          sed -i 's/CC=clang/CC=clang-17/g' install-verilator.sh
          sed -i 's/CXX=clang++/CXX=clang++-17/g' install-verilator.sh
          sed -i 's/LINK=clang++/LINK=clang++-17/g' install-verilator.sh
          sed -i 's/make -j8/make -j4/g' install-verilator.sh
          bash install-verilator.sh

      - name: Setup Scala
        uses: olafurpg/setup-scala@v10

      - name: Cache
        id: cache
        uses: coursier/cache-action@v5

      - name: Setup Mill
        uses: jodersky/setup-mill@v0.2.3
        with:
          mill-version: 0.11.1

      # - name: Check scalafmt
      #   run: make checkformat
        
      - name: Compile
        run: make compile
      
      # Clean artifacts folder (./tl-test-new/run) after successful run
      - name: Unit test for CHI version
        run: |
            rm -rf tl-test-new
            git clone https://github.com/OpenXiangShan/tl-test-new
            cd ./tl-test-new
            sed -i 's/ari.target.*/ari.target = 240/g' ./configs/user.tltest.ini
            sed -i 's/cmo.enable .*=.*/cmo.enable = 1/g' ./configs/user.tltest.ini
            sed -i 's/cmo.enable.cbo.clean.*=.*/cmo.enable.cbo.clean = 1/g' ./configs/user.tltest.ini
            sed -i 's/cmo.enable.cbo.flush.*=.*/cmo.enable.cbo.flush = 1/g' ./configs/user.tltest.ini
            cd ./dut
            git clone https://github.com/OpenXiangShan/OpenLLC
            cd OpenLLC && make init
            rm -rf ./coupledL2 && ln -s ../../.. ./coupledL2
            cd ..
            rm -rf ./CoupledL2 && ln -s ./OpenLLC ./CoupledL2 && cd ..
            make coupledL2-test-l2l3l2 run THREADS_BUILD=4 CXX_COMPILER=clang++-17
            rm -rf run/*.vcd run/*.fst run/*.log run/*.db
            
      - name: Tar up artifacts of Unit Test for CHI version
        if: always()
        run: |
            test -d ./tl-test-new/run || mkdir -p ./tl-test-new/run
            tar -zcf ${{ env.RUN_ARCHIVE_CHI }} ./tl-test-new/run
      
      # Clean artifacts folder (./tl-test-new/run) after successful run
      - name: Unit Test for TileLink version
        run: |
            rm -rf tl-test-new
            git clone https://github.com/OpenXiangShan/tl-test-new
            cd ./tl-test-new
            sed -i 's/ari.target.*/ari.target = 240/g' ./configs/user.tltest.ini
            rm -rf ./dut/CoupledL2 && ln -s ../.. ./dut/CoupledL2
            make coupledL2-test-l2l3l2 run THREADS_BUILD=4 CXX_COMPILER=clang++-17
            rm -rf run/*.vcd run/*.fst run/*.log run/*.db

      - name: Tar up artifacts of Unit Test for TileLink version
        if: always()
        run: |
            test -d ./tl-test-new/run || mkdir -p ./tl-test-new/run
            tar -zcf ${{ env.RUN_ARCHIVE_TL }} ./tl-test-new/run

      - name: Upload artifacts of Unit Test
        if: always()
        uses: actions/upload-artifact@v4
        with:
          path: ${{ github.workspace }}/${{ env.RUN_ARCHIVE }}*
          name: ${{ env.RUN_ARCHIVE }}
