/***************************************************************************************
* Copyright (c) 2020-2022 Institute of Computing Technology, Chinese Academy of Sciences
*
* NEMU is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
*          http://license.coscl.org.cn/MulanPSL2
*
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
*
* See the Mulan PSL v2 for more details.
***************************************************************************************/

//
// Created by zyy on 2020/11/21.
//

#ifndef NEMU_PATH_MANAGER_H
#define NEMU_PATH_MANAGER_H

#include <string>
#include <filesystem>

namespace fs = std::filesystem;

class PathManager
{

    std::string statsBaseDir;
    std::string configName;
    std::string workloadName;

    uint64_t cptID;

    std::string workloadPath;
    fs::path outputPath;
    fs::path simpointPath;

  public:
    void init();

    void incCptID();

    uint64_t getCptID() const {return cptID;}

    std::string getOutputPath() const;

    std::string getWorkloadPath() const {return workloadPath;};

    std::string getSimpointPath() const;

    void setSimpointProfilingOutputDir();
    void setCheckpointingOutputDir();
};

extern PathManager pathManager;

#endif //NEMU_PATH_MANAGER_H
