/***********************************************************************
 *  __________________________________________________________________
 *
 *              _____ _           ____        _       __
 *             / ___/(_)___ ___  / __ \____  (_)___  / /_
 *             \__ \/ / __ `__ \/ /_/ / __ \/ / __ \/ __/
 *            ___/ / / / / / / / ____/ /_/ / / / / / /_
 *           /____/_/_/ /_/ /_/_/    \____/_/_/ /_/\__/
 *
 *  __________________________________________________________________
 *
 * This file is part of the SimPoint Toolkit written by <PERSON>,
 * <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> as part of
 * Efficient Simulation Project at UCSD.  If you find this toolkit useful please
 * cite the following paper published at ASPLOS 2002.
 *
 *  Timothy Sherwood, Erez Perelman, Greg Hamerly and Brad Calder,
 *  Automatically Characterizing Large Scale Program Behavior , In the
 *  10th International Conference on Architectural Support for Programming
 *  Languages and Operating Systems, October 2002.
 *
 * Contact info:
 *        Brad Calder <<EMAIL>>, (858) 822 - 1619
 *        Greg Hamerly <<EMAIL>>,
 *        Erez Perelman <<EMAIL>>,
 *        Jeremy Lau <<EMAIL>>,
 *        Tim Sherwood <<EMAIL>>
 *
 *        University of California, San Diego
 *        Department of Computer Science and Engineering
 *        9500 Gilman Drive, Dept 0114
 *        La Jolla CA 92093-0114 USA
 *
 *
 * Copyright 2001, 2002, 2003, 2004, 2005 The Regents of the University of
 * California All Rights Reserved
 *
 * Permission to use, copy, modify and distribute any part of this
 * SimPoint Toolkit for educational, non-profit, and industry research
 * purposes, without fee, and without a written agreement is hereby
 * granted, provided that the above copyright notice, this paragraph and
 * the following four paragraphs appear in all copies and every modified
 * file.   
 *
 * Permission is not granted to include SimPoint into a commercial product.
 * Those desiring to incorporate this SimPoint Toolkit into commercial
 * products should contact the Technology Transfer Office, University of
 * California, San Diego, 9500 Gilman Drive, La Jolla, CA 92093-0910, Ph:
 * (619) 534-5815, FAX: (619) 534-7345.
 *
 * IN NO EVENT SHALL THE UNIVERSITY OF CALIFORNIA BE LIABLE TO ANY PARTY
 * FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES,
 * INCLUDING LOST PROFITS, ARISING OUT OF THE USE OF THE SimPoint
 * Toolkit, EVEN IF THE UNIVERSITY OF CALIFORNIA HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THE SimPoint Toolkit PROVIDED HEREIN IS ON AN "AS IS" BASIS, AND THE
 * UNIVERSITY OF CALIFORNIA HAS NO OBLIGATION TO PROVIDE MAINTENANCE,
 * SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS. THE UNIVERSITY OF
 * CALIFORNIA MAKES NO REPRESENTATIONS AND EXTENDS NO WARRANTIES OF ANY
 * KIND, EITHER IMPLIED OR EXPRESS, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR
 * PURPOSE, OR THAT THE USE OF THE SimPoint Toolkit WILL NOT INFRINGE ANY
 * PATENT, TRADEMARK OR OTHER RIGHTS.
 *
 * No non-profit user may place any restrictions on the use of this
 * software, including as modified by the user, by any other authorized
 * user.
 *
 ************************************************************************/


#ifndef K_MEANS_H
#define K_MEANS_H

/***********************************************************************
 * File: KMeans.h
 * Author: Greg Hamerly
 * Date: 8/20/2002
 *
 * This class has static methods to perform tasks related to k-means.
 ***********************************************************************/

#include "Dataset.h"

class KMeans {
    public:
        // Initialize centers randomly. The centers out parameter must be
        // pre-initialized.
        static void initializeRandomly(int randSeed, const Dataset &data,
                Dataset *centers);

        // Initialize the centers using the furthest-first heuristic (due to
        // Hochbaum & Shmoys). The randomness comes in choosing the first
        // center; after that it is deterministic.
        static void initializeFurthestFirst(int randSeed, const Dataset &data,
                Dataset *centers);

        // Run the k-means algorithm, starting with centers, and storing
        // the end result in centers.
        static void runKMeans(const Dataset &data, Dataset *centers, 
                              int maxIterations);

        // For each datapoint in data, find the closest center and its
        // distance, and place the results in labels and dists.  dists can be
        // NULL (in which case the distances are not recorded). labels cannot
        // be NULL.
        static void findLabelsAndDists(const Dataset &data, const Dataset &centers,
                                       vector<int> *labels, Datapoint *dists = NULL);

        // Find the number of occurrences of each number in labels, and
        // store the count in the weights (out) parameter.
        static void findWeights(const vector<int> &labels, vector<int> *weights);

        // Find the sum of the squares of the distance between each data
        // point and its closest center.
        static double distortion(const Dataset &data, const vector<int> &labels,
                           const Dataset &centers, Datapoint *distortionPerCluster = 0);

        // Find the BIC score for this dataset.
        static double bicScore(const Dataset &data, const Dataset &centers);
};

#endif

