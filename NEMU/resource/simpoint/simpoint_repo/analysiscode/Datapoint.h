/***********************************************************************
 *  __________________________________________________________________
 *
 *              _____ _           ____        _       __
 *             / ___/(_)___ ___  / __ \____  (_)___  / /_
 *             \__ \/ / __ `__ \/ /_/ / __ \/ / __ \/ __/
 *            ___/ / / / / / / / ____/ /_/ / / / / / /_
 *           /____/_/_/ /_/ /_/_/    \____/_/_/ /_/\__/
 *
 *  __________________________________________________________________
 *
 * This file is part of the SimPoint Toolkit written by <PERSON>,
 * <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> as part of
 * Efficient Simulation Project at UCSD.  If you find this toolkit useful please
 * cite the following paper published at ASPLOS 2002.
 *
 *  Timothy Sherwood, Erez Perelman, Greg Hamerly and Brad Calder,
 *  Automatically Characterizing Large Scale Program Behavior , In the
 *  10th International Conference on Architectural Support for Programming
 *  Languages and Operating Systems, October 2002.
 *
 * Contact info:
 *        Brad Calder <<EMAIL>>, (858) 822 - 1619
 *        Greg Hamerly <<EMAIL>>,
 *        Erez Perelman <<EMAIL>>,
 *        Jeremy Lau <<EMAIL>>,
 *        Tim Sherwood <<EMAIL>>
 *
 *        University of California, San Diego
 *        Department of Computer Science and Engineering
 *        9500 Gilman Drive, Dept 0114
 *        La Jolla CA 92093-0114 USA
 *
 *
 * Copyright 2001, 2002, 2003, 2004, 2005 The Regents of the University of
 * California All Rights Reserved
 *
 * Permission to use, copy, modify and distribute any part of this
 * SimPoint Toolkit for educational, non-profit, and industry research
 * purposes, without fee, and without a written agreement is hereby
 * granted, provided that the above copyright notice, this paragraph and
 * the following four paragraphs appear in all copies and every modified
 * file.   
 *
 * Permission is not granted to include SimPoint into a commercial product.
 * Those desiring to incorporate this SimPoint Toolkit into commercial
 * products should contact the Technology Transfer Office, University of
 * California, San Diego, 9500 Gilman Drive, La Jolla, CA 92093-0910, Ph:
 * (619) 534-5815, FAX: (619) 534-7345.
 *
 * IN NO EVENT SHALL THE UNIVERSITY OF CALIFORNIA BE LIABLE TO ANY PARTY
 * FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES,
 * INCLUDING LOST PROFITS, ARISING OUT OF THE USE OF THE SimPoint
 * Toolkit, EVEN IF THE UNIVERSITY OF CALIFORNIA HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THE SimPoint Toolkit PROVIDED HEREIN IS ON AN "AS IS" BASIS, AND THE
 * UNIVERSITY OF CALIFORNIA HAS NO OBLIGATION TO PROVIDE MAINTENANCE,
 * SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS. THE UNIVERSITY OF
 * CALIFORNIA MAKES NO REPRESENTATIONS AND EXTENDS NO WARRANTIES OF ANY
 * KIND, EITHER IMPLIED OR EXPRESS, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR
 * PURPOSE, OR THAT THE USE OF THE SimPoint Toolkit WILL NOT INFRINGE ANY
 * PATENT, TRADEMARK OR OTHER RIGHTS.
 *
 * No non-profit user may place any restrictions on the use of this
 * software, including as modified by the user, by any other authorized
 * user.
 *
 ************************************************************************/

#ifndef DATAPOINT_H
#define DATAPOINT_H

/***********************************************************************
 * File: Datapoint.h
 * Author: Greg Hamerly
 * Date: 8/20/2002
 *
 * A Datapoint is useful for representing a vector of double values, with
 * extra features.
 ***********************************************************************/

#include <vector>
#include <cstdio>
#include <iostream>

using namespace std;

class Datapoint : public vector<double> {
    public:
        Datapoint();
        Datapoint(unsigned int length);
        Datapoint(const Datapoint &dp);

        double distSquared(const Datapoint &dp) const;

        void fill(double value);

        int maxNdx(int start = 0, int end = -1, double *value = 0) const;

        Datapoint &operator+=(const Datapoint &dp);
        Datapoint &operator/=(double d);

        void multAndAdd(const Datapoint &dp, double d);

        Datapoint operator-(const Datapoint &dp) const;


        // write out all the data with a length indicator in advance
        // (format: "length: (*this)[0] (*this)[1]...")
        void write(ostream &os) const;
        void write(FILE *out) const;

        void read(istream &is);
        void read(FILE *in);

        // write/read in binary format (PLATFORM-SPECIFIC)
        void writeBinary(ostream &os) const;
        void writeBinary(FILE *out) const;

        void readBinary(istream &is);
        void readBinary(FILE *in);
};


// print it out in a human-readable way
ostream &operator<<(ostream &os, const Datapoint &dp);

#endif

