/***************************************************************************************
* Copyright (c) 2014-2021 Zihao Yu, Nanjing University
*
* NEMU is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
*          http://license.coscl.org.cn/MulanPSL2
*
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
*
* See the Mulan PSL v2 for more details.
***************************************************************************************/

#include <utils.h>

#ifdef CONFIG_SHARE
NEMUState nemu_state = { .state = NEMU_RUNNING };
#else
NEMUState nemu_state = { .state = NEMU_STOP };
#endif // CONFIG_SHARE

int is_exit_status_bad() {
  int good = (nemu_state.state == NEMU_END && nemu_state.halt_ret == 0) ||
    (nemu_state.state == NEMU_QUIT);
  if (!good) {
    Log("NEMU exit with bad state: %i, halt ret: %i", nemu_state.state, nemu_state.halt_ret);
  } else {
    Log("NEMU exit with good state: %i, halt ret: %i", nemu_state.state, nemu_state.halt_ret);
  }
  extern void log_close();
  log_close();
  return !good;
}
