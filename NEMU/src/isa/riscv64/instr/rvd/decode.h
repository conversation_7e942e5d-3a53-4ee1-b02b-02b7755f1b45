/***************************************************************************************
* Copyright (c) 2014-2021 <PERSON>ih<PERSON> Yu, Nanjing University
*
* NEMU is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
*          http://license.coscl.org.cn/MulanPSL2
*
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
*
* See the Mulan PSL v2 for more details.
***************************************************************************************/

def_THelper(op_fp_d) {
#ifndef CONFIG_FPU_NONE
  // RV32D
  def_INSTR_IDTAB("00000 ?? ????? ????? ??? ????? ????? ??", fr  , faddd);
  def_INSTR_IDTAB("00001 ?? ????? ????? ??? ????? ????? ??", fr  , fsubd);
  def_INSTR_IDTAB("00010 ?? ????? ????? ??? ????? ????? ??", fr  , fmuld);
  def_INSTR_IDTAB("00011 ?? ????? ????? ??? ????? ????? ??", fr  , fdivd);
  def_INSTR_IDTAB("01011 ?? 00000 ????? ??? ????? ????? ??", fr  , fsqrtd);
  def_INSTR_IDTAB("00100 ?? ????? ????? 000 ????? ????? ??", fr  , fsgnjd);
  def_INSTR_IDTAB("00100 ?? ????? ????? 001 ????? ????? ??", fr  , fsgnjnd);
  def_INSTR_IDTAB("00100 ?? ????? ????? 010 ????? ????? ??", fr  , fsgnjxd);
  def_INSTR_IDTAB("00101 ?? ????? ????? 000 ????? ????? ??", fr  , fmind);
  def_INSTR_IDTAB("00101 ?? ????? ????? 001 ????? ????? ??", fr  , fmaxd);
  def_INSTR_IDTAB("01000 00 00001 ????? ??? ????? ????? ??", fr  , fcvt_s_d);
  def_INSTR_IDTAB("01000 01 00000 ????? ??? ????? ????? ??", fr  , fcvt_d_s);
  def_INSTR_IDTAB("10100 ?? ????? ????? 010 ????? ????? ??", fr2r, feqd);
  def_INSTR_IDTAB("10100 ?? ????? ????? 001 ????? ????? ??", fr2r, fltd);
  def_INSTR_IDTAB("10100 ?? ????? ????? 000 ????? ????? ??", fr2r, fled);
  def_INSTR_IDTAB("11100 ?? 00000 ????? 001 ????? ????? ??", fr2r, fclassd);
  def_INSTR_IDTAB("11000 ?? 00000 ????? ??? ????? ????? ??", fr2r, fcvt_w_d);
  def_INSTR_IDTAB("11000 ?? 00001 ????? ??? ????? ????? ??", fr2r, fcvt_wu_d);
  def_INSTR_IDTAB("11010 ?? 00000 ????? ??? ????? ????? ??", r2fr, fcvt_d_w);
  def_INSTR_IDTAB("11010 ?? 00001 ????? ??? ????? ????? ??", r2fr, fcvt_d_wu);

  // RV64D
  def_INSTR_IDTAB("11000 ?? 00010 ????? ??? ????? ????? ??", fr2r, fcvt_l_d);
  def_INSTR_IDTAB("11000 ?? 00011 ????? ??? ????? ????? ??", fr2r, fcvt_lu_d);
  def_INSTR_IDTAB("11100 ?? 00000 ????? 000 ????? ????? ??", fr2r, fmv_x_d);
  def_INSTR_IDTAB("11010 ?? 00010 ????? ??? ????? ????? ??", r2fr, fcvt_d_l);
  def_INSTR_IDTAB("11010 ?? 00011 ????? ??? ????? ????? ??", r2fr, fcvt_d_lu);
  def_INSTR_IDTAB("11110 ?? 00000 ????? 000 ????? ????? ??", r2fr, fmv_d_x);
#endif // CONFIG_FPU_NONE

  return EXEC_ID_inv;
}

def_THelper(fmadd_d_dispatch) {
#ifndef CONFIG_FPU_NONE
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10000 11", fmaddd);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10001 11", fmsubd);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10010 11", fnmsubd);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10011 11", fnmaddd);
#endif // CONFIG_FPU_NONE
  return EXEC_ID_inv;
}
