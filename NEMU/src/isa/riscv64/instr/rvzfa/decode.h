#ifdef CONFIG_RV_ZFA
def_THelper(op_zfa) {

  def_INSTR_IDTAB("11110 00 00001 ????? 000 ????? ????? ??", r2fr, fli_s);
  def_INSTR_IDTAB("00101 00 ????? ????? 010 ????? ????? ??",   fr, fminm_s);
  def_INSTR_IDTAB("00101 00 ????? ????? 011 ????? ????? ??",   fr, fmaxm_s);
  def_INSTR_IDTAB("01000 00 00100 ????? ??? ????? ????? ??",   fr, fround_s);
  def_INSTR_IDTAB("01000 00 00101 ????? ??? ????? ????? ??",   fr, froundnx_s);
  def_INSTR_IDTAB("10100 00 ????? ????? 100 ????? ????? ??", fr2r, fleq_s);
  def_INSTR_IDTAB("10100 00 ????? ????? 101 ????? ????? ??", fr2r, fltq_s);

  def_INSTR_IDTAB("11110 01 00001 ????? 000 ????? ????? ??", r2fr, fli_d);
  def_INSTR_IDTAB("00101 01 ????? ????? 010 ????? ????? ??",   fr, fminm_d);
  def_INSTR_IDTAB("00101 01 ????? ????? 011 ????? ????? ??",   fr, fmaxm_d);
  def_INSTR_IDTAB("01000 01 00100 ????? ??? ????? ????? ??",   fr, fround_d);
  def_INSTR_IDTAB("01000 01 00101 ????? ??? ????? ????? ??",   fr, froundnx_d);
  def_INSTR_IDTAB("10100 01 ????? ????? 100 ????? ????? ??", fr2r, fleq_d);
  def_INSTR_IDTAB("10100 01 ????? ????? 101 ????? ????? ??", fr2r, fltq_d);
  def_INSTR_IDTAB("11000 01 01000 ????? 001 ????? ????? ??", fr2r, fcvtmod_w_d);

#ifdef CONFIG_RV_ZFH
  def_INSTR_IDTAB("11110 10 00001 ????? 000 ????? 1010011", r2fr, fli_h);
  def_INSTR_IDTAB("00101 10 ????? ????? 010 ????? 1010011",   fr, fminm_h);
  def_INSTR_IDTAB("00101 10 ????? ????? 011 ????? 1010011",   fr, fmaxm_h);
  def_INSTR_IDTAB("01000 10 00100 ????? ??? ????? 1010011",   fr, fround_h);
  def_INSTR_IDTAB("01000 10 00101 ????? ??? ????? 1010011",   fr, froundnx_h);
  def_INSTR_IDTAB("10100 10 ????? ????? 100 ????? 1010011", fr2r, fleq_h);
  def_INSTR_IDTAB("10100 10 ????? ????? 101 ????? 1010011", fr2r, fltq_h);
#endif // CONFIG_RV_ZFH

  return EXEC_ID_inv;
}
#endif // CONFIG_RV_ZFA
