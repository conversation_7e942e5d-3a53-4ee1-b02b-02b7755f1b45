#ifdef CONFIG_RV_ZFH_MIN
def_THelper(op_zfh) {
    def_INSTR_IDTAB("11110 ?? 00000 ????? 000 ????? ????? ??", r2fr, fmv_h_x);
    def_INSTR_IDTAB("11100 ?? 00000 ????? 000 ????? ????? ??", fr2r, fmv_x_h);
    def_INSTR_IDTAB("01000 00 00010 ????? ??? ????? ????? ??", fr  , fcvt_s_h);
    def_INSTR_IDTAB("01000 10 00000 ????? ??? ????? ????? ??", fr  , fcvt_h_s);
    def_INSTR_IDTAB("01000 01 00010 ????? ??? ????? ????? ??", fr  , fcvt_d_h);
    def_INSTR_IDTAB("01000 10 00001 ????? ??? ????? ????? ??", fr  , fcvt_h_d);
    #ifdef CONFIG_RV_ZFH
      //RV32Zfh
      def_INSTR_IDTAB("00000 ?? ????? ????? ??? ????? ????? ??", fr  , faddh);
      def_INSTR_IDTAB("00001 ?? ????? ????? ??? ????? ????? ??", fr  , fsubh);
      def_INSTR_IDTAB("00010 ?? ????? ????? ??? ????? ????? ??", fr  , fmulh);
      def_INSTR_IDTAB("00011 ?? ????? ????? ??? ????? ????? ??", fr  , fdivh);
      def_INSTR_IDTAB("01011 ?? 00000 ????? ??? ????? ????? ??", fr  , fsqrth);
      def_INSTR_IDTAB("00100 ?? ????? ????? 000 ????? ????? ??", fr  , fsgnjh);
      def_INSTR_IDTAB("00100 ?? ????? ????? 001 ????? ????? ??", fr  , fsgnjnh);
      def_INSTR_IDTAB("00100 ?? ????? ????? 010 ????? ????? ??", fr  , fsgnjxh);
      def_INSTR_IDTAB("00101 ?? ????? ????? 000 ????? ????? ??", fr  , fminh);
      def_INSTR_IDTAB("00101 ?? ????? ????? 001 ????? ????? ??", fr  , fmaxh);
      def_INSTR_IDTAB("10100 ?? ????? ????? 010 ????? ????? ??", fr2r, feqh);
      def_INSTR_IDTAB("10100 ?? ????? ????? 001 ????? ????? ??", fr2r, flth);
      def_INSTR_IDTAB("10100 ?? ????? ????? 000 ????? ????? ??", fr2r, fleh);
      def_INSTR_IDTAB("11100 ?? 00000 ????? 001 ????? ????? ??", fr2r, fclassh);
      def_INSTR_IDTAB("11000 10 00000 ????? ??? ????? ????? ??", fr2r, fcvt_w_h);
      def_INSTR_IDTAB("11000 10 00001 ????? ??? ????? ????? ??", fr2r, fcvt_wu_h);
      def_INSTR_IDTAB("11010 ?? 00000 ????? ??? ????? ????? ??", r2fr, fcvt_h_w);
      def_INSTR_IDTAB("11010 ?? 00001 ????? ??? ????? ????? ??", r2fr, fcvt_h_wu);
      
      // RV64Zfh
      def_INSTR_IDTAB("11000 ?? 00010 ????? ??? ????? ????? ??", fr2r, fcvt_l_h);
      def_INSTR_IDTAB("11000 ?? 00011 ????? ??? ????? ????? ??", fr2r, fcvt_lu_h);
      def_INSTR_IDTAB("11010 ?? 00010 ????? ??? ????? ????? ??", r2fr, fcvt_h_l);
      def_INSTR_IDTAB("11010 ?? 00011 ????? ??? ????? ????? ??", r2fr, fcvt_h_lu);
    #endif // CONFIG_RV_ZFH
  return EXEC_ID_inv;
}
#endif // CONFIG_RV_ZFH_MIN
#ifdef CONFIG_RV_ZFH
def_THelper(fmadd_h_dispatch) {
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10000 11", fmaddh);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10001 11", fmsubh);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10010 11", fnmsubh);
  def_INSTR_TAB("????? ?? ????? ????? ??? ????? 10011 11", fnmaddh);
  return EXEC_ID_inv;
}
#endif // CONFIG_RV_ZFH