/***************************************************************************************
* Copyright (c) 2014-2021 <PERSON>ao Yu, Nanjing University
*
* NEMU is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
*          http://license.coscl.org.cn/MulanPSL2
*
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
*
* See the Mulan PSL v2 for more details.
***************************************************************************************/

#if defined(CONFIG_DEBUG) || defined(CONFIG_SHARE)
def_THelper(atomic) {
  def_INSTR_TAB("00010 ?? 00000 ????? 011 ????? ????? ??", lr_d);
  def_INSTR_TAB("00011 ?? ????? ????? 011 ????? ????? ??", sc_d);
  def_INSTR_TAB("00001 ?? ????? ????? 011 ????? ????? ??", amoswap_d);
  def_INSTR_TAB("00000 ?? ????? ????? 011 ????? ????? ??", amoadd_d);
  def_INSTR_TAB("00100 ?? ????? ????? 011 ????? ????? ??", amoxor_d);
  def_INSTR_TAB("01100 ?? ????? ????? 011 ????? ????? ??", amoand_d);
  def_INSTR_TAB("01000 ?? ????? ????? 011 ????? ????? ??", amoor_d);
  def_INSTR_TAB("10000 ?? ????? ????? 011 ????? ????? ??", amomin_d);
  def_INSTR_TAB("10100 ?? ????? ????? 011 ????? ????? ??", amomax_d);
  def_INSTR_TAB("11000 ?? ????? ????? 011 ????? ????? ??", amominu_d);
  def_INSTR_TAB("11100 ?? ????? ????? 011 ????? ????? ??", amomaxu_d);

  def_INSTR_TAB("00010 ?? 00000 ????? 010 ????? ????? ??", lr_w);
  def_INSTR_TAB("00011 ?? ????? ????? 010 ????? ????? ??", sc_w);
  def_INSTR_TAB("00001 ?? ????? ????? 010 ????? ????? ??", amoswap_w);
  def_INSTR_TAB("00000 ?? ????? ????? 010 ????? ????? ??", amoadd_w);
  def_INSTR_TAB("00100 ?? ????? ????? 010 ????? ????? ??", amoxor_w);
  def_INSTR_TAB("01100 ?? ????? ????? 010 ????? ????? ??", amoand_w);
  def_INSTR_TAB("01000 ?? ????? ????? 010 ????? ????? ??", amoor_w);
  def_INSTR_TAB("10000 ?? ????? ????? 010 ????? ????? ??", amomin_w);
  def_INSTR_TAB("10100 ?? ????? ????? 010 ????? ????? ??", amomax_w);
  def_INSTR_TAB("11000 ?? ????? ????? 010 ????? ????? ??", amominu_w);
  def_INSTR_TAB("11100 ?? ????? ????? 010 ????? ????? ??", amomaxu_w);

#ifdef CONFIG_RV_ZACAS
  def_INSTR_TAB("00101 ?? ????? ????? 010 ????? ????? ??", amocas_w);
  def_INSTR_TAB("00101 ?? ????? ????? 011 ????? ????? ??", amocas_d);
  def_INSTR_TAB("00101 ?? ????? ????? 100 ????? ????? ??", amocas_q);
#endif

  return EXEC_ID_inv;
}
#endif
