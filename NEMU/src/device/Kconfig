menuconfig DEVICE
  bool "Devices"
  default y
  help
    Provide device support for NEMU.

if DEVICE

config HAS_PORT_IO
  depends on !SHARE
  bool
  default y

menuconfig HAS_SERIAL
  depends on !SHARE
  bool "Enable serial"
  default y

if HAS_SERIAL
config SERIAL_PORT
  depends on HAS_PORT_IO
  hex "Port address of the serial controller"
  default 0x3f8

config SERIAL_MMIO
  hex "MMIO address of the serial controller"
  default 0xa10003f8

config SERIAL_INPUT_FIFO
  bool "Enable input FIFO with /tmp/nemu.serial"
  default n

endif # HAS_SERIAL

menuconfig HAS_UARTLITE
  bool "Enable uartlite"
  default n

if HAS_UARTLITE
config UARTLITE_PORT
  depends on HAS_PORT_IO
  hex "Port address of uartlite controller"
  default 0x3f8

config UARTLITE_MMIO
  hex "MMIO address of uartlite controller"
  default 0xa10003f8

config UARTLITE_INPUT_FIFO
  bool "Enable input FIFO with /tmp/nemu.serial"
  default n
config UARTLITE_ASSERT_FOUR
  bool "Allow 4 byte write to allow community drivers"
endif # HAS_UARTLITE

menuconfig HAS_UART_SNPS
  depends on !SHARE
  bool "Enable uart_snps"
  default n

if HAS_UART_SNPS
# config UARTLITE_PORT
#   depends on HAS_PORT_IO
#    hex "Port address of uartlite controller"
##   default 0x3f8

config UART_SNPS_MMIO
  hex "MMIO address of uart-snps controller"
  default 0x310b0000

config UAR_SNPS_INPUT_FIFO
  bool "Enable input FIFO with /tmp/nemu.serial"
  default n
endif # HAS_UART_SNPS

menuconfig HAS_PLIC
  depends on !SHARE
  bool "Enable PLIC"
  default n

if HAS_PLIC
config PLIC_ADDRESS
  hex "base address of PLIC"
  default 0x3c000000
endif

menuconfig HAS_TIMER
  depends on !SHARE
  bool "Enable timer"
  default y

if HAS_TIMER
config RTC_PORT
  depends on HAS_PORT_IO
  hex "Port address of the timer"
  default 0x48

config RTC_MMIO
  hex "MMIO address of the timer"
  default 0xa1000048
endif # HAS_TIMER

menuconfig HAS_KEYBOARD
  depends on !SHARE
  bool "Enable keyboard"
  default y

if HAS_KEYBOARD
config I8042_DATA_PORT
  depends on HAS_PORT_IO
  hex "Port address of the keyboard controller"
  default 0x60

config I8042_DATA_MMIO
  hex "MMIO address of the keyboard controller"
  default 0xa1000060
endif # HAS_KEYBOARD

menuconfig HAS_VGA
  bool "Enable VGA"
  default y

if HAS_VGA
config FB_ADDR
  hex "Physical address of the VGA frame buffer"
  default 0xa0000000

config VGA_CTL_PORT
  depends on HAS_PORT_IO
  hex "Port address of the VGA controller"
  default 0x100

config VGA_CTL_MMIO
  hex "MMIO address of the VGA controller"
  default 0xa1000100

config VGA_SHOW_SCREEN
  bool "Enable SDL SCREEN"
  default n

choice
  prompt "Screen Size"
  default VGA_SIZE_400x300
config VGA_SIZE_400x300
  bool "400 x 300"
config VGA_SIZE_800x600
  bool "800 x 600"
endchoice
endif # HAS_VGA

menuconfig HAS_AUDIO
  depends on !SHARE
  bool "Enable audio"
  default y

if HAS_AUDIO
config SB_ADDR
  hex "Physical address of the audio stream buffer"
  default 0xa0800000

config SB_SIZE
  hex "Size of the audio stream buffer"
  default 0x10000

config AUDIO_CTL_PORT
  depends on HAS_PORT_IO
  hex "Port address of the audio controller"
  default 0x200

config AUDIO_CTL_MMIO
  hex "MMIO address of the audio controller"
  default 0xa1000200
endif # HAS_AUDIO

menuconfig HAS_DISK
  depends on !SHARE
  bool "Enable disk"
  default y

if HAS_DISK
config DISK_CTL_PORT
  depends on HAS_PORT_IO
  hex "Port address of the disk controller"
  default 0x300

config DISK_CTL_MMIO
  hex "MMIO address of the disk controller"
  default 0xa1000300

config DISK_IMG_PATH
  string "The path of disk image"
  default ""
endif # HAS_DISK

menuconfig HAS_SDCARD
  depends on !SHARE
  bool "Enable sdcard"
  default y

if HAS_SDCARD
config SDCARD_CTL_MMIO
  hex "MMIO address of the sdcard controller"
  default 0xa3000000

config SDCARD_IMG_PATH
  string "The path of sdcard image"
  default ""
endif # HAS_SDCARD

menuconfig HAS_FLASH
  bool "Enable flash"
  default n

if HAS_FLASH
config FLASH_PRESET_CONTENT
  string "The content of flash preset"
  default "0x0010029b,0x01f29293,0x00028067"

config FLASH_START_ADDR
  hex "Flash start addr"
  default 0x10000000

config FLASH_SIZE
  hex "Flash size"
  default 0x00000100

config FLASH_IMG_PATH
  string "The path of flash image"
  default ""
endif # HAS_FLASH

endif # DEVICE
