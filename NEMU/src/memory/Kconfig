menu "Memory Configuration"

config MBASE
  hex "Memory base address"
  default 0x08048000 if MODE_USER
  default 0x80000000

config USE_SPARSEMM
  bool "Use sparse memory model"
  default n

config MSIZE
  hex "Memory size"
  default 0x8000000

config PADDRBITS
  int "The number of paddr bits"
  default 36

config STORE_LOG
  bool "Enable store log"
  default n

config STORE_LOG_SIZE
  int "The size of store log"
  depends on STORE_LOG
  default 80000

config SPEC_GAP
  int "distance between speculative and stable snapshot"
  depends on LIGHTQS
  default 40

config LIGHTQS
  bool "Enable lightssss"
  depends on STORE_LOG
  default n

config LIGHTQS_DEBUG
  bool "lightssss debug log"
  depends on LIGHTQS
  default n

config BBL_OFFSET_WITH_CPT
  hex "The offset of bbl / baremetal app with using gcpt"
  default 0x100000

config RESET_FROM_MMIO
  bool "Reset from mmio address"
  default n

config PC_RESET_OFFSET
  hex "Offset of reset vector from the base of memory"
  default 0 if MODE_USER
  default 0x100000

config MMIO_RESET_VECTOR
  hex "MMIO based reset vector"
  depends on RESET_FROM_MMIO
  default 0x10000000

config USE_MMAP
  bool "Allocate guest physical memory with mmap()"
  default y

config ENABLE_MEM_DEDUP
  depends on SHARE
  depends on !USE_MMAP
  bool "When used as reference, use memory provided by DUT"
  default n

config MEM_RANDOM
  depends on MODE_SYSTEM && !DIFFTEST
  bool "Initialize the memory with random values"
  default y
  help
    This may help to find undefined behaviors.

config MEM_COMPRESS
  depends on MODE_SYSTEM && !SHARE
  bool "Initialize the memory with a compressed gz file"
  default n
  help
    Must have zlib installed.

config ENABLE_CONFIG_MMIO_SPACE
  bool "Configure the MMIO space"
  default y
  help
    The MMIO space can be freely configured without the need to actually specify device.

config MMIO_SPACE_RANGE
  string "MMIO address space range"
  default "0x0, 0x7FFFFFFF"
  depends on ENABLE_CONFIG_MMIO_SPACE
  help
    Please use the upper and lower boundary pairing for configuration, the specified upper and lower boundary addresses are included. For example: 0x0, 0x7FFFFFFF, 0x100000000, 0x100001000
    This will specify the MMIO address space area as [0x0, 0x7FFFFFFF] and [0x100000000, 0x100001000].
    Specifying the MMIO address space is not the same as specifying the peripherals, this is just to ensure PMA for RISC-V.

endmenu #MEMORY
