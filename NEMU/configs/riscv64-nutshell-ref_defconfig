#
# Automatically generated file; DO NOT EDIT.
# NEMU Configuration Menu
#
# CONFIG_ISA_x86 is not set
# CONFIG_ISA_mips32 is not set
# CONFIG_ISA_riscv32 is not set
CONFIG_ISA_riscv64=y
CONFIG_ISA="riscv64"
CONFIG_ILEN_MIN=2
CONFIG_ISA64=y

#
# ISA-dependent Options for riscv64
#
CONFIG_CLINT_MMIO=0x38000000
# CONFIG_MULTICORE_DIFF is not set
# CONFIG_RVB is not set
# CONFIG_RVK is not set
# CONFIG_RV_ZICOND is not set
# CONFIG_RVV is not set
# CONFIG_RV_DEBUG is not set
# CONFIG_RVH is not set
# CONFIG_RV_PMP_ENTRY_0 is not set
CONFIG_RV_PMP_ENTRY_16=y
# CONFIG_RV_PMP_ENTRY_64 is not set
CONFIG_RV_PMP_CSR=y
CONFIG_RV_PMP_NUM=16
CONFIG_RV_PMP_ACTIVE_NUM=4
CONFIG_PMP_GRANULARITY=2
# CONFIG_RV_PMP_CHECK is not set
# CONFIG_RV_SVINVAL is not set
CONFIG_RV_MSTATUS_FS_WRITABLE=y
CONFIG_MISA_UNCHANGEABLE=y
CONFIG_XTVEC_VECTORED_MODE=y
# CONFIG_TVAL_EX_II is not set
CONFIG_FS_CLEAN_STATE=y
# CONFIG_PMPTABLE_EXTENSION is not set
# CONFIG_EBREAK_AS_TRAP is not set
# end of ISA-dependent Options for riscv64

CONFIG_ENGINE_INTERPRETER=y
CONFIG_ENGINE="interpreter"
CONFIG_MODE_SYSTEM=y
# CONFIG_MODE_USER is not set

#
# Build Options
#
CONFIG_CC_GCC=y
# CONFIG_CC_GPP is not set
# CONFIG_CC_CLANG is not set
CONFIG_CC="gcc"
# CONFIG_CC_O0 is not set
# CONFIG_CC_O1 is not set
CONFIG_CC_O2=y
# CONFIG_CC_O3 is not set
CONFIG_CC_OPT="-O2"
# CONFIG_CC_LTO is not set
# CONFIG_CC_DEBUG is not set
# CONFIG_CC_ASAN is not set
# end of Build Options

#
# Testing and Debugging
#
# CONFIG_DEBUG is not set
# CONFIG_DIFFTEST_STORE_COMMIT is not set
CONFIG_DIFFTEST_REF_PATH="none"
CONFIG_DIFFTEST_REF_NAME="none"
# CONFIG_DETERMINISTIC is not set
# CONFIG_IQUEUE is not set
# CONFIG_MEMLOG is not set
# CONFIG_TRANSLOG is not set
# CONFIG_EXITLOG is not set
# CONFIG_TRACE_INST is not set
# CONFIG_TRACE_BB is not set
# CONFIG_SIMPOINT_LOG is not set
# end of Testing and Debugging

#
# Memory Configuration
#
CONFIG_MBASE=0x80000000
# CONFIG_USE_SPARSEMM is not set
CONFIG_MSIZE=0x80000000
CONFIG_PADDRBITS=32
CONFIG_STORE_LOG=y
# CONFIG_LIGHTQS is not set
# CONFIG_BR_LOG is not set
CONFIG_BBL_OFFSET_WITH_CPT=0x100000
CONFIG_RESET_FROM_MMIO=y
CONFIG_PC_RESET_OFFSET=0x0
CONFIG_MMIO_RESET_VECTOR=0x10000000
CONFIG_USE_MMAP=y
# CONFIG_MEM_RANDOM is not set
# end of Memory Configuration

CONFIG_DEVICE=y
# CONFIG_HAS_UARTLITE is not set
# CONFIG_HAS_VGA is not set
# CONFIG_HAS_FLASH is not set
# CONFIG_FPU_HOST is not set
# CONFIG_FPU_SOFT is not set
CONFIG_FPU_NONE=y
# CONFIG_AC_HOST is not set
CONFIG_AC_SOFT=y
# CONFIG_AC_NONE is not set

#
# Processor difftest reference config
#
CONFIG_SHARE=y
# CONFIG_DIFFTEST_REF_FOR_GEM5 is not set
# CONFIG_GUIDED_EXEC is not set
CONFIG_QUERY_REF=y
CONFIG_LARGE_COPY=y
# CONFIG_PANIC_ON_UNIMP_CSR is not set
# CONFIG_REF_STATUS is not set
# end of Processor difftest reference config

#
# Miscellaneous
#
CONFIG_TIMER_GETTIMEOFDAY=y
# CONFIG_TIMER_CLOCK_GETTIME is not set
# CONFIG_MEMORY_REGION_ANALYSIS is not set
# CONFIG_REPORT_ILLEGAL_INSTR is not set
CONFIG_RT_CHECK=y
CONFIG_INSTR_CNT_BY_INSTR_NORMAL=y
CONFIG_INSTR_CNT_BY_INSTR=y
CONFIG_ENABLE_INSTR_CNT=y
# end of Miscellaneous
