#
# Automatically generated file; DO NOT EDIT.
# NEMU Configuration Menu
#
# CONFIG_ISA_x86 is not set
# CONFIG_ISA_mips32 is not set
# CONFIG_ISA_riscv32 is not set
CONFIG_ISA_riscv64=y
CONFIG_ISA="riscv64"
CONFIG_ILEN_MIN=2
CONFIG_ISA64=y

#
# ISA-dependent Options for riscv64
#
CONFIG_HAS_CLINT=y
CONFIG_CLINT_MMIO=0x38000000
CONFIG_CYCLES_PER_MTIME_TICK=128
CONFIG_WFI_TIMEOUT_TICKS=8192
# CONFIG_MULTICORE_DIFF is not set
CONFIG_RVB=y
CONFIG_RV_CBO=y
CONFIG_RVK=y
CONFIG_RV_ZICOND=y
CONFIG_RV_ZFH=y
CONFIG_RV_ZFH_MIN=y
CONFIG_RV_ZFA=y
CONFIG_RVV=y
CONFIG_RV_ZVFH_MIN=y
CONFIG_RV_ZVFH=y
CONFIG_RV_DEBUG=y
CONFIG_RVH=y
# CONFIG_RV_SDEXT is not set
CONFIG_RV_SDTRIG=y
CONFIG_TDATA1_MCONTROL6=y
# CONFIG_TDATA1_ICOUNT is not set
# CONFIG_TDATA1_ITRIGGER is not set
# CONFIG_TDATA1_ETRIGGER is not set
CONFIG_TRIGGER_NUM=4
# CONFIG_SDTRIG_EXTRA is not set
CONFIG_RV_AIA=y
CONFIG_RV_IMSIC=y
CONFIG_GEILEN=5
CONFIG_RV_SSTC=y
CONFIG_RV_SMRNMI=y
CONFIG_RV_SMDBLTRP=y
# CONFIG_MDT_INIT is not set
CONFIG_RV_SSDBLTRP=y
CONFIG_NMIE_INIT=y
CONFIG_RV_ZICNTR=y
CONFIG_RV_CSR_TIME=y
CONFIG_RV_ZIHINTPAUSE=y
CONFIG_RV_ZIHPM=y
CONFIG_RV_CSR_MCOUNTINHIBIT=y
CONFIG_RV_CSR_MCOUNTINHIBIT_CNTR=y
CONFIG_RV_CSR_MCOUNTINHIBIT_HPM=y
# CONFIG_RV_PMP_ENTRY_0 is not set
CONFIG_RV_PMP_ENTRY_16=y
# CONFIG_RV_PMP_ENTRY_64 is not set
CONFIG_RV_PMP_CSR=y
CONFIG_RV_PMP_NUM=16
CONFIG_RV_PMP_ACTIVE_NUM=16
CONFIG_PMP_GRANULARITY=12
CONFIG_RV_PMP_CHECK=y
CONFIG_RV_SVINVAL=y
# CONFIG_RV_SV39 is not set
CONFIG_RV_SV48=y
CONFIG_RV_SVNAPOT=y
CONFIG_RV_SVPBMT=y
CONFIG_RV_SSNPM=y
CONFIG_RV_SMNPM=y
CONFIG_RV_SMMPM=y
CONFIG_RV_SSCOFPMF=y
CONFIG_RV_SHLCOFIDELEG=y
CONFIG_RV_SMSTATEEN=y
CONFIG_MISA_UNCHANGEABLE=y
CONFIG_XTVEC_VECTORED_MODE=y
CONFIG_TVAL_EX_II=y
CONFIG_FS_CLEAN_STATE=y
CONFIG_USE_XS_ARCH_CSRS=y
CONFIG_RVV_AGNOSTIC=y
CONFIG_RV_ZCMOP=y
CONFIG_RV_ZIMOP=y
CONFIG_RV_ZCB=y
CONFIG_RV_ZACAS=y
CONFIG_RV_ZAWRS=y
# end of ISA-dependent Options for riscv64

CONFIG_ENGINE_INTERPRETER=y
CONFIG_ENGINE="interpreter"
CONFIG_MODE_SYSTEM=y
# CONFIG_MODE_USER is not set

#
# Build Options
#
CONFIG_CC_GCC=y
# CONFIG_CC_GPP is not set
# CONFIG_CC_CLANG is not set
CONFIG_CC="gcc"
# CONFIG_CC_O0 is not set
# CONFIG_CC_O1 is not set
CONFIG_CC_O2=y
# CONFIG_CC_O3 is not set
CONFIG_CC_OPT="-O2"
# CONFIG_CC_NATIVE_ARCH is not set
# CONFIG_CC_LTO is not set
# CONFIG_CC_DEBUG is not set
# CONFIG_CC_ASAN is not set
# end of Build Options

#
# Testing and Debugging
#
# CONFIG_DEBUG is not set
CONFIG_DIFFTEST_STORE_COMMIT=y
CONFIG_DIFFTEST_REF_PATH="none"
CONFIG_DIFFTEST_REF_NAME="none"
# CONFIG_IQUEUE is not set
# CONFIG_MEMLOG is not set
# CONFIG_TRANSLOG is not set
# CONFIG_EXITLOG is not set
# CONFIG_TRACE_INST is not set
# CONFIG_TRACE_BB is not set
# CONFIG_SIMPOINT_LOG is not set
# end of Testing and Debugging

#
# Memory Configuration
#
CONFIG_MBASE=0x80000000
# CONFIG_USE_SPARSEMM is not set
CONFIG_MSIZE=0x7ff80000000
CONFIG_PADDRBITS=48
CONFIG_STORE_LOG=y
# CONFIG_LIGHTQS is not set
# CONFIG_BR_LOG is not set
CONFIG_BBL_OFFSET_WITH_CPT=0x100000
CONFIG_RESET_FROM_MMIO=y
CONFIG_PC_RESET_OFFSET=0x0
CONFIG_MMIO_RESET_VECTOR=0x10000000
CONFIG_USE_MMAP=y
# CONFIG_MEM_RANDOM is not set
CONFIG_ENABLE_CONFIG_MMIO_SPACE=y
CONFIG_MMIO_SPACE_RANGE="0x0, 0x7FFFFFFF"
# end of Memory Configuration

CONFIG_DEVICE=y
# CONFIG_HAS_UARTLITE is not set
# CONFIG_HAS_VGA is not set
CONFIG_HAS_FLASH=y
CONFIG_FLASH_PRESET_CONTENT="0x0010029b,0x01f29293,0x00028067"
CONFIG_FLASH_START_ADDR=0x10000000
CONFIG_FLASH_SIZE=0x100000
CONFIG_FLASH_IMG_PATH=""
# CONFIG_FPU_HOST is not set
CONFIG_FPU_SOFT=y
# CONFIG_FPU_NONE is not set
# CONFIG_AC_HOST is not set
# CONFIG_AC_SOFT is not set
CONFIG_AC_NONE=y
# CONFIG_VECTOR_AC_SOFT is not set
CONFIG_MMIO_AC_SOFT=y
CONFIG_AMO_AC_SOFT=y
CONFIG_RESERVATION_SET_WIDTH=6

#
# Processor difftest reference config
#
CONFIG_SHARE=y
# CONFIG_DIFFTEST_REF_FOR_GEM5 is not set
# CONFIG_DIFFTEST_STORE_COMMIT_AMO is not set
CONFIG_DIFFTEST_STORE_QUEUE_SIZE=64
CONFIG_GUIDED_EXEC=y
CONFIG_GUIDED_TVAL=y
CONFIG_QUERY_REF=y
CONFIG_LARGE_COPY=y
# CONFIG_PANIC_ON_UNIMP_CSR is not set
# CONFIG_REF_STATUS is not set
# end of Processor difftest reference config

#
# Miscellaneous
#
CONFIG_TIMER_GETTIMEOFDAY=y
# CONFIG_TIMER_CLOCK_GETTIME is not set
CONFIG_DETERMINISTIC=y
CONFIG_DISABLE_HOST_ALARM=y
# CONFIG_MEMORY_REGION_ANALYSIS is not set
# CONFIG_REPORT_ILLEGAL_INSTR is not set
CONFIG_RT_CHECK=y
CONFIG_INSTR_CNT_BY_INSTR_NORMAL=y
CONFIG_INSTR_CNT_BY_INSTR=y
CONFIG_ENABLE_INSTR_CNT=y
# end of Miscellaneous
