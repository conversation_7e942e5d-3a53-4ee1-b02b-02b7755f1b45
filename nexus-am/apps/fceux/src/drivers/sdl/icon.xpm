/* XPM */
static const char * icon_xpm[] = {
"32 32 20 1",
" 	c None",
".	c #040204",
"+	c #84A284",
"@	c #C42204",
"#	c #8482C4",
"$	c #FCFEFC",
"%	c #848284",
"&	c #648284",
"*	c #646284",
"=	c #444244",
"-	c #A4A284",
";	c #C4A284",
">	c #C48284",
",	c #A4CAF4",
"'	c #244244",
")	c #444204",
"!	c #442204",
"~	c #446244",
"{	c #646244",
"]	c #644244",
"                                ",
"               ........         ",
"          ...............       ",
"   ........................     ",
" ...........................+   ",
" ............@@..@@...........  ",
" .#............@@............$$ ",
" .##..........@@.@.....$$%%%%$$ ",
" &...........@....@$$$$$$%%&%$$ ",
" *&...............$$$$$$$%%&%$$ ",
" =&*.......-;;>;...$$,$$$%**&.. ",
"  '&&..............$$,,,%=)!~.. ",
"   ~&&............-%%##%*.~'=%& ",
"    *&&.....+%%****&&%%&*.&!!'  ",
"     **&%&***********&&&*~{'=   ",
"      ********=**~**~**~        ",
"       *****~******]            ",
"        **~***]'                ",
"        ~]==                    ",
"                                ",
" ..... .... .... .. ..@@      @@",
" ..... .... .... .. ..@@@    @@@",
" ..    ..   ..   .. .. @@@  @@@ ",
" ....  ..   ..   .. ..  @@@@@@  ",
" ....  ..   ...  .. ..   @@@@   ",
" ..    ..   ...  .. ..   @@@@   ",
" ..    ..   ..   .. ..  @@@@@@  ",
" ..    ..   ..   .. .. @@@  @@@ ",
" ..    .... .... .....@@@    @@@",
" ..    .... ....  ... @@      @@",
"                                ",
"                                "};
