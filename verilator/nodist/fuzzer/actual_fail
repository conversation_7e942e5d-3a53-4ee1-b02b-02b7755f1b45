#!/usr/bin/env python3
# pylint: disable=C0103,C0114,C0115,C0116,C0321,R0911
######################################################################
# DESCRIPTION: Fuzzer result checker
#
# Copyright 2019-2019 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU Lesser
# General Public License Version 3 or the Perl Artistic License Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
######################################################################

# This script is designed to rerun examples to see whether they have
# unexpected types of output besides the ones that afl-fuzz detects as
# such.

from glob import glob
from subprocess import getstatusoutput
from argparse import ArgumentParser


def interesting(s):
    if 'assert' in s: return 1
    if 'Assert' in s: return 1
    if 'Aborted' in s: return 1
    if 'terminate' in s:
        if 'unterminated' in s:
            return 0
        return 1
    if 'Segmentation' in s:
        return 1
    if 'internal error' in s:
        return 1
    return 0


def main():
    p = ArgumentParser()
    p.add_argument('--dir', default='out1/queue')
    args = p.parse_args()

    for infile in glob(args.dir + '/*'):
        # Input filenames are known not to contain spaces or other unusual
        # characters, therefore this works.
        status, output = getstatusoutput('../../bin/verilator_bin --cc ' +
                                         infile)
        if interesting(output):
            print(infile)
            print(status)
            print(output)


if __name__ == '__main__':
    main()
