# -*- Python -*-
# DESCRIPTION: Verilator: Internal C++ code lcov control file
#
# Copyright 2019-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

source_globs("src/*.cpp")
source_globs("src/*.h")
source_globs("src/*.l")
source_globs("src/*.y")
source_globs("src/obj_dbg/*.h")
source_globs("src/obj_dbg/*.cpp")
source_globs("include/*.c")
source_globs("include/*.cpp")
source_globs("include/*.h")
source_globs("include/*/*.h")
source_globs("include/*/*.cpp")
source_globs("include/*/*.c")

# Note *'s are removed when using fastcov
remove_source("/usr/*")
remove_source("*/include/sysc/*")
remove_source("*/V3Lexer_pregen.yy.cpp")
remove_source("*/V3PreLex_pregen.yy.cpp")
remove_source("*/verilog.c")
remove_source("*include/gtkwave/*")
# Something wrong in generation, unfortunately as would like this
#genhtml: ERROR: cannot read /svaha/wsnyder/SandBox/homecvs/v4/verilator/src/obj_dbg/verilog.y
#remove_source("*/src/obj_dbg/verilog.y")
remove_source("*test_regress/*")
remove_source("*examples/*")

# Remove collected coverage on each little test main file
# Would just be removed with remove_source in later step
remove_gcda_regexp(r'test_regress/.*/(Vt_|Vtop_).*\.gcda')

# Exclude line entirely, also excludes from function and branch coverage
exclude_line_regexp(r'\bv3fatalSrc\b')
exclude_line_regexp(r'\bfatalSrc\b')
exclude_line_regexp(r'\bVL_UNCOVERABLE\b')
exclude_line_regexp(r'\bVL_UNREACHABLE\b')
exclude_line_regexp(r'\bVL_FATAL')
exclude_line_regexp(r'\bUASSERT')
exclude_line_regexp(r'\bNUM_ASSERT')
exclude_line_regexp(r'\bERROR_RSVD_WORD')
exclude_line_regexp(r'\bV3ERROR_NA')
exclude_line_regexp(r'\bUINFO\b')
exclude_line_regexp(r'\bVL_DEFINE_DEBUG_FUNCTIONS\b')

# Exclude for branch coverage only
exclude_branch_regexp(r'\bdebug\(\)')
exclude_branch_regexp(r'\bassert\(')
exclude_branch_regexp(r'\bBROKEN_BASE_RTN\(')
exclude_branch_regexp(r'\bBROKEN_RTN\(')
exclude_branch_regexp(r'\bSELF_CHECK')

True
