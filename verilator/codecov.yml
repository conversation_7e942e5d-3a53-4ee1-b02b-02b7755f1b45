# DESCRIPTION: codecov.io config
#
# Copyright 2020-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
####################
# Validate:
#  curl --data-binary @codecov.yml https://codecov.io/validate

codecov:
  require_ci_to_pass: no

coverage:
  precision: 2
  round: down
  range: "50...100"
  ignore:
    - "ci"
    - "docs"
    - "examples"
    - "include/gtkwave"
    - "include/vltstd"
    - "test_regress"

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: yes
