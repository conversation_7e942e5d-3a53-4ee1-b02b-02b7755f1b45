#!/bin/bash
# DESCRIPTION: Wrap a verilator call to run a docker container
#
# Copyright 2020 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

docker pull verilator/verilator:$1 >/dev/null
docker run -ti -v ${PWD}:/work --user $(id -u):$(id -g) verilator/verilator:$1 "${@:2}"
