#!/usr/bin/env python3
# pylint: disable=C0114,C0209
#
# Copyright 2003-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify the Verilator internals under the terms
# of either the GNU Lesser General Public License Version 3 or the Perl
# Artistic License Version 2.0.
#
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
######################################################################

import re
import sys

print("// DESCR" + "IPTION: Generated by verilator_includer via makefile")

re_arg_d = re.compile(r'^-D([^=]+)=(.*)')

for arg in sys.argv[1:]:
    match_d = re_arg_d.match(arg)
    if match_d:
        print("#define %s %s" % (match_d.group(1), match_d.group(2)))
    else:
        print("#include \"%s\"" % (arg))

######################################################################
# Local Variables:
# compile-command: "./verilator_includer -Ddef=value -Ddef2=value2 file1.h file2.h"
# End:
