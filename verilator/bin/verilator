#!/usr/bin/env perl
######################################################################
#
# Copyright 2003-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
#
######################################################################

require 5.006_001;
use warnings;
use Getopt::Long;
use FindBin qw($RealBin $RealScript);
use IO::File;
use Pod::Usage;
use Cwd qw(realpath);

use strict;
use vars qw($Debug @Opt_Verilator_Sw);

#######################################################################
#######################################################################
# main

autoflush STDOUT 1;
autoflush STDERR 1;

$Debug = 0;
my $opt_gdb;
my $opt_rr;
my $opt_gdbbt;
my $opt_quiet_exit;
my $opt_unlimited_stack = 1;
my $opt_valgrind;

# No arguments can't do anything useful.  Give help
if ($#ARGV < 0) {
    pod2usage(-exitstatus => 2, -verbose => 0);
}

# Insert debugging options up front
push @ARGV, (split ' ', $ENV{VERILATOR_TEST_FLAGS} || "");

# We sneak a look at the flags so we can do some pre-environment checks
# All flags will hit verilator...
foreach my $sw (@ARGV) {
    push @Opt_Verilator_Sw, $sw;
}

Getopt::Long::config("no_auto_abbrev", "pass_through");
if (! GetOptions(
          # Major operating modes
          "help"             => \&usage,
          "debug"            => \&debug,
          # "version!"       => \&version,   # Also passthru'ed
          # Switches
          "gdb!"             => \$opt_gdb,
          "gdbbt!"           => \$opt_gdbbt,
          "quiet!"           => \$opt_quiet_exit,  # As -quiet implies -quiet-exit
          "quiet-exit!"      => \$opt_quiet_exit,
          "rr!"              => \$opt_rr,
          "unlimited-stack!" => \$opt_unlimited_stack,
          "valgrind!"        => \$opt_valgrind,
          # Additional parameters
          "<>"               => sub {},      # Ignored
    )) {
    pod2usage(-exitstatus => 2, -verbose => 0);
}

my $verilator_root = realpath("$RealBin/..");
if (defined $ENV{VERILATOR_ROOT}) {
    if ((!-d $ENV{VERILATOR_ROOT}) || $verilator_root ne realpath($ENV{VERILATOR_ROOT})) {
        warn "%Error: verilator: VERILATOR_ROOT is set to inconsistent path. Suggest leaving it unset.\n";
        warn "%Error: VERILATOR_ROOT=$ENV{VERILATOR_ROOT}\n";
        exit 1;
    }
} else {
    print "export VERILATOR_ROOT='$verilator_root'\n" if $Debug;
    $ENV{VERILATOR_ROOT} = $verilator_root;
}

if ($opt_gdbbt && !gdb_works()) {
    warn "-Info: --gdbbt ignored: gdb doesn't seem to be working\n" if $Debug;
    $opt_gdbbt = 0;
}

# Determine runtime flags and run
# Opt_Verilator_Sw is what we want verilator to see on its argc/argv.
# Starting with that, escape all special chars for the shell;
# The shell will undo the escapes and the verilator binary should
# then see exactly the contents of @Opt_Verilator_Sw.
my @quoted_sw = map { sh_escape($_) } @Opt_Verilator_Sw;
if ($opt_gdb) {
    # Generic GDB interactive
    run (ulimit_stack_unlimited()
         . aslr_off()
         . ($ENV{VERILATOR_GDB} || "gdb")
         . " " . verilator_bin()
         # Note, uncomment to set breakpoints before running:
         #  ." -ex 'break main'"

         # Note, we must use double-quotes ("run <switches>")
         # and not single ('run <switches>') below. Bash swallows
         # escapes as you would expect in a double-quoted string.
         # That's not true for a single-quoted string, where \'
         # actually terminates the string -- not what we want!
         . " -ex \"run " . join(' ', @quoted_sw) . "\""
         . " -ex 'set width 0'"
         . " -ex 'bt'");
} elsif ($opt_rr) {
    # Record with rr
    run (ulimit_stack_unlimited()
         . aslr_off()
         . "rr record " . verilator_bin()
         . " " . join(' ', @quoted_sw));
} elsif ($opt_gdbbt && $Debug) {
    # Run under GDB to get gdbbt
    run (ulimit_stack_unlimited()
         . aslr_off()
         . "gdb"
         . " " . verilator_bin()
         . " --batch --quiet --return-child-result"
         . " -ex \"run " . join(' ', @quoted_sw)."\""
         . " -ex 'set width 0'"
         . " -ex 'bt' -ex 'quit'");
} elsif ($opt_valgrind) {
    # Run under valgrind
    my $valgrind_bin = ($ENV{VERILATOR_VALGRIND} || "valgrind --error-exitcode=1 --max-stackframe=2815880"
        # Magic number sugested by valgrind, may need to be increased in future
        # if you get warnings. See: https://valgrind.org/docs/manual/manual-core.html#opt.max-stackframe
        );

    run (ulimit_stack_unlimited()
         . aslr_off()
         . $valgrind_bin
         . " " . verilator_bin()
         . " " . join(' ', @quoted_sw));
} elsif ($Debug) {
    # Debug
    run(ulimit_stack_unlimited()
        . aslr_off()
        . verilator_bin()
        . " " . join(' ', @quoted_sw));
} else {
    # Normal, non gdb
    run(ulimit_stack_unlimited() . verilator_bin() . " " . join(' ', @quoted_sw));
}

#----------------------------------------------------------------------

sub usage {
    pod2usage(-verbose => 2, -exitval => 0, -output => \*STDOUT);
}

sub debug {
    shift;
    my $level = shift;
    $Debug = $level || 3;
}

#######################################################################
#######################################################################
# Builds

sub verilator_bin {
    my $basename = ($ENV{VERILATOR_BIN}
                    || ($Debug ? "verilator_bin_dbg" : "verilator_bin"));
    if (-x "$RealBin/$basename" || -x "$RealBin/$basename.exe") {
        return "$RealBin/$basename";
    } else {
        return $basename;  # Find in PATH
    }
}

#######################################################################
#######################################################################
# Utilities

sub gdb_works {
    $! = undef;  # Cleanup -x
    system("gdb /bin/echo"
           . " --batch-silent --quiet --return-child-result"
           . " -ex 'run -n'"  # `echo -n`
           . " -ex 'set width 0'"
           . " -ex 'bt'"
           . " -ex 'quit'");
    my $status = $?;
    return $status == 0;
}

sub aslr_off {
    my $ok = `setarch --addr-no-randomize echo ok 2>/dev/null` || "";
    if ($ok =~ /ok/) {
        return "setarch --addr-no-randomize ";
    } else {
        return "";
    }
}

sub ulimit_stack_unlimited {
    return "" if !$opt_unlimited_stack;
    system("ulimit -s unlimited 2>/dev/null");
    my $status = $?;
    if ($status == 0) {
        return "ulimit -s unlimited 2>/dev/null; exec ";
    } else {
        return "";
    }
}

sub run {
    # Run command, check errors
    my $command = shift;
    $! = undef;  # Cleanup -x
    print "\t$command\n" if $Debug >= 3;
    system($command);
    my $status = $?;
    if ($status) {
        if ($! =~ /no such file or directory/i) {
            warn "%Error: verilator: Misinstalled, or VERILATOR_ROOT might need to be in environment\n";
        }
        if ($Debug) {  # For easy rerunning
            warn "%Error: export VERILATOR_ROOT=" . ($ENV{VERILATOR_ROOT} || "") . "\n";
            warn "%Error: $command\n";
        }
        if ($status & 127) {
            if (($status & 127) == 4  # SIGILL
                || ($status & 127) == 8  # SIGFPA
                || ($status & 127) == 11) {  # SIGSEGV
                warn "%Error: Verilator internal fault, sorry. "
                    . "Suggest trying --debug --gdbbt\n" if !$Debug;
            } elsif (($status & 127) == 6) {  # SIGABRT
                warn "%Error: Verilator aborted. "
                    . "Suggest trying --debug --gdbbt\n" if !$Debug;
            } else {
                warn "%Error: Verilator threw signal $status. "
                    . "Suggest trying --debug --gdbbt\n" if !$Debug;
            }
        }
        if (!$opt_quiet_exit && ($status != 256 || $Debug)) {  # i.e. not normal exit(1)
            warn "%Error: Command Failed $command\n";
        }
        exit $! if $!;  # errno
        exit $? >> 8 if $? >> 8;  # child exit status
        exit 255;  # last resort
    }
}

sub sh_escape {
    my ($arg) = @_;

    # This is similar to quotemeta() but less aggressive.
    # There's no need to escape hyphens, periods, or forward slashes
    # for the shell as these have no special meaning to the shell.
    $arg =~ s/([^0-9a-zA-Z_\-\+\=\.\/])/\\$1/g;
    return $arg;
}

#######################################################################
#######################################################################
package main;
__END__

=pod

=head1 NAME

Verilator - Translate and simulate SystemVerilog code using C++/SystemC


=head1 SYNOPSIS

    verilator --help
    verilator --version
    verilator --binary -j 0 [options] [source_files.v]... [opt_c_files.cpp/c/cc/a/o/so]
    verilator --cc [options] [source_files.v]... [opt_c_files.cpp/c/cc/a/o/so]
    verilator --sc [options] [source_files.v]... [opt_c_files.cpp/c/cc/a/o/so]
    verilator --lint-only -Wall [source_files.v]...


=head1 DESCRIPTION

The "Verilator" package converts all synthesizable, and many behavioral,
Verilog and SystemVerilog designs into a C++ or SystemC model that after
compiling can be executed.  Verilator is not a traditional simulator, but a
compiler.

For documentation see L<https://verilator.org/verilator_doc.html>.


=head1 ARGUMENT SUMMARY

This is a short summary of the arguments to the "verilator" executable.
See L<https://verilator.org/guide/latest/exe_verilator.html> for the
detailed descriptions of these arguments.

=for VL_SPHINX_EXTRACT "_build/gen/args_verilator.rst"

    <file.v>                    Verilog package, module, and top module filenames
    <file.c/cc/cpp>             Optional C++ files to compile in
    <file.a/o/so>               Optional C++ files to link in

     +1364-1995ext+<ext>        Use Verilog 1995 with file extension <ext>
     +1364-2001ext+<ext>        Use Verilog 2001 with file extension <ext>
     +1364-2005ext+<ext>        Use Verilog 2005 with file extension <ext>
     +1800-2005ext+<ext>        Use SystemVerilog 2005 with file extension <ext>
     +1800-2009ext+<ext>        Use SystemVerilog 2009 with file extension <ext>
     +1800-2012ext+<ext>        Use SystemVerilog 2012 with file extension <ext>
     +1800-2017ext+<ext>        Use SystemVerilog 2017 with file extension <ext>
     +1800-2023ext+<ext>        Use SystemVerilog 2023 with file extension <ext>
    --assert                    Enable all assertions
    --assert-case               Enable unique/unique0/priority case related checks
    --autoflush                 Flush streams after all $displays
    --bbox-sys                  Blackbox unknown $system calls
    --bbox-unsup                Blackbox unsupported language features
    --binary                    Build model binary
    --build                     Build model executable/library after Verilation
    --build-dep-bin <filename>  Override build dependency Verilator binary
    --build-jobs <jobs>         Parallelism for --build
    --cc                        Create C++ output
     -CFLAGS <flags>            C++ compiler arguments for makefile
    --clk <signal-name>         Mark specified signal as clock
    --no-clk <signal-name>      Prevent marking specified signal as clock
    --compiler <compiler-name>  Tune for specified C++ compiler
    --compiler-include          Include additional header in the precompiled one
    --converge-limit <loops>    Tune convergence settle time
    --coverage                  Enable all coverage
    --coverage-line             Enable line coverage
    --coverage-max-width <width>   Maximum array depth for coverage
    --coverage-toggle           Enable toggle coverage
    --coverage-underscore       Enable coverage of _signals
    --coverage-user             Enable SVL user coverage
     -D<var>[=<value>]          Set preprocessor define
    --debug                     Enable debugging
    --debug-check               Enable debugging assertions
    --no-debug-leak             Disable leaking memory in --debug mode
    --debugi <level>            Enable debugging at a specified level
    --debugi-<srcfile> <level>  Enable debugging a source file at a level
    --decorations <level>       Set output comment and spacing level
    --no-decoration             Disable comments and lower spacing level
    --default-language <lang>   Default language to parse
     +define+<var>=<value>      Set preprocessor define
    --dpi-hdr-only              Only produce the DPI header file
    --dump-defines              Show preprocessor defines with -E
    --dump-dfg                  Enable dumping DfgGraphs to .dot files
    --dump-graph                Enable dumping V3Graphs to .dot files
    --dump-tree                 Enable dumping Ast .tree files
    --dump-tree-addrids         Use short identifiers instead of addresses
    --dump-tree-dot             Enable dumping Ast .tree.dot debug files
    --dump-tree-json            Enable dumping Ast .tree.json files and .tree.meta.json file
    --dump-<srcfile>            Enable dumping everything in source file
    --dumpi-dfg <level>         Enable dumping DfgGraphs to .dot files at level
    --dumpi-graph <level>       Enable dumping V3Graphs to .dot files at level
    --dumpi-tree <level>        Enable dumping Ast .tree files at level
    --dumpi-tree-json <level>   Enable dumping Ast .tree.json files at level
    --dumpi-<srcfile> <level>   Enable dumping everything in source file at level
     -E                         Preprocess, but do not compile
    --emit-accessors            Emit getter and setter methods for model top class
    --error-limit <value>       Abort after this number of errors
    --exe                       Link to create executable
    --expand-limit <value>      Set expand optimization limit
     -F <file>                  Parse arguments from a file, relatively
     -f <file>                  Parse arguments from a file
     -FI <file>                 Force include of a file
    --flatten                   Force inlining of all modules, tasks and functions
    --future0 <option>          Ignore an option for compatibility
    --future1 <option>          Ignore an option with argument for compatibility
     -fno-<optimization>        Disable internal optimization stage
     -G<name>=<value>           Overwrite top-level parameter
    --gate-stmts <value>        Tune gate optimizer depth
    --gdb                       Run Verilator under GDB interactively
    --gdbbt                     Run Verilator under GDB for backtrace
    --generate-key              Create random key for --protect-key
    --getenv <var>              Get environment variable with defaults
    --get-supported <feature>   Get if feature is supported
    --help                      Show this help
    --hierarchical              Enable hierarchical Verilation
    --hierarchical-params-file <name> Internal option that specifies parameters file for hier blocks
     -I<dir>                    Directory to search for includes
    --if-depth <value>          Tune IFDEPTH warning
     +incdir+<dir>              Directory to search for includes
    --inline-mult <value>       Tune module inlining
    --instr-count-dpi <value>   Assumed dynamic instruction count of DPI imports
     -j <jobs>                  Parallelism for --build-jobs/--verilate-jobs
    --l2-name <value>           Verilog scope name of the top module
    --language <lang>           Default language standard to parse
     -LDFLAGS <flags>           Linker pre-object arguments for makefile
    --lib-create <name>         Create a DPI library
     +libext+<ext>+[ext]...     Extensions for finding modules
    --lint-only                 Lint, but do not make output
    --localize-max-size <value>  Tune localize optimization variable size
    --make <build-tool>         Generate scripts for specified build tool
     -MAKEFLAGS <flags>         Arguments to pass to make during --build
    --main                      Generate C++ main() file
    --main-top-name             Specify top name passed to Verilated model in generated C++ main
    --max-num-width <value>     Maximum number width (default: 64K)
    --Mdir <directory>          Name of output object directory
    --MMD                       Create .d dependency files
    --mod-prefix <topname>      Name to prepend to lower classes
    --MP                        Create phony dependency targets
     +notimingchecks            Ignored
     -O0                        Disable optimizations
     -O3                        High-performance optimizations
     -O<optimization-letter>    Selectable optimizations
     -o <executable>            Name of final executable
    --output-split <statements>          Split .cpp files into pieces
    --output-split-cfuncs <statements>   Split model functions
    --output-split-ctrace <statements>   Split tracing functions
     -P                         Disable line numbers and blanks with -E
    --pins-bv <bits>            Specify types for top-level ports
    --pins-inout-enables        Specify that __en and __out signals be created for inouts
    --pins-sc-biguint           Specify types for top-level ports
    --pins-sc-uint              Specify types for top-level ports
    --pins-sc-uint-bool         Specify types for top-level ports
    --pins-uint8                Specify types for top-level ports
    --no-pins64                 Don't use uint64_t's for 33-64 bit sigs
    --pipe-filter <command>     Filter all input through a script
    --pp-comments               Show preprocessor comments with -E
    --prefix <topname>          Name of top-level class
    --private                   Debugging; see docs
    --prof-c                    Compile C++ code with profiling
    --prof-cfuncs               Name functions for profiling
    --prof-exec                 Enable generating execution profile for gantt chart
    --prof-pgo                  Enable generating profiling data for PGO
    --protect-ids               Hash identifier names for obscurity
    --protect-key <key>         Key for symbol protection
    --protect-lib <name>        Create a DPI protected library
    --public                    Mark signals as public; see docs
    --public-depth <level>      Mark public to specified module depth
    --public-params             Mark all parameters as public_flat
    --public-flat-rw            Mark all variables, etc as public_flat_rw
     -pvalue+<name>=<value>     Overwrite toplevel parameter
    --quiet                     Minimize additional printing
    --quiet-exit                Don't print the command on failure
    --quiet-stats               Don't print statistics
    --relative-includes         Resolve includes relative to current file
    --reloop-limit              Minimum iterations for forming loops
    --report-unoptflat          Extra diagnostics for UNOPTFLAT
    --rr                        Run Verilator and record with rr
    --runtime-debug             Enable model runtime debugging
    --savable                   Enable model save-restore
    --sc                        Create SystemC output
    --no-skip-identical         Disable skipping identical output
    --stats                     Create statistics file
    --stats-vars                Provide statistics on variables
    --no-std                    Prevent parsing standard library
    --no-stop-fail              Do not call $stop when assertion fails
    --structs-packed            Convert all unpacked structures to packed structures
     -sv                        Enable SystemVerilog parsing
     +systemverilogext+<ext>    Synonym for +1800-2023ext+<ext>
    --threads <threads>         Enable multithreading
    --threads-dpi <mode>        Enable multithreaded DPI
    --threads-max-mtasks <mtasks>  Tune maximum mtask partitioning
    --timing                    Enable timing support
    --no-timing                 Disable timing support
    --timescale <timescale>     Sets default timescale
    --timescale-override <timescale>  Overrides all timescales
    --top <topname>             Alias of --top-module
    --top-module <topname>      Name of top-level input module
    --trace                     Enable waveform creation
    --trace-coverage            Enable tracing of coverage
    --trace-depth <levels>      Depth of tracing
    --trace-fst                 Enable FST waveform creation
    --trace-max-array <depth>   Maximum array depth for tracing
    --trace-max-width <width>   Maximum bit width for tracing
    --trace-params              Enable tracing of parameters
    --trace-structs             Enable tracing structure names
    --trace-threads <threads>   Enable FST waveform creation on separate threads
    --no-trace-top              Do not emit traces for signals in the top module generated by verilator
    --trace-underscore          Enable tracing of _signals
     -U<var>                    Undefine preprocessor define
    --no-unlimited-stack        Don't disable stack size limit
    --unroll-count <loops>      Tune maximum loop iterations
    --unroll-stmts <stmts>      Tune maximum loop body size
    --unused-regexp <regexp>    Tune UNUSED lint signals
     -V                         Verbose version and config
     -v <filename>              Verilog library
    --valgrind                  Run Verilator under valgrind
    --verilate-jobs             Job threads for Verilation stage
    --no-verilate               Skip Verilation and just compile previously Verilated code
     +verilog1995ext+<ext>      Synonym for +1364-1995ext+<ext>
     +verilog2001ext+<ext>      Synonym for +1364-2001ext+<ext>
    --version                   Show program version and exits
    --vpi                       Enable VPI compiles
    --waiver-output <filename>  Create a waiver file based on the linter warnings
     -Wall                      Enable all style warnings
     -Werror-<message>          Convert warnings to errors
     -Wfuture-<message>         Disable unknown message warnings
     -Wno-<message>             Disable warning
     -Wno-context               Disable source context on warnings
     -Wno-fatal                 Disable fatal exit on warnings
     -Wno-lint                  Disable all lint warnings
     -Wno-style                 Disable all style warnings
     -Wpedantic                 Warn on compliance-test issues
     -Wwarn-<message>           Enable specified warning message
     -Wwarn-lint                Enable lint warning message
     -Wwarn-style               Enable style warning message
    --x-assign <mode>           Assign non-initial Xs to this value
    --x-initial <mode>          Assign initial Xs to this value
    --x-initial-edge            Enable initial X->0 and X->1 edge triggers
    --no-json-edit-nums         Don't dump editNum in .tree.json files
    --no-json-ids               Don't use short identifiers instead of adresses/paths in .tree.json
    --json-only                 Create JSON parser output (.tree.json and .meta.json)
    --json-only-output          .tree.json output filename
    --json-only-meta-output     .tree.meta.json output filename
    --xml-only                  Create XML parser output
    --xml-output                XML output filename
     -y <dir>                   Directory to search for modules

This is a short summary of the simulation runtime arguments, i.e. for the
final Verilated simulation runtime models.  See
L<https://verilator.org/guide/latest/exe_verilator.html> for the detailed
description of these arguments.

=for VL_SPHINX_EXTRACT "_build/gen/args_verilated.rst"

     +verilator+debug                      Enable debugging
     +verilator+debugi+<value>             Enable debugging at a level
     +verilator+coverage+file+<filename>   Set coverage output filename
     +verilator+error+limit+<value>        Set error limit
     +verilator+help                       Show help
     +verilator+noassert                   Disable assert checking
     +verilator+prof+exec+file+<filename>  Set execution profile filename
     +verilator+prof+exec+start+<value>    Set execution profile starting point
     +verilator+prof+exec+window+<value>   Set execution profile duration
     +verilator+prof+vlt+file+<filename>   Set PGO profile filename
     +verilator+quiet                      Minimize additional printing
     +verilator+rand+reset+<value>         Set random reset technique
     +verilator+seed+<value>               Set random seed
     +verilator+V                          Show verbose version and config
     +verilator+version                    Show version and exit


=head1 DISTRIBUTION

The latest version is available from L<https://verilator.org>.

Copyright 2003-2024 by Wilson Snyder. This program is free software; you can
redistribute it and/or modify the Verilator internals under the terms of
either the GNU Lesser General Public License Version 3 or the Perl Artistic
License Version 2.0.

All Verilog and C++/SystemC code quoted within this documentation file are
released as Creative Commons Public Domain (CC0).  Many example files and
test files are likewise released under CC0 into effectively the Public
Domain as described in the files themselves.


=head1 SEE ALSO

L<verilator_coverage>, L<verilator_gantt>, L<verilator_profcfunc>, L<make>,

L<verilator --help> which is the source for this document,

and L<https://verilator.org/verilator_doc.html> for detailed documentation.

=cut

######################################################################
# Local Variables:
# fill-column: 75
# End:
