#!/usr/bin/env perl
######################################################################
#
# Copyright 2003-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
#
######################################################################

require 5.006_001;
use warnings;
use FindBin qw($RealBin $RealScript);
use strict;

my $relpath = RELPATH;  # Substituted during Verilator 'make install'

unshift @ARGV, $RealScript;

exec { "$RealBin/$relpath/$RealScript" } @ARGV;
die "%Error: Exec failed,";
