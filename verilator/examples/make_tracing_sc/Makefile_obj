# -*- Makefile -*-
#######################################################################
#
# DESCRIPTION: Verilator Example: Makefile for inside object directory
#
# This is executed in the object directory, and called by ../Makefile
#
# This file ONLY is placed under the Creative Commons Public Domain, for
# any use, without warranty, 2020 by <PERSON>.
# SPDX-License-Identifier: CC0-1.0
#
#######################################################################

default: Vtop

# Include the rules made by Verilator
include Vtop.mk

# Use OBJCACHE (ccache) if using gmake and its installed
COMPILE.cc = $(OBJCACHE) $(CXX) $(CXXFLAGS) $(CPPFLAGS) $(TARGET_ARCH) -c

#######################################################################
# Compile flags

# Override some default compile flags
CPPFLAGS += -MMD -MP
CPPFLAGS += -DVL_DEBUG=1
# SystemC isn't too clean
CPPFLAGS += -Wno-deprecated
# Turn on some more flags (when configured appropriately)
# For testing inside Verilator, "configure --enable-ccwarn" will do this
# automatically; otherwise you may want this unconditionally enabled
ifeq ($(CFG_WITH_CCWARN),yes)  # Local... Else don't burden users
USER_CPPFLAGS_WALL += -W -Werror -Wall
endif

# If you build your own rules from scratch, note you need to include
# SystemC as follows (Vtop.mk file includes verilated.mk with these
# already).
# CPPFLAGS += $(SYSTEMC_CXX_FLAGS) $(addprefix -I, $(SYSTEMC_INCLUDE))
# LDFLAGS  += $(SYSTEMC_CXX_FLAGS) $(addprefix -L, $(SYSTEMC_LIBDIR))

# See the benchmarking section of bin/verilator.
# Support class optimizations.  This includes the tracing and symbol table.
# SystemC takes minutes to optimize, thus it is off by default.
OPT_SLOW =
# Fast path optimizations.  Most time is spent in these classes.
OPT_FAST = -Os -fstrict-aliasing
#OPT_FAST = -O
#OPT_FAST =

#######################################################################
# Linking final exe -- presumes have a sim_main.cpp

# Special compile rule
sim_main.o: sim_main.cpp $(VM_PREFIX).h

######################################################################
######################################################################
# Automatically understand dependencies

DEPS := $(wildcard *.d)
ifneq ($(DEPS),)
include $(DEPS)
endif
