.. for github, vim: syntax=reStructuredText

===============================
Revision History and Change Log
===============================

The changes in each Verilator version are described below.  The
contributors that suggested a given feature are shown in []. Thanks!


Verilator 5.028 2024-08-21
==========================

**Minor:**

* Support state-dependent constraints (#5217). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support cross-module clocking variable access (#5184). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support inline constraints for class randomization methods (#5234). [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
* Support clocking blocks in virtual interfaces (#5235). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support `$assertcontrol` assertion_type (#5236). [Bartł<PERSON><PERSON>l, Antmicro Ltd.]
* Support conditional constraints (#5245). [<PERSON><PERSON><PERSON><PERSON>, Antmicro Ltd.]
* Support`--compiler-include` headers in user-supplied cpp files  (#5271). [<PERSON><PERSON><PERSON><PERSON>, Antmicro Ltd.]
* Support `rand_mode` (#5273). [Krzysztof Bieganski, Antmicro Ltd.]
* Support `this.randomize with` (#5282). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support foreach constraints (#5302). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support `parameter type` in hierarchical blocks (#5309) (#5333). [Bartłomiej Chmiel, Antmicro Ltd.]
* Support assertcontrol directive type  (#5310). [Bartłomiej Chmiel, Antmicro Ltd.]
* Support inline random variable control (#5317). [Krzysztof Bieganski, Antmicro Ltd.]
* Support streaming operator on arrays and wide data (#5326). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support streams to/from arrays of wide data (#5334). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support `constraint_mode` (#5338). [Krzysztof Bieganski, Antmicro Ltd.]
* Support constraining AstSel (#5344). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support default value on module input (#5358) (#5373). [Drew Ranck]
* Add `--compiler-include` for additional C++ includes (#5139) (#5202). [Bartłomiej Chmiel, Antmicro Ltd.]
* Add `--emit-accessors` (#5182) (#5227). [Ryan Ziegler]
* Add suggestions on misspelled PLI functions.
* Add warning on dist in constraints (#5264). [Arkadiusz Kozdra, Antmicro Ltd.]
* Add more `rand_mode` unsupported errors (#5329). [Krzysztof Bieganski, Antmicro Ltd.]
* Add parsing but otherwise ignore std::randomize (#5354). [Arkadiusz Kozdra, Antmicro Ltd.]
* Add Verilated cc define when `--timing` used (#5383). [Kaleb Barrett]
* Improve emitted code to use a reference for VlSelf (#5254). [Yangyu Chen]
* Fix monitor block sensitivity items (#4400) (#5294). [Udaya Raj Subedi]
* Fix fusing macro arguments to not ignore whitespace (#5061). [Tudor Timi]
* Fix optimized-out sensitivity trees with `--timing` (#5080) (#5349). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix classes/modules of case-similar names (#5109). [Arkadiusz Kozdra]
* Fix mis-removing $value$plusargs calls (#5127) (#5137). [Seth Pellegrino]
* Fix incorrect result of width mismatch (#5186) (#5189). [Yutetsu TAKATSUKASA]
* Fix compiler coroutine check (#5190) (#5300). [Ricardo Barbedo]
* Fix shortened module names when searching for files (#5196) (#5246). [Tim Hutt]
* Fix `--x-assign` to be independent from `+verilator+rand+reset` (#5214). [Andrew Nolte]
* Fix splitting if statements with impure conditions (#5219). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix unknown conversion on queues (#5220). [Alex Solomatnikov]
* Fix top-level unpacked structure resets (#5221).
* Fix concurrency for mailbox and semaphores (#5222). [Liam Braun]
* Fix forks capturing non-input ports in tasks (#5237) (#5343). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix toggle coverage aggregation on same line (#5248). [Krzysztof Obłonczek]
* Fix error on empty generate with -O0 (#5250).
* Fix unconstrained randomization of unpacked structs (#5252). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix inlining of variables driven from forced vars (#5259). [Geza Lore]
* Fix tracing with `--main-top-name -` (#5261). [Ethan Sifferman]
* Fix randomization when used with inheritance (#5268). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix inline constraints creating class random generator (#5280). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix WIDTHEXPAND on left shift of intuitive amount (#5284). [Greg Taylor]
* Fix elaborating foreach loops (#5285). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix initializing static array in dynamic arrays and queues (#5287). [Baruch Sterin]
* Fix static variable initializers in procedures (#5296). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix randomizing current object with `rand` class instance member (#5292). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix handling of rand fields not referenced in constraints (#5305). [Ryszard Rozak, Antmicro Ltd.]
* Fix Python3 path discovery in make flows to avoid mixing system and user python interpreters (#5307) [Markus Krause]
* Fix make flows to pass PYTHON3 (like PERL) (#5307) (#5308). [Markus Krause]
* Fix assert on wide expression (#5319) (#5324). [Varun Koyyalagunta]
* Fix output clock variable overwriting signal (#5320)  (#5347). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix stringify in nested preprocessor macros (#5323). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix $sformat with array arguments (#5330). [Abe Jordan]
* Fix -Wunused-but-set-variable clang warning (#5331). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix purity of functions with AstJumpBlock or AstStmtExpr (#5332). [Ryszard Rozak, Antmicro Ltd.]
* Fix compilation error on unreachable disable fork / wait fork (#5339). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix missing type coercion in 'inside {array}' (#5340). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix +: and -: unpacked array slicing when array has nonzero low index (#5345) (#5387). [James Bailey]
* Fix tracing_{on,off} in the presence of non-inlined modules (#5346). [Geza Lore]
* Fix NBAs in suspendables (#5348). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix lint_off on Errors (#5351) (#5363). [Ethan Sifferman]
* Fix cache config file resolution performance (#5369). [Geza Lore]
* Fix capturing fields from superclass in `randomize() with` (#5389). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix virtual interface null checks (#5391). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix ==? and !=? with X values.
* Fix CPU time being zero.
* Fix inline function ref port persistence.


Verilator 5.026 2024-06-15
==========================

**Major:**

* Support constrained randomization with external solvers (#4947). [Arkadiusz Kozdra, Antmicro Ltd.]

**Minor:**

* Support `$psprintf` system function (#4314) (#5169). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support 2D dynamic array initialization (#4700) (#5122). [Valentin Atepalikhin]
* Support __en/__out signals on top level inout ports (#4812) (#4856). [Paul Wright]
* Support empty queue as dynarray default value (#5055). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support vpiInertialDelay (#5087). [Todd Strader]
* Support NBAs to arrays inside loops (#5092). [Geza Lore]
* Support parsing and otherwise ignoring inline constraints (#5126). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support `inout` clocking items (#5160). [Arkadiusz Kozdra, Antmicro Ltd.]
* Support StructSel in unpacked array assignments (#5176). [Geza Lore]
* Add error on zero width select (#5028).
* Add CITATION.cff (#5057) (#5058). [Gijs Burghoorn]
* Add VPI eval needed tracking (#5065). [Todd Strader]
* Add `--localize-max-size` option and optimization (#5072).
* Add parameterless assert control system tasks (#5010). [Bartłomiej Chmiel]
* Add traceCapable indication to model header (#5053). [Vito Gamberini]
* Add increasing of stack size when possible (#5071) (#5104). [Yinan Xu]
* Add assertion on reusing VerilatedContext (#5167).
* Add `--pins-sc-uint-bool` to force SystemC uint type (#5192). [Bartłomiej Chmiel, Antmicro Ltd.]
* Improve DFG regularization in cyclic graphs (#5142). [Geza Lore]
* Improve VerilatedVpiPutHolder storage requirements (#5144). [Kaleb Barrett]
* Fix coroutines without awaits to have a co_return (#4208) (#5175). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix missing flex include path variable (#4970) (#4971). [Christopher Taylor]
* Fix missing parameters with comma to be errors (#4979) (#5012). [Paul Swirhun]
* Fix 'experimental/coroutine' file not found on MacOS (#5030) (#5031) (#5151). [Paul Bowen-Huggett]
* Fix bound queue printing (#5032). [Aleksander Kiryk, Antmicro Ltd.]
* Fix consecutive zero-delays (#5038). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix attempted to destroy locked thread pool error (#5040). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix `$system` with string argument (#5042).
* Fix width extension on delays (#5043).
* Fix `$typename` on array.min and others (#5049). [Gökçe Aydos]
* Fix `make $(info)` which cannot be silenced (#5059). [Gökçe Aydos]
* Fix CMake builds to export VERILATOR_ROOT (#5063). [Michael Bikovitsky]
* Fix false ASSIGNIN on functions with explicit port map (#5069).
* Fix 4-state value support for $readmem (#5070) (#5078). [Ethan Sifferman]
* Fix DFG assertion with SystemC (#5076). [Geza Lore]
* Fix `$typename` string to be more standard (#5082) (#5083). [Andrew Nolte]
* Fix missed optimization in V3Delayed (#5089). [Geza Lore]
* Fix macro expansion in strings per 1800-2023 (#5094). [Geza Lore]
* Fix width extension of unpacked array select (#5095). [Varun Koyyalagunta]
* Fix MacOS missing <type_traits> header (#5096) (#5097). [Vito Gamberini]
* Fix assertion failure in V3Gate (#5101). [Yutetsu TAKATSUKASA]
* Fix aliases for forced port signals (#5105). [Geza Lore]
* Fix tracing interface functions (#5108). [Alex Solomatnikov]
* Fix method calls parsing in constraints (#5110). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix vpiInertialDelay for memories (#5113). [Todd Strader]
* Fix hierarchical compilation with nested -F (#5114) (#5124). [Alex Solomatnikov]
* Fix references to ports in forks (#5123). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix output C++ type error on change detect of I/O arrays (#5125) (#5131). [Pawel Jewstafjew]
* Fix x-valued parameters with `--x-assign unique` (#5129). [Ethan Sifferman]
* Fix overflow of string on VPI reads (#5145) (#5146). [Kaleb Barrett]
* Fix VerilatedVpiPutHolder class (#5156). [Kaleb Barrett]
* Fix extending out-of-range select (#5159) (#5164). [Geza Lore]
* Fix radix in width warnings (#5166). [Geza Lore]
* Fix SystemC BITS_PER_DIGIT in VL_ASSIGN_SBW (#5170). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix non-constant replication in concats (#5171). [Arkadiusz Kozdra, Antmicro Ltd.]
* Fix table optimization when applied on real data type (#5172) (#5173). [Arthur Rosa]
* Fix signed types emitted in hierarchical Verilation (#5178). [Bartłomiej Chmiel, Antmicro Ltd.]
* Fix DPI import of null C-string (#5179).
* Fix CMake installation missing verilated.mk (#5187) (#5188). [Philip Axer]
* Fix linking with pthreads on CMake (#5194). [Tim Hutt]
* Fix clang-17 coroutines configuration with -std=gnu++20 (#5200). [Gus Smith]


Verilator 5.024 2024-04-05
==========================

**Major:**

* Add printing summary reports, use `--quiet` or `+verilator+quiet` to suppress (#4909).
* Support 1800-2023 keywords, and parsing with UNDEFINED warnings.
* Support 1800-2023 preprocessor ifdef expressions.

**Minor:**

* Change 1800-2023 to be default language version.
* Add DFG 'regularize' pass, and improve variable removal (#4937). [Geza Lore]
* Add error when pass net to function argument (#4132) (#4966). [Fuad Ismail]
* Add `UNUSEDLOOP` when unused loop is removed (#4926). [Bartłomiej Chmiel, Antmicro Ltd.]
* Add custom version for verilator --version packaging (#4954). [Nolan Poe]
* Add error on missing pure virtual functions (#4961).
* Add error on calling static function without object (#4962).
* Add JSON AST dumps (#5020). [Szymon Gizler]
* Support 1800-2023 DPI headers, svGetTime/svgGetTimeUnit/svGetTimePrecision methods.
* Support 1800-2023 class and function :initial, :extends, :final virtual overrides (#5025).
* Support public packed struct / union (#860) (#4878). [Kefa Chen]
* Support stream operation on unpacked array (#4714) (#5006). [Fuad Ismail]
* Support implicitly-typed variable definitions in for-loop initializers (#4945) (#4986). [Kevin Nygaard]
* Support inside range with implicit type conversion (#5026). [Arkadiusz Kozdra, Antmicro Ltd.]
* Improve installation to be relocatable (#4927). [Geza Lore]
* Improve internal ordering code (#4957) (#4990) (#4994) et al. [Geza Lore]
* Fix generate blocks in vpi_iterate (#3609) (#4913). [Andrew Nolte]
* Fix __Vlip undefined error in --freloop (#4824). [Justin Yao Du]
* Fix missing VPI scopes (#4918). [Andrew Nolte]
* Fix invalid cast on string structure creation (#4921).
* Fix try-lock spuriously fails (#4931) (#4938). [Kamil Rakoczy]
* Fix V3Unknown unpacked struct x-assign (#4934). [Yan Xu]
* Fix DFG removing forceable signals (#4942). [Geza Lore]
* Fix null characters in shortened identifiers (#4946). [Abdul Hameed]
* Fix assignment of null into struct member (#4952).
* Fix VPI missing scopes 2 (#4965). [Andrew Nolte]
* Fix object assignment from conditionals (#4968).
* Fix GCC14 warnings on template specialization syntax (#4974) (#4975). [Nolan Poe]
* Fix unpacked structure upper bit cleaning (#4978).
* Fix tests on MacOS (#4984) (#4985). [Kevin Nygaard]
* Fix `--prof-exec` predicted time values (#4988). [Geza Lore]
* Fix class type as an associative array parameter (#4997).
* Fix inout ports of unpacked struct type (#5000). [Ryszard Rozak, Antmicro Ltd.]
* Fix `unique {}` constraints missing semicolon (#5001).
* Fix preprocessor to respect strings in joins (#5007).
* Fix tracing class parameters (#5014).
* Fix memory leaks (#5016). [Geza Lore]
* Fix $readmem with missing newline (#5019). [Josse Van Delm]
* Fix internal error on missing pattern key (#5023).
* Fix tracing replicated hierarchical models (#5027).


Verilator 5.022 2024-02-24
==========================

**Minor:**

* Add predicted stack overflow warning (#4799).
* Add `+verilator+coverage+file` runtime option.
* Add `--assert-case` option (#4919). [Yutetsu TAKATSUKASA]
* Add `--decorations node` for inserting debug comments into emitted code.
* Add `--json-only` and related JSON dumping (#4715) (#4831). [Szymon Gizler, Antmicro Ltd.]
* Add `--[no]-stop-fail` option for continuing after assertions (#4904). [Yutetsu TAKATSUKASA]
* Add `--runtime-debug` for Verilated executable runtime debugging.
* Add `--valgrind` switch (#4828). [Szymon Gizler]
* Add `unroll_disable` and `unroll_full` loop control metacomments (#3260). [Jiaxun Yang]
* Remove deprecated 32-bit pointer mode (`gcc -m32`).
* Deprecate --xml-only and XML dumping (#4715) (#4831).
* Change zero replication width error to ZEROREPL warning (#4753) (#4762). [Pengcheng Xu]
* Improve message for priority case assertion failure (#4905). [Yutetsu TAKATSUKASA]
* Support dumping coverage with `--main`.
* Support dumping DFG patterns with `--stats` (#4889). [Geza Lore]
* Support `vpiConstType` in `vpi_get_str()` (#4797). [Marlon James]
* Support SystemC 3.0.0 public review version (#4805) (#4807). [Anthony Donlon]
* Support parsing anonymous primitive instantiations (#4809). [Anthony Donlon]
* Fix to not emit already waived warnings in waiver output (#4574) (#4818). [Jonathan Schröter]
* Fix `this` in member initialization (#4710). [eliasphanna]
* Fix localparam elaboration (#3858) (#4794). [Andrew Nolte]
* Fix lint_off disables on preprocessor warnings (#4703). [Srinivasan Venkataramanan]
* Fix $time not rounding up (#4790) (#4792). [Paul Wright]
* Fix `vpi_get()` and `vpi_get64()` to return vpiUndefined on errors (#4795). [Marlon James]
* Fix VPI parameter iteration (#4798). [Marlon James]
* Fix delays using wrong timeunit when modules inlined (#4806). [Paul Wright]
* Fix warnings in verilated_sc_trace.h for Clang. (#4807) (#4827). [Anthony Donlon]
* Fix null pointer dereference (#4810) (#4825). [Adrian Sampson]
* Fix compilation error on multi-inherited interface class usage (#4819).
* Fix maybe-uninitialized compiler warning (#4820) (#4822). [Larry Doolittle]
* Fix mis-splitting of dump control functions (#4821). [Fan Shupei]
* Fix wrong utimes() parameter (#4829). [Szymon Gizler]
* Fix incorrect bit-op-tree NOT optimization (#4832) (#4847). [Yutetsu TAKATSUKASA]
* Fix width calculation in replaceShiftOp (#4837) (#4841) (#4849). [Yutetsu TAKATSUKASA]
* Fix unsafe write in wide array insertion (#4850) (#4855). [Paul Swirhun]
* Fix NOT when checking EQ/NEQ under AND/OR tree (#4857) (#4863). [Yutetsu TAKATSUKASA]
* Fix tracing chandles (#4860). [Nathan Graybeal]
* Fix $fwrite of null (#4862). [Jose Tejada]
* Fix -fno-const-bit-op-tree wrong runtime result (#4864) (#4867). [Yutetsu TAKATSUKASA]
* Fix SystemC biguint sign desynchronization (#4870). [Bartłomiej Chmiel]
* Fix incorrect temporary insertion in loop conditions with statements (#4873). [Geza Lore]
* Fix timing with expr on assign LHS (#4880). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix assertion for unique case (#4892). [Yutetsu TAKATSUKASA]
* Fix GCC tautological-compare warnings.
* Fix compile error on structs with queues (and ignore toggle coverage on queues).
* Fix toggle coverage error on multi-edge driven signals.
* Fix whitespace in `pragma protect version` (#4902) (#4914). [Paul Swirhun]
* Fix incorrect code generation for change expression on typedefed unpacked array (#4915). [Geza Lore]
* Fix inconsistent driver resolution with typedefs (#4917). [Geza Lore]


Verilator 5.020 2024-01-01
==========================

**Major:**

* Support compilation with precompiled headers with Make, and GCC or CLang.
* Change include to systemc instead of systemc.h (#4622) (#4623). [Chih-Mao Chen]
  This may require that SystemC programs add 'using namespace sc_core', 'using namespace sc_dt'.

**Minor:**

* Add devcontainer support (#4748). [Stefan Wallentowitz]
* Support `iff` in sensitivity list (#1482) (#4626). [Krzysztof Bieganski, Antmicro Ltd.]
* Support parameterized virtual interfaces (#4047) (#4743). [Ryszard Rozak, Antmicro Ltd.]
* Support --timing triggers for virtual interfaces (#4673). [Krzysztof Bieganski, Antmicro Ltd.]
* Support ccache when compiling Verilator with CMake (#4678). [Anthony Donlon]
* Support passing constraints to --xml-only output (still otherwise unsupported) (#4683). [Shahid Ikram]
* Support node memory usage information in --stats (#4684). [Geza Lore]
* Support vpiConstType in vpi_get() (#4761). [Todd Strader]
* Support vpi_iterate on packages with vpiInstance (#4726). [Todd Strader]
* Support multiple parameters in virtual interfaces (#4745). [Ryszard Rozak, Antmicro Ltd.]
* Support user C/C++ code in final archive, and make a lib{model}.a (#4749) (#4754). [Fan Shupei]
* Support inside operator on unpacked arrays and queues (#4751). [Ryszard Rozak, Antmicro Ltd.]
* Support VPI parameter iteration (#4765). [Todd Strader]
* Support packages in vpi_handle_by_name() (#4768). [Todd Strader]
* Support invoking interface methods on virtual interface variables (#4774) (#4775). [Jordan McConnon]
* Remove deprecated options (#4663). [Geza Lore]
* Remove older compiler support; require C++14 or newer (#4784) (#4786).
* Optimize timing-delayed queue (#4584). [qrqiuren]
* Optimize substitute optimization memory usage (#4687). [Geza Lore]
* Optimize wide primitive operations with -Oz (#4733). [Geza Lore]
* Optimize V3Premit performance etc. (#4736). [Geza Lore]
* Fix VPI TOP level variable iteration (#3919) (#4618). [Marlon James]
* Fix display with no % printing assoc array (#4376). [Alex Solomatnikov]
* Fix scheduling of external force signals (#4577) (#4668). [Geza Lore]
* Fix a memory leak in V3Fork (#4628). [Krzysztof Boroński]
* Fix linking parameterized hierarchical blocks and recursive hierarchical blocks (#4654). [Anthony Donlon]
* Fix identifiers that end with '_' on Windows (#4655). [Anthony Donlon]
* Fix 'for' loop with outside variable reference (#4660). [David Harris]
* Fix tracing FST enums (#4661) (#4756). [Todd Strader]
* Fix interface parameters used in loop generate constructs (#4664) (#4665). [Anthony Donlon]
* Fix C++20 compilation errors (#4670).
* Fix deadlocks in error handler (#4672). [Mariusz Glebocki, Antmicro Ltd.]
* Fix MingW compilation (#4675). [David Ledger]
* Fix trace when using SystemC with certain configurations (#4676). [Anthony Donlon]
* Fix range access to classes depending on parameter resolution (#4681). [Krzysztof Boroński]
* Fix select into constant And/Or/Xor pattern (#4689). [Geza Lore]
* Fix access type of function arguments (#4692) (#4694). [Ryszard Rozak, Antmicro Ltd.]
* Fix dynamic NBAs with automatic vars (#4696). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix # 0 delays for process resumption, etc. (#4697). [Krzysztof Boroński]
* Fix conflicted namespace for coroutines (#4701) (#4707). [Jinyan Xu]
* Fix compilers seeing empty input due to file system races (#4708). [Flavien Solt]
* Fix shift of > 32-bit number (#4719). [Flavien Solt]
* Fix Windows include gates in filesystem Flush implementation. (#4720). [William D. Jones]
* Fix power operator with wide numbers and constants (#4721) (#4763). [Flavien Solt]
* Fix parameter passing to ports (#4723). [Ryszard Rozak, Antmicro Ltd.]
* Fix block names of nested do..while loops (#4728). [Ryszard Rozak, Antmicro Ltd.]
* Fix class name in error on 'new' on virtual class (#4739). [Ryszard Rozak, Antmicro Ltd.]
* Fix typedefs pointing to parameterized classes (#4747). [Ryszard Rozak, Antmicro Ltd.]
* Fix $finish twice to no longer exit (#4757). [Tim Hutt]
* Fix dynamic NBA conditions (#4773). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix `V3Fork` stage to run only if `--timing` is set (#4778). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix max multiply width and add runtime assertions if too small. (#4781)
* Fix select value too wide (#5148) (#5153). [Dercury]


Verilator 5.018 2023-10-30
==========================

**Major:**

* Support compilation with precompiled headers with Make and GCC or CLang.
* Change include of systemc instead of systemc.h (#4622) (#4623). [Chih-Mao Chen]
  This may require that SystemC programs add 'using namespace sc_core', 'using namespace sc_dt'.

**Minor:**

* Add SIDEEFFECT warning on mishandled side effect cases.
* Add trace() API even when Verilated without --trace (#4462). [phelter]
* Add warning on interface instantiation without parens (#4094). [Gökçe Aydos]
* Add sv_vpi_user.h from IEEE 1800-2017 Annex M (#4606). [Marlon James]
* Support 'disable fork' (#4125) (#4569). [Aleksander Kiryk, Antmicro Ltd.]
* Support 'wait fork' (#4586). [Aleksander Kiryk, Antmicro Ltd.]
* Support 'randc' (#4349).
* Support assigning events (#4403). [Krzysztof Boroński]
* Support resizing function call inout arguments (#4467).
* Support NBAs in non-inlined functions/tasks (#4496) (#4572). [Krzysztof Bieganski, Antmicro Ltd.]
* Support converting parameters inside modules to localparams (#4511). [Anthony Donlon]
* Support concatenation of unpacked arrays (#4558). [Yutetsu TAKATSUKASA]
* Support Clang 16 (#4592). [Mariusz Glebocki]
* Support VPI variables of real and string data types (#4594). [Marlon James]
* Support making VL_LOCK_SPINS configurable (#4599). [Geza Lore]
* Change code --stats output (#4597). [Geza Lore]
* Change --prof-exec infrastructure and report (#4602). [Geza Lore]
* Change lint_off to not propagate upwards to files including where the lint_off is.
* Optimize empty expression statements (#4544).
* Optimize trace internals (#4610) (#4612). [Geza Lore]
* Optimize internal performance issues (#4638). [Geza Lore]
* Fix conversion of impure logical expressions to bit expressions (#487 partial) (#4437). [Ryszard Rozak, Antmicro Ltd.]
* Fix enum functions in localparams (#3999). [Andrew Nolte]
* Fix passing arguments by reference (#3385 partial) (#4489). [Ryszard Rozak, Antmicro Ltd.]
* Fix multithreading handling to separate by code units that use/never use it (#4228). [Mariusz Glebocki, Antmicro Ltd.]
* Fix usage of annotation options (#4486) (#4504). [Michal Czyz]
* Fix detecting local vars in nested forks (#4493) (#4506). [Kamil Rakoczy]
* Fix handling input file path separator (#4515) (#4516). [Anthony Donlon]
* Fix mis-support for parameterized UDPs (#4518). [Anthony Donlon]
* Fix constant conversion of $realtobits, $bitstoreal (#4522).  [Andrew Nolte]
* Fix conversion of integers in $display '%e' (#4528). [muzafferkal]
* Fix non-inlined interface tracing (#3984) (#4530). [Todd Strader]
* Fix stream operations with operands of struct type (#4531) (#4532). [Ryszard Rozak, Antmicro Ltd.]
* Fix 'this' in a constructor (#4533). [Ryszard Rozak, Antmicro Ltd.]
* Fix stream shift operator of 32 bits (#4536). [Julien Faucher]
* Fix object destruction after a copy constructor (#4540) (#4541). [Ryszard Rozak, Antmicro Ltd.]
* Fix inlining of real functions miscasting (#4543). [Andrew Nolte]
* Fix broken link error for enum references (#4551). [Anthony Donlon]
* Fix logical expressions with class objects - caching in v3Const (#4552). [Ryszard Rozak, Antmicro Ltd.]
* Fix using functions/tasks following class definition inside module (#4553). [Anthony Donlon]
* Fix large constant buffer overflow (#4556). [Varun Koyyalagunta]
* Fix instance arrays connecting to array of structs (#4557). [raphmaster]
* Fix error message for invalid parameter overrides (#4559). [Anthony Donlon]
* Fix shift to remove operation side effects (#4563).
* Fix compile warning on unused member function variable (#4567).
* Fix method narrowing conversion compiler error (#4568).
* Fix interface comparison (#4570). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix dynamic triggers for named events (#4571). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix dictionaries with keys of class types (#4576). [Ryszard Rozak, Antmicro Ltd.]
* Fix to not remap local assign intervals in forks (#4583). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix display optimization ignoring side effects (#4585).
* Fix PLI/DPI user defined system task/function grammar (#4587) (#4588). [Quentin Corradi]
* Fix fault on empty clocking block (#4593). [Alex Mykyta]
* Fix creating implicit nets for inputs of gate primitives (#4603). [Geza Lore]
* Fix try_put method of unbounded mailbox (#4608). [Ryszard Rozak, Antmicro Ltd.]
* Fix stable name generation in V3Fork (#4615) (#4624). [Krzysztof Boroński]
* Fix virtual methods (#4616). [Ryszard Rozak, Antmicro Ltd.]
* Fix insertion at queue end (#4619). [Krzysztof Boroński]
* Fix rand fields of reference types (#4627). [Ryszard Rozak, Antmicro Ltd.]
* Fix dynamic casts of null values (#4631). [Ryszard Rozak, Antmicro Ltd.]
* Fix signals read via virtual interfaces being misoptimized (#4645). [Krzysztof Bieganski, Antmicro Ltd.]
* Fix handling of static keyword in methods (#4649). [Ryszard Rozak, Antmicro Ltd.]
* Fix preprocessor to show `line 2 on resumed file.


Verilator 5.016 2023-09-16
==========================

**Minor:**

* Add prepareClone and atClone APIs for Verilated models (#3503) (#4444). [Yinan Xu]
* Add check for conflicting options e.g. binary and lint-only (#4409). [Ethan Sifferman]
* Add --no-trace-top to not trace top signals (#4412) (#4422). [Frans Skarman]
* Support recursive function calls (#3267).
* Support assignments of packed values to stream expressions on queues (#4401). [Ryszard Rozak, Antmicro Ltd]
* Support no-parentheses calls to static methods (#4432). [Krzysztof Boroński]
* Support block_item_declaration in forks (#4455). [Krzysztof Boroński]
* Support assignments of stream expressions on queues to packed values (#4458). [Ryszard Rozak, Antmicro Ltd]
* Support function non-constant default arguments (#4470).
* Support 'let'.
* Optimize Verilator executable size by refactoring error reporting routines (#4446). [Anthony Donlon]
* Optimize Verilation runtime pointers and graphs (#4396) (#4397) (#4398). [Krzysztof Bieganski, Antmicro Ltd]
* Optimize preparations towards multithreaded Verilation (#4291) (#4463) (#4476) (#4477) (#4479).  [Kamil Rakoczy, Antmicro Ltd]
* Fix Windows filename format, etc (#3873) (#4421). [Anthony Donlon].
* Fix t_dist_cppstyle Perl performance issue (#4085). [Srinivasan Venkataramanan]
* Fix using type in parameterized classes without #() (#4281) (#4440). [Anthony Donlon]
* Fix false INFINITELOOP on forever..mailbox.get() (#4323). [Srinivasan Venkataramanan]
* Fix data type of condition operation on class objects (#4345) (#4352). [Ryszard Rozak, Antmicro Ltd]
* Fix variables mutated under fork..join_none/join_any blocks into anonymous objects (#4356). [Krzysztof Boroński]
* Fix V3CUse, do not consider implementations (.cpp) at all (#4386). [Krzysztof Boroński]
* Fix ++/-- under statements (#4399). [Aleksander Kiryk, Antmicro Ltd]
* Fix detection of mixed blocking and nonblocking assignment in nested assignments (#4404). [Ryszard Rozak, Antmicro Ltd]
* Fix jumping over object initialization (#4411). [Krzysztof Boroński]
* Fix multiple issues towards short circuit support (#4413) (#4460). [Ryszard Rozak, Antmicro Ltd]
* Fix variable lifetimes in extern methods (#4414). [Krzysztof Boroński]
* Fix multiple function definitions in V3Sched (#4416). [Hennadii Chernyshchyk]
* Fix false UNUSEDPARAM on generate localparam (#4427). [Bill Pringlemeir]
* Fix checking for parameter and port connections in the wrong place (#4428). [Anthony Donlon]
* Fix coroutine handle movement during queue manipulation (#4431). [Aleksander Kiryk, Antmicro Ltd]
* Fix nested assignments on the LHS (#4435). [Ryszard Rozak, Antmicro Ltd]
* Fix false MULTITOP on bound interfaces (#4438). [Alex Solomatnikov]
* Fix internal error on real conversion (#4447). [vdhotre-ventana]
* Fix lifetime unknown error on enum.name (#4448). [jwoutersymatra]
* Fix unstable output of VHashSha256 (#4453). [Anthony Donlon]
* Fix static cast from a stream type (#4469) (#4485). [Ryszard Rozak, Antmicro Ltd]
* Fix error on enum with VARHIDDEN of cell (#4482). [Michail Rontionov]
* Fix lint of case statements with enum and wildcard bits (#4464) (#4487). [Anthony Donlon]
* Fix reference to extended class in parameterized class (#4466).
* Fix recursive display causing segfault (#4480). [Kuoping Hsu]
* Fix the error message when the type of ref argument is wrong (#4490). [Ryszard Rozak, Antmicro Ltd]
* Fix display %x formatting of real.
* Fix mis-warning on #() in classes' own functions.
* Fix IGNOREDRETURN to not warn on void-cast static function calls.
* Fix ZERODLY to not warn on 'wait(0)'.


Verilator 5.014 2023-08-06
==========================

**Minor:**

* Deprecation planned for 32-bit pointer -m32 mode (#4268).
* Deprecate CMake config below version 3.13 (#4389) (#4390). [Vito Gamberini]
* Support some stream operations on queues (#4292). [Ryszard Rozak, Antmicro Ltd]
* Support property declaration with empty parentheses (#4313) (#4317). [Anthony Donlon]
* Support locator methods with "with" on assoc arrays (#4335). [Ryszard Rozak, Antmicro Ltd]
* Support string replication with variable (#4341). [Aleksander Kiryk, Antmicro Ltd]
* Support more types in wait (#4374). [Aleksander Kiryk, Antmicro Ltd]
* Support static method calls as default values of function arguments (#4378). [Ryszard Rozak, Antmicro Ltd]
* Add GENUNNAMED lint warning. [Srinivasan Venkataramanan, Deepa Palaniappan]
* Add MISINDENT lint warning for misleading indentation.
* Fix 'VlForkSync' redeclaration (#4277). [Krzysztof Bieganski, Antmicro Ltd]
* Fix processes that can outlive their parents (#4253). [Krzysztof Boronski, Antmicro Ltd]
* Fix duplicate fork names (#4295). [Ryszard Rozak, Antmicro Ltd]
* Fix splitting coroutines (#4297) (#4307). [Jiamin Zhu]
* Fix error when multiple duplicate DPI exports (#4301).
* Fix class reference assignment checking (#4296). [Ryszard Rozak, Antmicro Ltd]
* Fix handling of ref types in initial values of type parameters (#4304). [Ryszard Rozak, Antmicro Ltd]
* Fix comparison of string parameters (#4308). [Ryszard Rozak, Antmicro Ltd]
* Fix state update for always processes (#4311). [Aleksander Kiryk, Antmicro Ltd]
* Fix multiple edge timing controls in class methods (#4318) (#4320) (#4344). [Krzysztof Bieganski, Antmicro Ltd]
* Fix implicit calls of base class constructors with optional arguments (#4319). [Ryszard Rozak, Antmicro Ltd]
* Fix propagation of process requirement (#4321). [Krzysztof Boroński]
* Fix unhandled overloads in V3InstrCount (#4324). [Krzysztof Boroński]
* Fix selects of static members (#4326). [Ryszard Rozak, Antmicro Ltd]
* Fix references to members of results of static methods (#4327). [Ryszard Rozak, Antmicro Ltd]
* Fix unique..with method on queues of class objects (#4328). [Ryszard Rozak, Antmicro Ltd]
* Fix queue slicing (#4329). [Aleksander Kiryk, Antmicro Ltd]
* Fix wildcard referring types (#4336) (#4342). [Aleksander Kiryk, Antmicro Ltd]
* Fix comparison of class objects (#4346). [Ryszard Rozak, Antmicro Ltd]
* Fix unexpected RefDType on assoc arrays (#4337). [Aleksander Kiryk, Antmicro Ltd]
* Fix cmake astgen for Rocky Linux 8.7 (#4343). [Julian Daube]
* Fix class timescale in class packages (#4348). [Krzysztof Bieganski, Antmicro Ltd]
* Fix string concatenations (#4354). [Ryszard Rozak, Antmicro Ltd]
* Fix unlinked task error from broken context (#4355) (#4402). [Aleksander Kiryk, Antmicro Ltd]
* Fix selects on unpacked structs (#4359). [Ryszard Rozak, Antmicro Ltd]
* Fix select operation on assoc array with wide keys (#4360). [Ryszard Rozak, Antmicro Ltd]
* Fix non-public methods with wide output (#4364). [Ryszard Rozak, Antmicro Ltd]
* Fix handling of super.new calls (#4366). [Ryszard Rozak, Antmicro Ltd]
* Fix assign to input var in methods (#4367). [Aleksander Kiryk, Antmicro Ltd]
* Fix VlProcess not found (#4368). [Aleksander Kiryk, Antmicro Ltd]
* Fix order of evaluation of function calls in statements (#4375). [Ryszard Rozak, Antmicro Ltd]
* Fix config_build.h issues (#4380) (#4381). [Andrew Miloradovsky]


Verilator 5.012 2023-06-13
==========================

**Major:**

* With -j or --build-jobs, multithread Verilator's emit phase of Verilation. [Kamil Rakoczy, Antmicro Ltd]
  Additional Verilator-internal stages will become multithreaded over time.

**Minor:**

* Add --main-top-name option for C main TOP name (#4235) (#4249). [Don Williamson]
* Add creating __inputs.vpp file with --debug (#4177). [Tudor Timi]
* Add NEWERSTD warning when using feature in newer language standard (#4168) (#4172). [Ethan Sifferman]
* Add warning that timing controls in DPI exports are unsupported (#4238). [Krzysztof Bieganski, Antmicro Ltd]
* Support std::process class (#4212). [Aleksander Kiryk, Antmicro Ltd]
* Support inside expressions with strings and doubles (#4138) (#4139). [Krzysztof Boroński]
* Support get_randstate/set_randstate class method functions.
* Support for condition operator on class objects (#4214). [Ryszard Rozak, Antmicro Ltd]
* Support array max (#4275). [Aleksander Kiryk, Antmicro Ltd]
* Optimize VPI callValueCbs (#4155). [Hennadii Chernyshchyk]
* Configure for faster C++ linking using 'mold', if it is installed.
* Fix crash on duplicate imported modules (#3231). [Robert Balas]
* Fix false WIDTHEXPAND on array declarations (#3959). [Jose Tejada]
* Fix marking overridden methods as coroutines (#4120) (#4169). [Krzysztof Bieganski, Antmicro Ltd]
* Fix SystemC signal copy macro use (#4135). [Josep Sans]
* Fix duplicate static names in blocks in functions (#4144) (#4160). [Stefan Wallentowitz]
* Fix initialization order of initial static after function/task (#4159). [Kamil Rakoczy, Antmicro Ltd]
* Fix linking AstRefDType if it has parameterized class ref (#4164) (#4170). [Ryszard Rozak, Antmicro Ltd]
* Fix crash caused by $display() optimization (#4165) (#4166). [Tudor Timi]
* Fix arrays of unpacked structs (#4173). [Risto Pejašinović]
* Fix $fscanf of decimals overflowing variables (#4174). [Ahmed El-Mahmoudy]
* Fix super.new missing data type (#4147). [Tudor Timi]
* Fix missing class forward declarations (#4151). [Krzysztof Boroński]
* Fix hashes of instances of parameterized classes (#4182). [Ryszard Rozak, Antmicro Ltd]
* Fix forced assignments that override non-continuous assignments (#4183) (#4192). [Krzysztof Bieganski, Antmicro Ltd]
* Fix wide structure VL_TOSTRING_W generation (#4188) (#4189). [Aylon Chaim Porat]
* Fix references to members of parameterized base classes (#4196). [Ryszard Rozak, Antmicro Ltd]
* Fix tracing undefined alignment (#4201) (#4288) [John Wehle]
* Fix class specific same methods for AstVarScope, AstVar, and AstScope (#4203) (#4250). [John Wehle]
* Fix dotted references in parameterized classes (#4206). [Ryszard Rozak, Antmicro Ltd]
* Fix bit selections under parameterized classes (#4210). [Ryszard Rozak, Antmicro Ltd]
* Fix duplicate std:: declaration with -I (#4215). [Harald Pretl]
* Fix deep traversal of class inheritance timing (#4216). [Krzysztof Boroński]
* Fix class parameters of enum types (#4219). [Ryszard Rozak, Antmicro Ltd]
* Fix static methods with prototypes (#4220). [Ryszard Rozak, Antmicro Ltd]
* Fix LATCH warning on function local variables (#4221) (#4284) [Julien Margetts]
* Fix VCD scope types (#4227) (#4282). [Àlex Torregrosa]
* Fix incorrect multi-driven lint warning (#4231) (#4248). [Adrien Le Masle]
* Fix missing assignment for wide unpacked structs (#4233). [Jiamin Zhu]
* Fix unpacked struct == and != operators (#4234) (#4240). [Risto Pejašinović]
* Fix AstStructSel clean when data type is structure (#4241) (#4244). [Risto Pejašinović]
* Fix function calls in with statements (#4245). [Ryszard Rozak, Antmicro Ltd]
* Fix operator == for unpacked struct, if elements are VlUnpacked arrays (#4247). [Risto Pejašinović]
* Fix STATIC lifetime for variables created from clocking items (#4262). [Krzysztof Boroński]
* Fix names of foreach blocks (#4264). [Ryszard Rozak, Antmicro Ltd]
* Fix iterated variables in foreach loops to have VAUTOM lifetimes (#4265). [Krzysztof Boroński]
* Fix missing assignment for wide class members (#4267). [Jiamin Zhu]
* Fix the global uses timing flag when forks exist (#4274). [Krzysztof Bieganski, Antmicro Ltd]
* Fix struct redefinition (#4276). [Aleksander Kiryk, Antmicro Ltd]
* Fix detection of wire/reg duplicates.
* Fix false IMPLICITSTATIC on package functions.
* Fix method calls on function return values.


Verilator 5.010 2023-04-30
==========================

**Minor:**

* Add --public-depth to force public to a certain instance depth (#3952). [Andrew Nolte]
* Add --public-params flag (#3990). [Andrew Nolte]
* Add CONSTRAINTIGN warning when constraint ignored.
* Add STATICVAR warning and convert to automatic (#4018) (#4027) (#4030). [Ryszard Rozak, Antmicro Ltd]
* Add error if class types don't match (#4064). [Ryszard Rozak, Antmicro Ltd]
* Support class extends of package::class.
* Support class srandom and class random stability.
* Support class method calls without parenthesis (#3902) (#4082). [Srinivasan Venkataramanan]
* Support method calls without parenthesis (#4034). [Ryszard Rozak, Antmicro Ltd]
* Support parameterized return types of methods (#4122). [Ryszard Rozak, Antmicro Ltd]
* Support parameterized class references in extends statement (#4146). [Ryszard Rozak, Antmicro Ltd]
* Support complicated IEEE 'for' assignments.
* Support $fopen as an expression.
* Support ++/-- on dotted member variables.
* Optimize static trigger evaluation (#4142). [Geza Lore, X-EPIC]
* Optimize more xor trees (#4071). [Yutetsu TAKATSUKASA]
* Change range order warning from LITENDIAN to ASCRANGE (#4010). [Iztok Jeras]
* Change ZERODLY to a warning.
* Fix random internal crashes (#666). [Dag Lem]
* Fix install, standardization in cmake CMakeLists.txt (#3974). [Yu-Sheng Lin]
* Fix UNDRIVEN warning seg fault (#3989). [Felix Neumärker]
* Fix symbol entries when inheriting classes (#3995) (#3996). [Krzysztof Boroński]
* Fix event controls reusing same variable (#4014). Kamil Rakoczy <<EMAIL>>
* Fix push to dynamic queue in struct (#4015). [ezchi]
* Fix names for blocks in do..while loop (#4019). [Ryszard Rozak, Antmicro Ltd]
* Fix randomize on null field (#4023). [Ryszard Rozak, Antmicro Ltd]
* Fix rand fields in base classes (#4025). [Ryszard Rozak, Antmicro Ltd]
* Fix large return blocks with --comp-limit-blocks (#4028). [tenghtt]
* Fix clocking block scope internal error (#4032). [Srinivasan Venkataramanan]
* Fix false LATCH warning on --assert 'unique else if' (#4033) ($4054). [Jesse Taube]
* Fix characters from DEFENV literals for Conda (#4035) (#4044). [Tim Snyder]
* Fix info message prints under --assert (#4036) (#4053). [Srinivasan Venkataramanan]
* Fix C++ compile errors when passing class refs as task argument (#4063). [Krzysztof Bieganski, Antmicro Ltd]
* Fix NBAs inside fork-joins (#4050). [Aleksander Kiryk, Antmicro Ltd]
* Fix task calls as fork statements (#4055). [Krzysztof Bieganski, Antmicro Ltd]
* Fix _Vilp used before declaration (#4057) (#4062). [Josep Sans]
* Fix incorrect optimization of bit op tree (#4059) (#4070). [Yutetsu TAKATSUKASA]
* Fix parameters in a class body to be localparam (#4061). [Ryszard Rozak, Antmicro Ltd]
* Fix interface generate begin (#4065). [Srinivasan Venkataramanan]
* Fix tracing with awaits at end of block (#4075) (#4076). [Krzysztof Bieganski, Antmicro Ltd]
* Fix sense expression variable naming (#4081). [Kamil Rakoczy]
* Fix importing symbols from base class (#4084). [Ryszard Rozak, Antmicro Ltd]
* Fix false error on new const assignment (#4098). [Tudor Timi]
* Fix unpacked structs under classes (#4102). [Tudor Timi]
* Fix variables in class methods to be automatic (#4111) (#4137). [Peter Monsson]
* Fix to use parallel build for projects with a lot of files (#4116). [Krzysztof Boroński]
* Fix including __Syms header in generated C++ files (#4123). [Krzysztof Boroński]
* Fix systemc namespace issues (#4126) (#4127). [Eyck Jentzsch]
* Fix class param extends A=B (#4128). [Ryszard Rozak, Antmicro Ltd]
* Fix missing begin block hierarchy in --xml-only cells section (#4129) (#4133). [Risto Pejašinović]
* Fix resolution of class lvalues after parameterization (#4131). [Krzysztof Boroński]
* Fix DFG error on $countbits (#4101) (#4143). [Paul Donahue]
* Fix duplicating parameter class types (#4115). [Ryszard Rozak, Antmicro Ltd]
* Fix class extend param references (#4136).  [Ryszard Rozak, Antmicro Ltd]
* Fix -CFLAGS to allow overriding optimization levels (#4140). [Peter Monsson]
* Fix DPI function type alias (#4148) (#4149). [Toru Niina]
* Fix deleting unused parameterized classes (#4150). [Ryszard Rozak, Antmicro Ltd]
* Fix false ENUMVALUE on expressions and arrays.
* Fix unnecessary verilated_std.sv waivers in --waiver-output.


Verilator 5.008 2023-03-04
==========================

**Minor:**

* Add --annotate-points option, change multipoint on line reporting (#3876). [Nassim Corteggiani]
* Add --verilate-jobs option (#3889). [Kamil Rakoczy, Antmicro Ltd]
* Add WIDTHEXPAND and WIDTHTRUNC warnings to replace WIDTH (#3900). [Andrew Nolte]
* Add SOURCE_DATE_EPOCH for docs/guide/conf.py (#3918). [Larry Doolittle]
* Add /*verilator public[flat|flat_rd|flat_rw| ]*/ metacomments (#3894). [Joseph Nwabueze]
* Add lint warning on always_comb multidriven (#3888) (#3939). [Adam Bagley]
* Add warning on ++/-- over expressions with potential side effects (#3976). [Krzysztof Boroński]
* Add error on mixing .name and by-port instantiations.
* Removed deprecated --cdc option.
* Support unpacked unions.
* Support interface classes and class implements.
* Support global clocking and $global_clock.
* Support class parameters without initial values.
* Support cast to numbers from strings.
* Support struct I/O in --lib-create (#3378) (#3892). [Varun Koyyalagunta]
* Support function calls without parenthesis (#3903) (#3902). [Ryszard Rozak, Antmicro Ltd]
* Support class extending its parameter (#3904). [Ryszard Rozak, Antmicro Ltd]
* Support static function variables (#3830). [Ryszard Rozak, Antmicro Ltd]
* Support recursive methods (#3987). [Ryszard Rozak, Antmicro Ltd]
* Fix real parameters of infinity and NaN.
* Fix pattern assignment to unpacked structs (#3510). [Mostafa Garnal]
* Fix single-element replication to dynarray/unpacked/queue (#3548). [Gustav Svensk]
* Fix constant enum methods (#3621). [Todd Strader]
* Fix inconsistent naming of generate scope arrays (#3840). [Andrew Nolte]
* Fix namespace fallback resolution (#3863) (#3942). [Aleksander Kiryk, Antmicro Ltd]
* Fix std:: to be parsed first (#3864) (#3928). [Aleksander Kiryk, Antmicro Ltd]
* Fix cmake warning if multiple SOURCES w/o PREFIX (#3916) (#3927). [Yoda Lee]
* Fix parameterized class function linkage (#3917). [Ryszard Rozak]
* Fix static members of type aliases of a parameterized class (#3922). [Ryszard Rozak, Antmicro Ltd]
* Fix class extend parameter dot case (#3926). [Ryszard Rozak, Antmicro Ltd]
* Fix MsWin missing directory exception, and ::std (#3928) (#3933) (#3935). [Kritik Bhimani]
* Fix very long VPI signal names (#3929). [Marlon James]
* Fix VPI upper interface scopes not found (#3937). [David Stanford]
* Fix virus detection false positive (#3944). [Stuart Morris]
* Fix constant string function assignment (#3945). [Todd Strader]
* Fix constant format field widths (#3946). [Todd Strader]
* Fix class field linking when a super classes is a param (#3949). [Ryszard Rozak, Antmicro Ltd]
* Fix CMake bad C identifiers (#3948) (#3951). [Zixi Li]
* Fix build on HP PA architecture (#3954). [John David Anglin]
* Fix date on the front page of verilator.pdf (#3956) (#3957). [Larry Doolittle]
* Fix associative arrays declared with ref type (#3960). [Ryszard Rozak, Antmicro Ltd]
* Fix missing error on negative replicate (#3963). [Benjamin Menküc]
* Fix self references to parameterized classes (#3962). [Ryszard Rozak, Antmicro Ltd]
* Fix LITENDIAN warning is backwards (#3966) (#3967). [Cameron Kirk]
* Fix subsequent parameter declarations (#3969). [Ryszard Rozak, Antmicro Ltd]
* Fix timing delays to not truncate below 64 bits (#3973) (#3982). [Felix Neumärker]
* Fix cmake on MacOS to mark weak symbols with -U linker flag (#3978) (#3979). [Peter Debacker]
* Fix UNDRIVEN warning seg fault (#3989). [Felix Neumärker]
* Fix coverage of class methods (#3998). [Tim Paine]
* Fix packed array structure replication.
* Fix enum.next(0) and enum.prev(0).


Verilator 5.006 2023-01-22
==========================

**Minor:**

* Support clocking blocks (#3674). [Krzysztof Bieganski, Antmicro Ltd]
* Support unpacked structs (#3802). [Aleksander Kiryk, Antmicro Ltd]
* Support Windows-native builds using cmake (#3814). [Kritik Bhimani]
* Support p format for UnpackArray (#3877). [Aleksander Kiryk, Antmicro Ltd]
* Support property calls without parenthesis (#3879) (#3893). [Ryszard Rozak, Antmicro Ltd]
* Support import/export lists in modport (#3886). [Gökçe Aydos]
* Support class queue equality (#3895). [Ilya Barkov]
* Support type case and type equality comparisons.
* Add IMPLICITSTATIC warning when a task/function is implicitly static (#3839). [Ryszard Rozak, Antmicro Ltd]
* Add VL_VALUE_STRING_MAX_WORDS override (#3869). [Andrew Nolte]
* Optimize expansion of extend operators.
* Internal multithreading tests. [Mariusz Glebocki, et al, Antmicro Ltd]
* Fix VPI one-time timed callbacks (#2778). [Marlon James, et al]
* Fix initiation of function variables (#3815). [Dan Gisselquist]
* Fix to zero possibly uninitialized bits in replications (#3815).
* Fix crash in DFT due to width use after free (#3817) (#3820). [Jevin Sweval]
* Fix signed/unsigned comparison compile warning (#3822). [Kamil Rakoczy]
* Fix OS-X weak symbols with -U linker flag (#3823). [Jevin Sweval]
* Fix wrong bit op tree optimization (#3824) (#3825). [Yutetsu TAKATSUKASA]
* Fix self references when param class instantiated (#3833). [Ryszard Rozak, Antmicro Ltd]
* Fix memory leak in V3Sched, etc. (#3834). [Geza Lore]
* Fix compatibility with musl libc / Alpine Linux (#3845). [Sören Tempel]
* Fix empty case items crash (#3851). [Rich Porter]
* Fix VL_CPU_RELAX on MIPS/Armel/s390/sparc (#3843) (#3891). [Kamil Rakoczy]
* Fix module parameter name collision (#3854) (#3855). [James Shi]
* Fix unpacked array expansion (#3861). [Joey Liu]
* Fix signed/unsigned parameter types (#3866). [James Shi]
* Fix chain call of abstract class constructor (#3868) (#3883). [Ilya Barkov]
* Fix to use same std in Verilator and Verilated compile (#3881). [Kamil Rakoczy, Antmicro Ltd]
* Fix foreach unnamedblk duplicate error (#3885). [Ilya Barkov]
* Fix elaboration of member selected classes (#3890). [Ilya Barkov]
* Fix mismatched widths in DFG (#3872). [Geza Lore, Yike Zhou]
* Fix lint for non-integral types in packed structs.
* Fix generate case with empty body statements.


Verilator 5.004 2022-12-14
==========================

**Major:**

* Support named properties (#3667). [Ryszard Rozak, Antmicro Ltd]
* Add ENUMVALUE warning when value misused for enum (#726) (#3777) (#3783).
* Deprecate --no-threads; use --threads 1 for single threaded (#3703). [Kamil Rakoczy, Antmicro Ltd]

**Minor:**

* Support std::semaphore and typed std::mailbox (#3708). [Krzysztof Bieganski, Antmicro Ltd]
* Support 'with' in unique, unique_index, min, max in queues (#3772). [Ryszard Rozak, Antmicro Ltd]
* Support events in VCD/FST traces (#3759). [Yves Mathieu]
* Support foreach loops on strings (#3760). [Ryszard Rozak, Antmicro Ltd]
* Support member selects in with clauses (#3775). [Ryszard Rozak, Antmicro Ltd]
* Support super.new calls (#3789). [Ryszard Rozak, Antmicro Ltd]
* Support randcase.
* Support pre_randomize and post_randomize.
* Support $timeunit and $timeprecision.
* Support assignment expressions.
* Change ENDLABEL from warning into an error.
* Internal AST improvements, also affect XML format (#3721). [Geza Lore]
* Deprecate verilated_fst_sc.cpp and verilated_vcd_sc.cpp.
* Disable stack size limit (#3706) (#3751). [Mariusz Glebocki]
* Add error when use --exe with --lib-create (#3785). [Yinan Xu]
* Fix jump handling in do while loops (#3731). [Ryszard Rozak, Antmicro Ltd]
* Fix 'with' clause handling in functions (#3739). [Ryszard Rozak, Antmicro Ltd]
* Fix CONTEXT compile error on MingW (#3741). [William D. Jones]
* Fix MSVC compiler errors (#3742) (#3746). [Kritik Bhimani]
* Fix CASEINCOMPLETE when covers all enum values (#3745) (#3782). [Guy-Armand Kamendje]
* Fix return type of $countbits functions to int (#3725). [Ryszard Rozak, Antmicro Ltd]
* Fix timing control in while-break loops (#3733) (#3769). [Ryszard Rozak, Antmicro Ltd]
* Fix return in constructors (#3734). [Ryszard Rozak, Antmicro Ltd]
* Fix missing UNUSED warnings with --coverage (#3736). [alejandro-castro-ortegon]
* Fix tracing parameters overridden with -G (#3723). [Iztok Jeras]
* Fix folding of LogAnd with non-bool operands (#3726). [Geza Lore]
* Fix DFG optimization issues (#3740) (#3771). [Geza Lore]
* Fix pre/postincrement operations (#3744) (#3756). [Ryszard Rozak, Antmicro Ltd]
* Fix cross-compile for MingW, Arm and RISC-V (#3752). [Miodrag Milanović]
* Fix $unit as base package for other packages (#3755). [Ryszard Rozak, Antmicro Ltd]
* Fix make jobserver with submakes (#3758). [Gus Smith]
* Fix to escape VERILATOR_ROOT file paths (#3764) (#3765). [Jiacheng Qian]
* Fix empty string literals converting to string types (#3774). [miree]
* Fix to remove $date from .vcd files (#3779). [Larry Doolittle]
* Fix missing user objects in --lib-create mode (#3780) (#3784). [Yinan Xu]
* Fix non-blocking assignments in forks (#3781) (#3800). [Krzysztof Bieganski, Antmicro Ltd]
* Fix forks without any delayed statements (#3792) (#3801). [Krzysztof Bieganski, Antmicro Ltd]
* Fix internal error in bit op tree optimization (#3793). [Yutetsu TAKATSUKASA]
* Fix lint_off EOFNEWLINE in .vlt files (#3796). [Andrew Nolte]
* Fix wait 0.
* Fix comparing ranged slices of unpacked arrays.


Verilator 5.002 2022-10-29
==========================

**Major:**

* This is a major new release.
* Require C++20 for the new --timing features. Upgrading to a C++20 or
  newer compiler is strongly recommended.
* Support the Active and NBA scheduling regions as defined by the
  SystemVerilog standard (IEEE 1800-2017 chapter 4). This means all generated
  clocks are now simulated correctly (#3278, #3384). [Geza Lore, Shunyao CAD]
* Support timing controls (delays, event controls in any location, wait
  statements) and forks. [Krzysztof Bieganski, Antmicro Ltd]
  This may require adding --timing or --no-timing. See docs for details.
* Introduce a new combinational logic optimizer (DFG), that can yield
  significant performance improvements on some designs. [Geza Lore, Shunyao CAD]
* Add --binary option as alias of --main --exe --build --timing (#3625).
  For designs where C++ was only used to make a simple no-I/O testbench, we
  recommend abandoning that C++, and instead letting Verilator build it
  with --binary (or --main).

**Minor:**

* Split UNUSED warning into genvar, param, and signal warnings (#3607). [Topa Topino]
* Support standalone 'this' in classes (#2594) (#3248) (#3675). [Arkadiusz Kozdra, Antmicro Ltd]
* Support tristate select/extend (#3604). [Ryszard Rozak, Antmicro Ltd>
* Support linting for top module interfaces (#3635). [Kanad Kanhere]
* Support virtual interfaces (#3654). [Arkadiusz Kozdra, Antmicro Ltd]
* Support class type params without defaults (#3693). [Krzysztof Bieganski, Antmicro Ltd]
* Support empty generate_regions (#3695). [mpb27]
* Support access to constructs inside type parameters (#3702). [Arkadiusz Kozdra, Antmicro Ltd]
* Add --dump-tree-dot to enable dumping Ast Tree .dot files (#3636). [Marcel Chang]
* Add --get-supported to determine what features are in Verilator.
* Add error on real edge event control.
* Fix false LATCH warning on 'unique if' (#3088). [Rachit Nigam]
* Fix cell assigning integer array parameters (#3299). [Michael Platzer]
* Fix LSB error on --hierarchical submodules (#3539). [danbone]
* Fix $display of fixed-width numbers (#3565). [Iztok Jeras]
* Fix foreach and pre/post increment in functions (#3613). [Nandu Raj]
* Fix linker errors in user-facing timing functions (#3657). [Krzysztof Bieganski, Antmicro Ltd]
* Fix null access on optimized-out fork statements (#3658). [Krzysztof Bieganski, Antmicro Ltd]
* Fix VPI inline module naming mismatch (#3690) (#3694). [Jiuyang Liu]
* Fix deadlock in timeprecision when using SystemC (#3707). [Kamil Rakoczy, Antmicro Ltd]
* Fix width mismatch on inside operator (#3714). [Àlex Torregrosa]


Verilator 4.228 2022-10-01
==========================

**Announcement:**

* The next release is anticipated to premiere Verilator Version 5.  Please
  consider beta-testing the github 'develop-v5' branch, which will soon
  merge into the github 'master' branch (#3383).

**Minor:**

* Support some IEEE signal strengths (#3601) (#3629). [Ryszard Rozak, Antmicro Ltd]
* Add --main to generate main() C++ (previously was experimental only).
* Add --build-jobs, and rework arguments for -j (#3623). [Kamil Rakoczy]
* Rename --bin to --build-dep-bin.
* Rename debug flags --dumpi-tree, --dumpi-graph, etc. [Geza Lore]
* Fix thread safety in SystemC VL_ASSIGN_SBW/WSB (#3494) (#3513). [Mladen Slijepcevic]
* Fix crash in gate optimization of circular logic (#3543). [Bill Flynn]
* Fix arguments in non-static method call (#3547) (#3582). [Gustav Svensk]
* Fix default --mod-prefix when --prefix is repeated (#3603). [Geza Lore]
* Fix calling trace() after open() segfault (#3610) (#3627). [Yu-Sheng Lin]
* Fix typedef'ed class conversion to Boolean (#3616). [Aleksander Kiryk]
* Fix Verilation speed when disabled warnings (#3632). [Kamil Rakoczy, Antmicro Ltd]


Verilator 4.226 2022-08-31
==========================

**Minor:**

* Add --future0 and --future1 options.
* Support class parameters (#2231) (#3541). [Arkadiusz Kozdra, Antmicro Ltd]
* Support wildcard index associative arrays (#3501). [Arkadiusz Kozdra, Antmicro Ltd]
* Support negated properties (#3572). [Aleksander Kiryk]
* Support $test$plusargs(expr) (#3489).
* Rename trace rolloverSize() (#3570).
* Improve Verilation speed with --threads on large designs. [Geza Lore]
* Improve Verilation memory by reducing V3Number (#3521). [Mariusz Glebocki, Antmicro Ltd]
* Fix struct pattern assignment (#2328) (#3517). [Mostafa Gamal]
* Fix public combo propagation issues (#2905). [Todd Strader]
* Fix incorrect tristate logic (#3399) [shareefj, Vighnesh Iyer]
* Fix incorrect bit op tree optimization (#3470). [algrobman]
* Fix bisonpre for MSYS2 (#3471).
* Fix max memory usage (#3483). [Kamil Rakoczy, Antmicro Ltd]
* Fix empty string arguments to display (#3484). [Grulfen]
* Fix table optimizing away display (#3488). [Stefan Post]
* Fix unique_ptr memory header for MinGW64 (#3493).
* Fix $dump system task with --output-split-cfuncs (#3495) (#3497). [Varun Koyyalagunta]
* Fix wrong bit op tree optimization (#3509). [Nathan Graybeal]
* Fix nested default assignment for struct pattern (#3511) (#3524). [Mostafa Gamal]
* Fix sformat string incorrectly cleared (#3515) (#3519). [Gustav Svensk]
* Fix segfault exporting non-existent package (#3535).
* Fix void-cast queue pop_front or pop_back (#3542) (#3364). [Drew Ranck]
* Fix case statement comparing string literal (#3544). [Gustav Svensk]
* Fix === with some tristate constants (#3551). [Ryszard Rozak, Antmicro Ltd]
* Fix converting classes to string (#3552). [Arkadiusz Kozdra, Antmicro Ltd]
* Fix --hierarchical with order-based pin connections (#3583) (#3585). [Kelin9298]


Verilator 4.224 2022-06-19
==========================

**Major:**

* VCD tracing is now parallelized with --threads (#3449). [Geza Lore, Shunyao CAD]

**Minor:**

* Add -f<optimization> options to replace -O<letter> options (#3436).
* Changed --no-merge-const-pool to -fno-merge-const-pool (#3436).
* Changed --no-decoration to remove output whitespace (#3460). [Kamil Rakoczy]
* Support compile time trace signal selection with tracing_on/off (#3323). [Shunyao CAD]
* Support non-ANSI interface port declarations (#3439). [Geza Lore, Shunyao CAD]
* Support concat assignment to packed array (#3446).
* Improve conditional merging optimization (#3125). [Geza Lore, Shunyao CAD]
* Define VM_TRACE_VCD when tracing in VCD format. [Geza Lore, Shunyao CAD]
* Add assert when VerilatedContext is mis-deleted (#3121). [Rupert Swarbrick]
* Internal prep work towards timing control. [Krzysztof Bieganski, Antmicro Ltd]
* Fix hang with large case statement optimization (#3405). [Mike Urbach]
* Fix UNOPTFLAT warning from initial static var (#3406). [Kamil Rakoczy]
* Fix compile error when enable VL_LEAK_CHECKS (#3411). [HungMingWu]
* Fix cmake rules to support higher-level targets (#3377) (#3386). [Martin Stadler]
* Fix BLKANDNBLK on $readmem/$writemem (#3379). [Alex Solomatnikov]
* Fix 'with' operator with type casting (#3387). [xiak95]
* Fix incorrect conditional merging (#3409). [Raynard Qiao]
* Fix passing VL_TRACE_FST_WRITER_THREAD in CMake build. [Geza Lore, Shunyao CAD]
* Fix compile error under strict C++11 mode (#3463). [Kevin Kiningham]
* Fix public unpacked input ports (#3465). [Todd Strader]


Verilator 4.222 2022-05-02
==========================

**Minor:**

* Split --prof-threads into --prof-exec and --prof-pgo (#3365). [Geza Lore, Shunyao CAD]
* Deprecate 'vluint64_t' and similar types (#3255).
* Raise error on assignment to const in initial blocks. [Geza Lore, Shunyao CAD]
* Issue INITIALDLY/COMBDLY/BLKSEQ warnings consistent with Verilator execution. [Geza Lore, Shunyao CAD]
* Support LoongArch ISA multithreading (#3353) (#3354). [Xi Zhang]
* Fix MSVC localtime_s (#3124).
* Fix Bison 3.8.2 error (#3366). [elike-ypq]
* Fix rare bug in -Oz (V3Localize) (#3286). [Geza Lore, Shunyao CAD]
* Fix tracing interfaces inside interfaces (#3309). [Kevin Millis]
* Fix filenames with dots overwriting debug .vpp files (#3373).
* Fix including VK_USER_OBJS in make library (#3370) (#3382). [Julien Margetts]
* Fix hang in generate symbol references (#3391) (#3398). [Yoda Lee]
* Fix missing #include <memory> (#3392). [Aliaksei Chapyzhenka]
* Fix crash in recursive module inlining (#3393). [david-sawatzke]
* Fix --protect-ids mangling names of library methods. [Geza Lore, Shunyao CAD]
* Fix foreach segmentation fault (#3400). [Kamil Rakoczy]


Verilator 4.220 2022-03-12
==========================

**Minor:**

* Removed the deprecated lint_off flag -msg; use -rule instead.
* Removed the deprecated "fl" attribute in XML output; use "loc" attribute instead.
* Suppress WIDTH warning on negate using carry bit (#3295). [Peter Monsson]
* Add trace dumpvars() call for selective runtime tracing (#3322). [Shunyao CAD]
* Add VERILATOR_VERSION_INTEGER for determining API (#3343). [Larry Doolittle]
* Improve various V3Combine algorithm details (#3328). [Yutetsu TAKATSUKASA]
* Improve various V3Order algorithm details. [Geza Lore]
* Fix MacOS arm64 build (#3285) (#3291). [Guokai Chen]
* Fix signed number operation (#3294) (#3308). [Raynard Qiao]
* Fix FST traces to include vector range (#3296) (#3297). [Jamie Iles]
* Fix skipping public enum values with four-state values (#3303).
* Fix $readmem file not found to be warning not error (#3310). [Alexander Grobman]
* Fix class stringification on wide arrays (#3312). [Iru Cai]
* Fix $fscanf etc to return -1 on EOF (#3313). [Jose Tejada]
* Fix public function arguments that are arrayed (#3316). [pawel256]
* Fix unnamedblk error on foreach (#3321). [Aliaksei Chapyzhenka]
* Fix crash in recursive module inlining (#3324). [Larry Doolittle]
* Fix VL_RESTORER behavior on passing a lvalue reference (#3326). [HungMingWu]
* Fix compile error with --trace-fst --sc (#3332). [leavinel]
* Fix cast to array types (#3333). [Todd Strader]
* Fix Vdeeptemp error with --threads and --compiler clang (#3338). [Per Karlsson]


Verilator 4.218 2022-01-17
==========================

**Major:**

* Primary inputs and outputs (VL_INW/VL_OUTW) now use VlWide type.
  In general this should be backward compatible, but may lead to some
  wrapper code needing changes.
* Option --cdc is deprecated and is planned for removal,
  file a bug if this is still being used.

**Minor:**

* Support class static members (#2233).
* Support force/release (#2431) (#2593). [Shunyao CAD]
* Add 'forceable' attribute to allow forcing from C++. (#3272). [Geza Lore, Shunyao CAD]
* Support lower dimension looping in foreach loops (#3172). [Ehab Ibrahim]
* Support up to 64 bit enums for .next/.prev/.name (#3244). [Alexander Grobman]
* Reduce .rodata footprint of trace initialization (#3250). [Geza Lore, Shunyao CAD]
* Support FST tracing in hierarchical Verilation (#3251). [Yutetsu TAKATSUKASA]
* Use C++11 standard types for MacOS portability (#3254) (#3257). [Adrien Le Masle]
* Fix make support for BSD ar (#2999) (#3256). [Julie Schwartz]
* Fix bad ending address on $readmem (#3205). [Julie Schwartz]
* Fix MSWIN compile error (#2681). [Unai Martinez-Corral]
* Fix break under foreach loop (#3230).
* Fix VL_STREAML_FAST_QQI with 64 bit left-hand-side (#3232) (#3235). [Adrien Le Masle]
* Fix $sformat of inputs/outputs (#3236). [Adrien Le Masle]
* Fix associative array first method as statement (#3228). [Adrien Le Masle]
* Fix associative array foreach loop (#3229).
* Fix $fclose not accepting expressions (#3237). [Julie Schwartz]
* Fix $random not updating seed (#3238). [Julie Schwartz]
* Fix top level param overwrite when package has same param (#3241) (#3247). [Adrien Le Masle]
* Fix spurious UNUSED by ignoring inout pin connections (#3242). [Julie Schwartz]
* Fix splitting of _eval and other top level functions. [Geza Lore, Shunyao CAD]
* Fix internal error by inout port (#3258). [Yutetsu TAKATSUKASA]
* Fix GCC 11 compile error (#3273). [HungMingWu]


Verilator 4.216 2021-12-05
==========================

**Major:**

* Add --lib-create, similar to --protect-lib but without protections.
* Support tracing through --hierarchical/--lib-create libraries (#3200).

**Minor:**

* Internal code cleanups and improvements. [Geza Lore]
* Improve --thread Verilation-time performance.
* Support task name in $display %m (#3211). [Julie Schwartz]
* Make 'bit', 'logic' and 'time' types unsigned by default. [Geza Lore]
* Optimize $random concatenates/selects (#3114).
* Fix array method names with parenthesis (#3181) (#3183). [Teng Huang]
* Fix split_var assign merging (#3177) (#3179). [Yutetsu TAKATSUKASA]
* Fix wrong bit op tree optimization (#3185). [Yutetsu TAKATSUKASA]
* Fix some SliceSels not being constants (#3186) (#3218). [Michaël Lefebvre]
* Fix nested generate if genblk naming (#3189). [yanx21]
* Fix hang on recursive definition error (#3199). [Jonathan Kimmitt]
* Fix display of signed without format (#3204). [Julie Schwartz]
* Fix display of empty string constant (#3207) (#3215). [Julie Schwartz]
* Fix incorrect width after and-or optimization (#3208). [Julie Schwartz]
* Fix $fopen etc on integer arrays (#3214). [adrienlemasle]
* Fix $size on dynamic strings (#3216).
* Fix %0 format on $value$plusargs (#3217).
* Fix timescale portability on Arm64 (#3222).


Verilator 4.214 2021-10-17
==========================

**Major:**

* Add profile-guided optimization of mtasks (#3150).

**Minor:**

* Verilator_gantt has removed the ASCII graphics, use the VCD output instead.
* Verilator_gantt now shows the predicted mtask times, eval times, and additional statistics.
* Verilator_gantt data files now include processor information, to allow later processing.
* Support displaying x and z in $display task (#3107) (#3109). [Iru Cai]
* Fix verilator_profcfunc profile accounting (#3115).
* Fix display has no time units on class function (#3116). [Damien Pretet]
* Fix removing if statement with side effect in condition (#3131). [Alexander Grobman]
* Fix --waiver-output for multiline warnings (#2429) (#3141). [Keith Colbert]
* Fix internal error on bad widths (#3140) (#3145). [Zhanglei Wang]
* Fix crash on clang 12/13 (#3148). [Kuoping Hsu]
* Fix cygwin compile error due to missing -std=gnu++14 (#3149). [Sun Kim]
* Fix $urandom_range when the range is 0 ... UINT_MAX (#3161). [Iru Cai]
* Fix constructor-parameter argument comma-separation in C++ (#3162). [Matthew Ballance]
* Fix missing install of vl_file_copy/vl_hier_graph (#3165). [Popolon]
* Fix calling new with arguments in same class (#3166). [Matthew Ballance]
* Fix false EOFNEWLINE warning when DOS carriage returns present (#3171).


Verilator 4.212 2021-09-01
==========================

**Minor:**

* Fix re-evaluation of logic dependent on state set in DPI exports (#3091). [Geza Lore]
* Support unpacked array localparams in tasks/functions (#3078). [Geza Lore]
* Support timeunit/timeprecision in $unit.
* Support assignment patterns as children of pins (#3041). [Krzysztof Bieganski, Antmicro Ltd]
* Add --instr-count-dpi to tune assumed DPI import cost for multithreaded
  model scheduling. Default value changed to 200 (#3068). [Yinan Xu]
* Output files are split based on the set of headers required
  in order to aid incremental compilation via ccache (#3071). [Geza Lore]
* Parameter values are now emitted as 'static constexpr' instead of enum.
  C++ direct references to parameters might require updating (#3077). [Geza Lore]
* Refactored Verilated include files; include verilated.h not verilated_heavy.h.
* Add header guards on Dpi.h generated files (#2979). [Tood Strader]
* Add XML ccall, constpool, initarray, and if/while begins (#3080). [Steven Hugg]
* Add error when constant function under a generate (#3103). [Don Owen]
* Fix -G to treat simple integer literals as signed (#3060). [Anikin1610]
* Fix emitted string array initializers (#2895). [Iztok Jeras]
* Fix bitop tree optimization dropping necessary & operator (#3096). [Flavien Solt]
* Fix internal error on wide -x-initial unique (#3106). [Alexandre Joannou]
* Fix traces to show array instances with brackets (#3092) (#3095). [Pieter Kapsenberg]


Verilator 4.210 2021-07-07
==========================

**Major:**

* Generated code is now emitted as global functions rather than methods. '$c'
  contents might need to be updated, see the docs (#3006). [Geza Lore]
* The generated model class instantiated by the user is now an interface
  object and no longer the TOP module instance.  User code with direct
  C++ member access to model internals, including verilator public_flat
  items will likely need to be updated. See the manual for instructions:
  https://verilator.org/guide/latest/connecting.html#porting-from-pre-4-210
  (#3036). [Geza Lore]

**Minor:**

* Add --prof-c to pass profiling to compiler (#3059). [Alexander Grobman]
* Optimize a lot more model variables into function locals (#3027). [Geza Lore]
* Support middle-of-design nested top modules (#3026). [Dan Petrisko]
* Remove deprecated --no-relative-cfuncs option (#3024). [Geza Lore]
* Remove deprecated --inhibit-sim option (#3035). [Geza Lore]
* Merge const static data globally into a new constant pool (#3013). [Geza Lore]
* Allow configure override of AR program (#2999). [ahouska]
* In XML, show pinIndex information (#2877). [errae233]
* Fix error on unsupported recursive functions (#2957). [Trefor Southwell]
* Fix type parameter specialization when struct names are same (#3055). [7FM]
* Improve speed of table optimization (-OA) pass. [Geza Lore]


Verilator 4.204 2021-06-12
==========================

**Minor:**

* Add 'make ccache-report' (#3011). [Geza Lore]
* Add --reloop-limit argument (#2943) (#2960). [Geza Lore]
* Add --expand-limit argument (#3005). [Julien Margetts]
* Add TRACE_THREADS to CMake (#2934). [Jonathan Drolet]
* Optimize large lookup tables to static data (#2925). [Geza Lore]
* Optimize reloop to accept constant index offsets (#2939). [Geza Lore]
* Split always blocks to better respect --output-split-cfuncs. [Geza Lore]
* Support ignoring "`pragma protect ..." (#2886). [Udi Finkelstein]
* Support --trace-fst for SystemC with CMake (#2927). [Jonathan Drolet]
* Update cmake latest C++ Standard Compilation flag (#2951). [Ameya Vikram Singh]
* Prep work towards better ccache hashing/performance. [Geza Lore]
* Fix assertion failure in bitOpTree optimization (#2891) (#2899). [Raynard Qiao]
* Fix DPI functions not seen as vpiModule (#2893). [Todd Strader]
* Fix bounds check in VL_SEL_IWII (#2910). [Krzysztof Bieganski, Antmicro Ltd]
* Fix slowdown in elaboration (#2911). [Nathan Graybeal]
* Fix initialization of assoc in assoc array (#2914). [myftptoyman]
* Fix make support for gmake 3.x (#2920) (#2921). [Philipp Wagner]
* Fix VPI memory access for packed arrays (#2922). [Todd Strader]
* Fix MCD close also closing stdout (#2931). [Alexander Grobman]
* Fix split procedures to better respect --output-split-cfuncs (#2942). [Geza Lore]
* Fix to emit 'else if' without nesting (#2944). [Geza Lore]
* Fix part select issues in LATCH warning (#2948) (#2938). [Julien Margetts]
* Fix to not emit empty files with low split limits (#2961). [Geza Lore]
* Fix merging of assignments in C++ code (#2970). [Rupert Swarbrick]
* Fix unused variable warnings (#2991). [Pieter Kapsenberg]
* Fix --protect-ids when using SV classes (#2994). [Geza Lore]
* Fix constant function calls with uninitialized value (#2995). [yanx21]
* Fix Makefiles to support Windows EXEEXT usage (#3008). [Miodrag Milanovic]


Verilator 4.202 2021-04-24
==========================

**Major:**

* Documentation has been rewritten into a book format.
* Verilated signals now use VlWide and VlPacked in place of C arrays.

**Minor:**

* Add an URL on warnings to point to the manual's description.
* Add EOFNEWLINE warning when missing a newline at EOF.
* Changed TIMESCALEMOD from error into a warning.
* Mark --no-relative-cfuncs as scheduled for deprecation.
* Add --coverage-max-width (#2853). [xuejiazidi]
* Add VerilatedCovContext::forcePerInstance (#2793). [Kevin Laeufer]
* Add FST SystemC tracing (#2806). [Àlex Torregrosa]
* Add PINNOTFOUND warning in place of error (#2868). [Udi Finkelstein]
* Support overlaps in priority case statements (#2864). [Rupert Swarbrick]
* Support for null ports (#2875). [Udi Finkelstein]
* Fix class unpacked-array compile error (#2774). [Iru Cai]
* Fix scope types in FST and VCD traces (#2805). [Àlex Torregrosa]
* Fix exceeding command-line ar limit (#2834). [Yinan Xu]
* Fix false $dumpfile warning on model save (#2834). [Yinan Xu]
* Fix --timescale-override not suppressing TIMESCALEMOD (#2838). [Kaleb Barrett]
* Fix false TIMESCALEMOD on generate-ignored instances (#2838). [Kaleb Barrett]
* Fix --output-split with class extends (#2839). [Iru Cai]
* Fix false WIDTHCONCAT on casted constant (#2849). [Rupert Swarbrick]
* Fix tracing of long hashed names (#2854). [Graham Rushton]
* Fix --public-flat-rw / DPI issue (#2858). [Todd Strader]
* Fix interface localparam access (#2859). [Todd Strader]
* Fix Cygwin example compile issues (#2856). [Mark Shaw]
* Fix select of with index variable (#2880). [Alexander Grobman]
* Fix cmake version number to be numeric (#2881). [Yuri Victorovich]
* Fix MinGW not supporting 'localtime_r' (#2882). [HyungKi Jeong]
* Fix cast from packed, typedef'ed interface signal (#2884). [Todd Strader]
* Fix VPI package reported as vpiModule (#2885). [Todd Strader]
* Fix dumping waveforms to multiple FST files (#2889). [David Metz]
* Fix assertion failure in bitOpTree (#2892). [Yutetsu TAKATSUKASA]
* Fix V3Premit infinite loop on always read-and-write (#2898). [Raynard Qiao]
* Fix VPI packed vectors (#2900). [Todd Strader]
* Fix VPI public interface parameters (#2901). [Todd Strader]


Verilator 4.200 2021-03-12
==========================

**Announcement:**

* --inhibit-sim is planned for deprecation, file a bug if this is still being used.

**Major:**

* Add simulation context (VerilatedContext) to allow multiple fully independent
  models to be in the same process. Please see the updated examples. (#2660)
* Add context->time() and context->timeInc() API calls, to set simulation time.
  These now are recommended in place of the legacy sc_time_stamp().

**Minor:**

* Converted Asciidoc documentation into reStructuredText (RST) format.
* Fix range inheritance on port without data type (#2753). [Embedded Go]
* Fix slice-assign overflow (#2803) (#2811). [David Turner]
* Fix interface array connection ordering broken in v4.110 (#2827). [Don Owen]
* Fix or-reduction on different scopes broken in 4.110 (#2828). [Yinan Xu]
* Fix MSVC++ compile error. (#2831) (#2833) [Drew Taussig]


Verilator 4.110 2021-02-25
==========================

**Major:**

* Optimize bit operations and others (#2186) (#2632) (#2633) (#2751) (#2800) [Yutetsu TAKATSUKASA]

**Minor:**

* Support concat selection (#2721).
* Support struct scopes when dumping structs to VCD (#2776) [Àlex Torregrosa]
* Generate SELRANGE for potentially unreachable code (#2625) (#2754) [Pierre-Henri Horrein]
* For --flatten, override inlining of public and no_inline modules (#2761) [James Hanlon]
* Fix little endian interface pin swizzling (#2475). [Don Owen]
* Fix range inheritance on port without data type (#2753). [Embedded Go]
* Fix TIMESCALE warnings on primitives (#2763). [Xuanqi]
* Fix to exclude strings from toggle coverage (#2766) (#2767) [Paul Wright]
* Fix $fread extra semicolon inside statements. [Leendert van Doorn]
* Fix class extends with VM_PARALLEL_BUILDS (#2775). [Iru Cai]
* Fix shifts by > 32 bit values (#2785). [qrq992]
* Fix examples not flushing vcd (#2787). [Richard E George]
* Fix little endian packed array pattern assignment (#2795). [Àlex Torregrosa]


Verilator 4.108 2021-01-10
==========================

**Major:**

* Many VPI changes for IEEE compatibility, which may alter behavior from previous releases.
* Support randomize() class method and rand (#2607). [Krzysztof Bieganski, Antmicro Ltd]

**Minor:**

* Support $cast and new CASTCONST warning.
* Add --top option as alias of --top-module.
* Add LATCH and NOLATCH warnings (#1609) (#2740). [Julien Margetts]
* Remove Unix::Processors internal test dependency.
* Report UNUSED on parameters, localparam and genvars (#2627). [Charles Eric LaForest]
* Add error on real to non-real output pins (#2690). [Peter Monsson]
* Support package imports before parameters in interfaces (#2714). [James Hanlon]
* Support --sanitize in internal tests (#2705). [Yutetsu TAKATSUKASA]
* Fix passing parameter type instantiations by position number.
* Fix DPI open array handling issues.
* Fix error when dotted refers to missing module (#2095). [Alexander Grobman]
* Fix little endian packed array counting (#2499). [phantom-killua]
* Fix showing reference locations for BLKANDNBLK (#2170). [Yuri Victorovich]
* Fix genblk naming to match IEEE (#2686). [tinshark]
* Fix VPI memory word indexing (#2695). [Marlon James]
* Fix vpiLeftRange on little-endian memories (#2696). [Marlon James]
* Fix VPI module tree (#2704). [Todd Strader]
* Fix vpi_release_handle to be called implicitly per IEEE (#2706).
* Fix to allow inheriting 'VerilatedVcdFile' class. (#2720) [HyungKi Jeong]
* Fix $urandom_range maximum value (#2723). [Nandu Raj]
* Fix tracing empty sc module (#2729).
* Fix generate for unrolling to be signed (#2730). [yanx21]
* Fix to emit timescale in hierarchical blocks (#2735). [Yutetsu TAKATSUKASA]
* Fix to ignore coverage on real ports (#2741) (#2745). [Paul Wright]



Verilator 4.106 2020-12-02
==========================

**Major:**

* Change -sv option to select 1800-2017 instead of 1800-2005.

**Minor:**

* Check for proper 'local' and 'protected' (#2228).
* Support $random and $urandom seeds.
* Support $monitor and $strobe.
* Support complex function arguments.
* Support 'super'.
* Support 'with item.index'.
* Fix the default GNU Make executable name on FreeBSD (#2553). [Yuri Victorovich]
* Fix trace signal names getting hashed (#2643).  [Barbara Gigerl]
* Fix unpacked array parameters near functions (#2639). [Anderson Ignacio da Silva]
* Fix access to non-overridden base class variable (#2654). [Tobias Rosenkranz]


Verilator 4.104 2020-11-14
==========================

**Minor:**

* Support queue and associative array 'with' statements (#2616).
* Support queue slicing (#2326).
* Support associative array pattern assignments and defaults.
* Support static methods and typedefs in classes (#2615). [Krzysztof Bieganski, Antmicro Ltd]
* Add error on typedef referencing self (#2539). [Cody Piersall]
* With --debug, turn off address space layout randomization.
* Fix iteration over mutating list bug in VPI (#2588). [Kaleb Barrett]
* Fix cast width propagation (#2597). [flex-liu]
* Fix return from callValueCbs (#2589) (#2605). [Marlon James]
* Fix WIDTH warnings on comparisons with nullptr (#2602). [Rupert Swarbrick]
* Fix fault when $fgets, $sscanf, etc used with string (#2604). [Yutetsu TAKATSUKASA]
* Fix WIFEXITED missing from MinGW/MSYS2 (#2609). [Jean Berniolles]
* Fix queue poping wrong value when otherwise unused (#2512). [nanduraj1]
* Fix arrays of modport interfaces (#2614). [Thierry Tambe]
* Fix split_var internal error (#2640) (#2641). [Yutetsu TAKATSUKASA]


Verilator 4.102 2020-10-15
==========================

**Minor:**

* Support const object new() assignments.
* Support # as a comment in -f files (#2497). [phantom-killua]
* Support 'this' (#2585). [Rafal Kapuscik]
* Support defines for FST tracing (#2592). [Markus Krause]
* Support non-overlapping implication inside properties (#1292). [Peter Monsson]
* Fix timescale with --hierarchical (#2554). [Yutetsu TAKATSUKASA]
* Fix cmake build with --hierarchical (#2560). [Yutetsu TAKATSUKASA]
* Fix -G dropping public indication (#2561). [Andrew Goessling]
* Fix $urandom_range passed variable (#2563). [nanduraj1]
* Fix method calls to package class functions (#2565). [Peter Monsson]
* Fix class wide member display (#2567). [Nandu Raj P]
* Fix hierarchical references inside function (#2267) (#2572). [James Pallister]
* Fix flushCall for backward compatibility (#2580). [chenguokai]
* Fix preprocessor stringify of undefined macro. [Martin Whitaker]


Verilator 4.100 2020-09-07
==========================

**Major:**

* C++11 or newer compilers are now required.
* SystemC 2.3.0 or newer (SYSTEMC_VERSION >= 20111121) is now required.
* Support hierarchical Verilation (#2206). [Yutetsu TAKATSUKASA]

**Minor:**

* Support (with limitations) class extern, class extends, virtual class.
* Support $urandom, $urandom_range without stability.
* Support assume property. [Peter Monsson]
* Support non-overlapping implication inside properties (#1292). [Peter Monsson]
* Fix false DECLFILENAME on black-boxed modules (#2430). [Philipp Wagner]
* Fix naming of "id : begin" blocks.
* Fix class constructor error on assignments to const.
* Fix splitting eval functions with --output-split-cfuncs (#2368). [Geza Lore]
* Fix queues as class members (#2525). [nanduraj1]


Verilator 4.040 2020-08-15
==========================

**Announcement:**

* Version 4.040 is planned to be the final version that will support
  pre-C++11 compilers. Please move to C++11 or newer compilers.

**Minor:**

* Fix arrayed interfaces, broke in 4.038 (#2468). [Josh Redford]
* Support $stable, $rose and $fell.  (#2148) (#2501) [Peter Monsson]
* Support simple function localparams (#2461). [James Hanlon]
* Miscellaneous parsing error changes towards UVM support.
* Fix arrayed interfaces (#2469).  [Josh Redford]
* Fix protect lib VCS warning. (#2479) [Julien Margetts]
* Fix combining different-width parameters (#2484). [abirkmanis]
* Fix protect-lib without sequential logic (#2492). [Yutetsu TAKATSUKASA]
* Fix V3Unknown from running with flat XML output (#2494). [James Hanlon]
* Fix non-32 bit conversion to float (#2495). [dsvf]
* Fix casting non-self-determined subexpressions (#2493). [phantom-killua]
* Fix SystemC net names (#2500). [Edgar E. Iglesias]
* Fix build with Bison 3.7 and newer (#2505). [Rupert Swarbrick]
* Fix slice of unpacked array (#2506) (#2507). [Yutetsu TAKATSUKASA]


Verilator 4.038 2020-07-11
==========================

**Announcement:**

* Versions 4.038 and 4.040 are planned to be the final versions that will
  support pre-C++11 compilers. Please move to C++11 or newer compilers.

**Minor:**

* Support VPI access to parameters and localparam. [Ludwig Rogiers]
* Support parsing (not elaboration, yet) of UVM.
* Add new UNSUPPORTED error code to replace most previous Unsupported: messages.
* With --bbox-unsup continue parsing on many (not all) UVM constructs.
* Support for-loop increments with commas.
* Support $swrite with arbitrary arguments.
* Support $writememb (#2450). [Fan Shupei]
* Fix OS X, Free BSD, and -m32 portability issues. [Geza Lore]
* Fix to flush FST trace on termination due to $stop or assertion failure.
* Fix part select error when multipling by power-of-two (#2413). [Conor McCullough]
* Fix division exception (#2460) [Kuoping Hsu]


Verilator 4.036 2020-06-06
==========================

**Major:**

* OPT_FAST is now -Os by default. See the BENCHMARKING & OPTIMIZATION part
  of the manual if you experience issues with compilation speed.
* --output-split is now on by default. VM_PARALLEL_BUILDS is set by default
  iff the --output-split caused an actual file split to
  occur.  --output-split-cfuncs and --output-split-ctrace now default to
  the value of --output-split. These changes should improve build times of
  medium and large designs with default options. User makefiles may require
  changes.

**Minor:**

* Configure now enables SystemC if it is installed as a system headers,
  e.g. with 'apt-get install systemc-dev'.
* Add --waiver-output flag that writes a verilator config file (.vlt) with
  waivers to the warnings emitted during a Verilator run.
* Support verilator_coverage --write-info for lcov HTML reports.
* Line Coverage now tracks all statement lines, not just branch lines.
* The run-time library is now compiled with -Os by default. (#2369, #2373)
* Support multi channel descriptor I/O (#2190) [Stephen Henry]
* Support $countbits. (#2287) [Yossi Nivin]
* Support $isunbounded and parameter $. (#2104)
* Support unpacked array .sum and .product.
* Support prefix/postfix increment/decrement. (#2223) [Maciej Sobkowski]
* Fix FST tracing of little bit endian signals. [Geza Lore]
* Fix +: and -: on unpacked arrays. (#2304) [engr248]
* Fix $isunknown with constant Z's.
* Fix queues and dynamic array wide ops. (#2352) [Vassilis Papaefstathiou]


Verilator 4.034 2020-05-03
==========================

**Major:**

* Support simplistic classes with many restrictions, see manual. (#377)
* Support IEEE time units and time precisions. (#234)
  Includes `timescale, $printtimescale, $timeformat.
  VL_TIME_MULTIPLIER, VL_TIME_PRECISION, VL_TIME_UNIT have been removed
  and the time precision must now match the SystemC time precision. To
  get closer behavior to older versions, use e.g. --timescale-override
  "1ps/1ps".
* Add --build to call make automatically. (#2249) [Yutetsu TAKATSUKASA]
* Configuring with ccache present now defaults to using it; see OBJCACHE.
* Fix DPI import/export to be standard compliant. (#2236) [Geza Lore]
* Add --trace-threads for general multithreaded tracing. (#2269) [Geza Lore]

**Minor:**

* Add --flatten for use with --xml-only. (#2270) [James Hanlon]
* Greatly improve FST/VCD dump performance (#2244) (#2246) (#2250) (#2257) [Geza Lore]
* Support $ferror, and $fflush without arguments. (#1638)
* Support event data type (with some restrictions).
* Support $root. (#2150) [Keyi Zhang]
* Add error if use SystemC 2.2 and earlier (pre-2011) as is deprecated.
* Add support of --trace-structs for CMake (#2986). [Martin Schmidt]
* Fix arrayed instances connecting to slices. (#2263) [Don/engr248]
* Fix error on unpacked connecting to packed. (#2288) [Joseph Shaker]
* Fix logical not optimization with empty begin. (#2291) [Baltazar Ortiz]
* Fix reduction OR on wide data, broke in v4.026. (#2300) [Jack Koenig]
* Fix clock enables with bit-extends. (#2299) [Marco Widmer]
* Fix MacOs Homebrew by removing default LIBS. (#2298) [Ryan Clarke]


Verilator 4.032 2020-04-04
==========================

**Minor:**

* Add column numbers to errors and warnings.
* Add GCC 9-style line number prefix when showing source text for errors.
* Add setting VM_PARALLEL_BUILDS=1 when using --output-split. (#2185)
* Change --quiet-exit to also suppress 'Exiting due to N errors'.
* Suppress REALCVT for whole real numbers.
* Support split_var in vlt files. (#2219) [Marco Widmer]
* Fix parameter type redeclaring a type. (#2195) [hdzhangdoc]
* Fix VCD open with empty filename. (#2198) [Julius Baxter]
* Fix packages as enum base types. (#2202) [Driss Hafdi]
* Fix duplicate typedefs in generate for. (#2205) [hdzhangdoc]
* Fix MinW portability. (#2114) [Sean Cross]
* Fix assertions with unique case inside. (#2199) [hdzhangdoc]
* Fix implicit conversion of floats to wide integers.


Verilator 4.030 2020-03-08
==========================

**Major:**

* Add split_var metacomment to assist UNOPTFLAT fixes. (#2066) [Yutetsu TAKATSUKASA]
* Support $dumpfile and $dumpvars. (#2126) [Alexander Grobman]
* Support dynamic arrays. (#379)

**Minor:**

* Add +verilator+noassert flag to disable assertion checking. [Tobias Wölfel]
* Add check for assertOn for asserts. (#2162) [Tobias Wölfel]
* Add --structs-packed for forward compatibility.
* Support $displayb/o/h, $writeb/o/h, etc. (#1637)
* Use gcc -Os in examples instead of -O2 for better average performance.
* Fix genblk naming with directly nested generate blocks. (#2176) [Alexander Grobman]
* Fix undeclared VL_SHIFTR_WWQ. (#2114) [Alex Solomatnikov]


Verilator 4.028 2020-02-08
==========================

**Major:**

* Support attributes (public, isolate_assignments, etc.) in configuration files.
* Add -match to lint_off to waive warnings. [Philipp Wagner]

**Minor:**

* Link Verilator binary partially statically. (#2146) [Geza Lore]
* Verilation speed improvements (#2133) (#2138) [Geza Lore]
* Support libgoogle-perftools-dev's libtcmalloc if available. (#2137) [Geza Lore]
* Support $readmem/$writemem with assoc arrarys. (#2100) [agrobman]
* Support type(expression) operator and $typename. (#1650)
* Support left justified $display. (#2101) [Pieter Kapsenberg]
* Support string character access via indexing.
* Support enum.next(k) with constant k > 1. (#2125) [Tobias Rosenkranz]
* Support parameter access from arrays of interfaces. (#2155) [Todd Strader]
* Add parameter values in XML. #2110. [Pieter Kapsenberg]
* Add loc column location in XML (replaces fl). (#2122) [Pieter Kapsenberg]
* Add error on misused define. [Topa Tota]
* Add parameter to set maximum signal width. (#2082) [Øyvind Harboe]
* Add warning on genvar in normal for loop. (#2143) [Yuri Victorovich]
* Fix VPI scope naming for public modules. [Nandu Raj]
* Fix FST tracing of enums inside structs. [fsiegle]
* Fix WIDTH warning on </<= of narrower value. (#2141) [agrobman]
* Fix OpenSolaris issues. (#2154) [brancoliticus]
* Fix gated clocks under --protect-lib. (#2169) [Todd Strader]


Verilator 4.026 2020-01-11
==========================

**Major:**

* Docker images are now available for Verilator releases.

**Minor:**

* Support bounded queues.
* Support non-overlapping implication operator in assertions. (#2069) [Peter Monsson]
* Support string compare, ato*, etc methods. (#1606) [Yutetsu TAKATSUKASA]
* Support immediate cover statements.
* Ignore `uselib to end-of-line. (#1634) [Frederic Antonin]
* Update FST trace API for better performance.
* Add vpiTimeUnit and allow to specify time as string. (#1636) [Stefan Wallentowitz]
* Add error when `resetall inside module (IEEE 2017-22.3).
* Add cleaner error on version control conflicts in sources.
* Fix little endian cell ranges. (#1631) [Julien Margetts]
* Fix queue issues (#1641) (#1643) [Peter Monsson, Stefan Wallentowitz]
* Fix strcasecmp for windows. (#1651) [Kuba Ober]
* Fix disable iff in assertions. Closes #1404. [Peter Monsson]
* Fix huge case statement performance. Closes #1644. [Julien Margetts]
* Fix tracing -1 index arrays. Closes #2090. [Yutetsu Takatsukasa]
* Fix expand optimization slowing --lint-only. Closes #2091. [Thomas Watts]
* Fix %{number}s with strings. #2093. [agrobman]
* Fix shebang breaking some shells. Closes #2067. [zdave]
* Fix errors on using string in incorrect format (#5240). [John Demme]


Verilator 4.024 2019-12-08
==========================

**Major:**

* Support associative arrays (excluding [*] and pattern assignments). (#544)
* Support queues (excluding {} notation and pattern assignments). (#545)

**Minor:**

* Add +verilator+error+limit to see more assertion errors. [Peter Monsson]
* Support string.toupper and string.tolower.
* Support $rewind and $ungetc.
* Support shortreal as real, with a SHORTREAL warning.
* Add -Wpedantic and -Wno-context for compliance testing.
* Add error on redefining preprocessor directives. [Piotr Binkowski]
* Support $value$plusargs float and shorts. (#1592) (#1619) [Garrett Smith]
* Fix gate lvalue optimization error. (#831) [Jonathon Donaldson, Driss Hafdi]
* Fix color assertion on empty if. (#1604) [Andrew Holme]
* Fix for loop missing initializer. (#1605) [Andrew Holme]
* Fix hang on concat error. (#1608) [Bogdan Vukobratovic]
* Fix VPI timed callbacks to be one-shot, pull5. [Matthew Ballance]
* Fix // in filenames. (#1610) [Peter Nelson]
* Fix $display("%p") to be closer to IEEE.
* Fix labels on functions with returns. (#1614) [Mitch Hayenga]
* Fix false unused message on __Vemumtab. (#2061) [Tobias Rosenkranz]
* Fix assertion on dotted parameter arrayed function. (#1620) [Rich Porter]
* Fix interface reference tracing. (#1595) [Todd Strader]
* Fix error on unpacked concatenations. (#1627) [Driss Hafdi]


Verilator 4.022 2019-11-10
==========================

**Major:**

* Add --protect-lib. (#1490) [Todd Strader]
* Add cmake support. (#1363) [Patrick Stewart]

**Minor:**

* Examples have been renamed.
* Add --protect-ids to obscure information in objects. (#1521) [Todd Strader]
* Add --trace-coverage.
* Add --xml-output.
* Support multithreading on Windows. [Patrick Stewart]
* Suppress 'command failed' on normal errors.
* Support some unpacked arrays in parameters. (#1315) [Marshal Qiao]
* Add interface port visibility in traces. (#1594) [Todd Strader]
* Increase case duplicate/incomplete to 16 bit tables. (#1545) [Yossi Nivin]
* Support quoted arguments in -f files. (#1535) [Yves Mathieu]
* Optimize modulus by power-of-two constants, and masked conditionals.
* Fix detecting missing reg types. (#1570) [Jacko Dirks]
* Fix multithreaded yield behavior when no work. [Patrick Stewart]
* Fix bad-syntax crashes. (#1548, #1550-#1553, #1557-#1560, #1563,
  #1573-#1577, #1579, #1582-#1591) [Eric Rippey]
* Fix false CMPCONST/UNSIGNED warnings on "inside". (#1581) [Mitch Hayenga]


Verilator 4.020 2019-10-06
==========================

**Minor:**

* Add --public-flat-rw. (#1511) [Stefan Wallentowitz]
* Support $fseek, $ftell, $frewind. (#1496) [Howard Su]
* Support vpiModule. (#1469) [Stefan Wallentowitz]
* Make Syms file honor --output-split-cfuncs. (#1499) [Todd Strader]
* Fix make test with no VERILATOR_ROOT. (#1494) [Ahmed El-Mahmoudy]
* Fix error on multidimensional cells. (#1505) [Anderson Ignacio Da Silva]
* Fix config_rev revision detection on old versions.
* Fix false warning on backward indexing. (#1507) [Hao Shi]
* Fix vpiType accessor. (#1509) (#1510) [Stefan Wallentowitz]
* Fix ugly error on interface misuse. (#1525) [Bogdan Vukobratovic]
* Fix misc bad-syntax crashes. (#1529) (#1530) (#1531) (#1532) (#1533) [Eric Rippey]
* Fix case statements with strings. (#1536) [Philipp Wagner]
* Fix some coverage lost when multithreaded. (#2151)


Verilator 4.018 2019-08-29
==========================

**Major:**

* When showing an error, show source code and offer suggestions of replacements.
* When showing an error, show the instance location. (#1305) [Todd Strader]

**Minor:**

* Add --rr. (#1481) [Todd Strader]
* Change MULTITOP to warning to help linting, see manual.
* Add XSim support to driver.pl. (#1493) [Todd Strader]
* Add --dpi-hdr-only. (#1491) [Todd Strader]
* Show included-from filenames in warnings. (#1439) [Todd Strader]
* Fix elaboration time errors. (#1429) [Udi Finkelstein]
* Fix not reporting some duplicate signals/ports. (#1462) [Peter Gerst]
* Fix not in array context on non-power-of-two slices. (#2027) [Yu Sheng Lin]
* Fix system compile flags injection. [Gianfranco Costamagna]
* Fix enum values not being sized based on parent. (#1442) [Dan Petrisko]
* Fix internal error on gate optimization of assign. (#1475) [Oyvind Harboe]



Verilator 4.016 2019-06-16
==========================

**Minor:**

* Add --quiet-exit. (#1436) [Todd Strader]
* Error continuation lines no longer have %Error prefix.
* Support logical equivalence operator <->.
* Support VerilatedFstC set_time_unit. (#1433) [Pieter Kapsenberg]
* Support deferred assertions. (#1449) [Charles Eddleston]
* Mark infrequently called functions with GCC cold attribute.
* Fix sign-compare warning in verilated.cpp. (#1437) [Sergey Kvachonok]
* Fix fault on $realtime with %t. (#1443) [Julien Margetts]
* Fix $display with string without %s. (#1441) [Denis Rystsov]
* Fix parameter function string returns. (#1441) [Denis Rystsov]
* Fix invalid XML output due to special chars. (#1444) [Kanad Kanhere]
* Fix performance when mulithreaded on 1 CPU. (#1455) [Stefan Wallentowitz]
* Fix type and real parameter issues (#1427) (#1456) (#1458) [Todd Strader]
* Fix build error on MinGW. (#1460) [Richard Myers]
* Fix not reporting some duplicate signals. (#1462) [Peter Gerst]
* Fix --savable invalid C++ on packed arrays. (#1465) [Alex Chadwick]
* Fix constant function return of function var. (#1467) [Roman Popov]


Verilator 4.014 2019-05-08
==========================

**Minor:**

* Add --trace-fst-thread.
* Support '#' comments in $readmem. (#1411) [Frédéric Requin]
* Support "'dx" constants. (#1423) [Udi Finkelstein]
* For FST tracing use LZ4 compression. [Tony Bybell]
* Add error when use parameters without value. (#1424) [Peter Gerst]
* Auto-extend and WIDTH warn on unsized X/Zs. (#1423) [Udi Finkelstein]
* Fix missing VL_SHIFTL errors. (#1412) (#1415) [Larry Lee]
* Fix MinGW GCC 6 printf formats. (#1413) [Sergey Kvachonok]
* Fix test problems when missing fst2vcd. (#1417) [Todd Strader]
* Fix GTKWave register warning. (#1421) [Pieter Kapsenberg]
* Fix FST enums not displaying. (#1426) [Danilo Ramos]
* Fix table compile error with multiinterfaces. (#1431) [Bogdan Vukobratovic]


Verilator 4.012 2019-03-23
==========================

**Minor:**

* Add +verilator+seed. (#1396) [Stan Sokorac]
* Support $fread. [Leendert van Doorn]
* Support void' cast on functions called as tasks. (#1383) [Al Grant]
* Add IGNOREDRETURN warning. (#1383)
* Report PORTSHORT errors on concat constants. (#1400) [Will Korteland]
* Fix VERILATOR_GDB being ignored. (#2017) [Yu Sheng Lin]
* Fix $value$plus$args missing verilated_heavy.h. [Yi-Chung Chen]
* Fix MSVC compile error. (#1406) [Benjamin Gartner]
* Fix maintainer test when no Parallel::Forker. (#1977) [Enzo Chi]
* Fix +1364-1995ext flags applying too late. (#1384) [Al Grant]


Verilator 4.010 2019-01-27
==========================

**Minor:**

* Removed --trace-lxt2, use --trace-fst instead.
* For --xml, add additional information. (#1372) [Jonathan Kimmitt]
* Add circular typedef error. (#1388)  [Al Grant]
* Add unsupported for loops error. (#1986) [Yu Sheng Lin]
* Fix FST tracing of wide arrays. (#1376) [Aleksander Osman]
* Fix error when pattern assignment has too few elements. (#1378) [Viktor Tomov]
* Fix error when no modules in $unit. (#1381) [Al Grant]
* Fix missing too many digits warning. (#1380) [Jonathan Kimmitt]
* Fix uninitialized data in verFiles and unroller. (#1385) (#1386) [Al Grant]
* Fix internal error on xrefs into unrolled functions. (#1387) [Al Grant]
* Fix DPI export void compiler error. (#1391) [Stan Sokorac]


Verilator 4.008 2018-12-01
==========================

**Minor:**

* Support "ref" and "const ref" pins and functions. (#1360) [Jake Longo]
* In --xml-only show the original unmodified names, and add module_files
  and cells similar to Verilog-Perl, msg2719. [Kanad Kanhere]
* Add CONTASSREG error on continuous assignments to regs. (#1369) [Peter Gerst]
* Add PROCASSWIRE error on behavioral assignments to wires, msg2737. [Neil Turton]
* Add IMPORTSTAR warning on import::* inside $unit scope.
* Fix --trace-lxt2 compile error on MinGW. (#1990) [HyungKi Jeong]
* Fix hang on bad pattern keys. (#1364) [Matt Myers]
* Fix crash due to cygwin bug in getline. (#1349) [Affe Mao]
* Fix __Slow files getting compiled with OPT_FAST. (#1370) [Thomas Watts]


Verilator 4.006 2018-10-27
==========================

**Minor:**

* Add --pp-comments. (#1988) [Robert Henry]
* Add --dump-defines.
* For --trace-fst, save enum decoding information. (#1358) [Sergi Granell]
  (To visualize enumeration data you must use GTKwave 3.3.95 or newer.)
* For --trace-fst, combine hier information into FST. [Tony Bybell]
* Fix --trace-lxt2 compile error on MinGW, msg2667. [HyungKi Jeong]
* Fix Windows .exe not found. (#1361) [Patrick Stewart]


Verilator 4.004 2018-10-06
==========================

**Major:**

* Add GTKWave FST native tracing. (#1356) [Sergi Granell]
  (Verilator developers need to pull the latest vcddiff.)

**Minor:**

* Support $past. [Dan Gisselquist]
* Support restrict. (#1350) [Clifford Wolf]
* Rename include/lxt2 to include/gtkwave.
* Fix replication of 64-bit signal change detects.
* Fix Mac OSX 10.13.6 / LLVM 9.1 compile issues. (#1348) [Kevin Kiningham]
* Fix MinGW compile issues. (#1979) [HyungKi Jeong]


Verilator 4.002 2018-09-16
==========================

**Major:**

* This is a major release. Any patches may require major rework to apply.
  [Thanks everyone]
* Add multithreaded model generation.
* Add runtime arguments.
* Add GTKWave LXT2 native tracing. (#1333) [Yu Sheng Lin]
* Note $random has new algorithm; results may vary vs. previous versions.

**Minor:**

* Better optimize large always block splitting. (#1244) [John Coiner]
* Add new reloop optimization for repetitive assignment compression.
* Support string.atoi and similar methods. (#1289) [Joel Holdsworth]
* Fix internals to be C++ null-pointer-check clean.
* Fix internals to avoid 'using namespace std'.
* Fix Verilation performance issues. (#1316) [John Coiner]
* Fix clocker attributes to not propagate on concats. [John Coiner]
* Fix first clock edge and --x-initial-edge. (#1327) [Rupert Swarbrick]
* Fix compile error on tracing of string arrays. (#1338) [Iztok Jeras]
* Fix number parsing with newline after radix. (#1340) [George Cuan]
* Fix string ?: conditional type resolution. (#1345) [Iztok Jeras]
* Fix duplicate symbol error on generate tri. (#1347) [Tomas Dzetkulic]


Verilator 3.926 2018-08-22
==========================

**Minor:**

* Add OBJCACHE envvar support to examples and generated Makefiles.
* Change MODDUP errors to warnings. (#1969) [Marshal Qiao]
* Fix define argument stringification (`"), broke since 3.914. [Joe DErrico]
* Fix to ignore Unicode UTF-8 BOM sequences. (#1967) [HyungKi Jeong]
* Fix std:: build error. (#1322)
* Fix function inlining inside certain while loops. (#1330) [Julien Margetts]


Verilator 3.924 2018-06-12
==========================

**Minor:**

* Renamed --profile-cfuncs to --prof-cfuncs.
* Report interface ports connected to wrong interface. (#1294) [Todd Strader]
* When tracing, use scalars on single bit arrays to appease vcddiff.
* Fix parsing "output signed" in V2K port list, msg2540. [James Jung]
* Fix parsing error on bad missing #. (#1308) [Dan Kirkham]
* Fix $clog2 to be in verilog 2005. (#1319) [James Hutchinson]


Verilator 3.922 2018-03-17
==========================

**Major:**

* Support IEEE 1800-2017 as default language.

**Minor:**

* Support trig functions ($sin() etc). (#1281) [Patrick Stewart]
* Support calling system functions as tasks. (#1285) [Joel Holdsworth]
* Support assert properties. (#785) (#1290) [John Coiner, et al]
* Support $writememh. [John Coiner]
* Add --no-debug-leak to reduce memory use under debug. [John Coiner]
* Fix severe runtime performance bug in certain foreach loops. [John Coiner]
* On convergence errors, show activity. [John Coiner]
* Fix GCC 8.0 issues. (#1273)
* Fix pullup/pulldowns on bit selects. (#1274) [Rob Stoddard]
* Fix verilator_coverage --annotate-min. (#1284) [Tymoteusz Blazejczyk]
* Fix quoting of quoted arguments. [John Coiner]


Verilator 3.920 2018-02-01
==========================

**Announcement:**

* Moving forward, use the git "stable" branch to track the latest release,
  and git "v#.###" tags for specific releases.

**Minor:**

* Support 'assume' similar to 'assert'. (#1269) [Dan Gisselquist]
* Remove c++filt. (#1265) [Stefan Wallentowitz]
* Fix tracing example file output. (#1268) [Enzo Chi]
* Fix gate optimization out of memory, add --gate-stmts. (#1260) [Alex Solomatnikov]
* Fix compile error on public real parameters by suppressing. (#1261) [Alex Solomatnikov]
* Fix input-only tristate comparisons. (#1267) [Alexis G]
* Fix missing edge type in xml output. (#1955) [Alexis G]
* Fix compile error with --public and interface bind. (#1264) [Alexis G]


Verilator 3.918 2018-01-02
==========================

**Minor:**

* Workaround GCC/clang bug with huge compile times. (#1248)
* Support DPI open arrays. (#909) (#1245) [David Pierce, Victor Besyakov]
* Add INFINITELOOP warning. (#1254) [Alex Solomatnikov]
* Support > 64 bit decimal $display.
* Support DPI time and svLogicVal. [Victor Besyakov]
  Note older version incorrectly assumed svBitVal even for logicals.
* Support string len() method. [Victor Besyakov]
* Add error if always_comb has sensitivity list. [Arjen Roodselaar]
* Fix SystemC 2.3.2 compile error. (#1251) [Tymoteusz Blazejczyk]
* Fix modport outputs being treated as inputs. (#1246) [Jeff Bush]
* Fix false ALWCOMBORDER on interface references. (#1247) [Josh Redford]
* Fix constant propagation across DPI imports of inout strings. [Victor Besyakov]
* Fix resolving inline nested interface names. (#1250) [Arjen Roodselaar]
* Fix GCC false warning on array bounds. (#2386)


Verilator 3.916 2017-11-25
==========================

**Minor:**

* Support self-recursive modules. (#659) [Sean Moore, et al]
* Support $error/$warning in elaboration time blocks.
* Support $size/$bits/etc on type references.
* Add error when driving input-only modport. (#1110) [Trevor Elbourne]
* Add BSSPACE and COLONPLUS lint warnings.
* Detect MSB overflow when under VL_DEBUG. (#1238) [Junyi Xi]
* Add data types to --xml. [Rui Terra]
* Fix partial slicing with pattern assignments. (#991) [Johan Bjork]
* Fix false unused warning on interfaces. (#1241) [Laurens van Dam]
* Fix error on "unique case" with no cases.
* Fix MacOS portability. (#1232) [Jeff Bush]


Verilator 3.914 2017-10-14
==========================

**Major:**

* Add new examples/ directory with appropriate examples. This replaces the
  old test_c and test_sc directories.

**Minor:**

* Add --getenv option for simplifying Makefiles.
* Add --x-initial option for specifying initial value assignment behavior.
* Add --no-relative-cfuncs and related default optimization. (#1224) [John Coiner]
* Add /*verilator tag*/ for XML extraction applications. [Chris Randall]
* The internal test_verilated test directory is moved to be part of test_regress.
* The experimental VL_THREADED setting (only, not normal mode) now requires C++11.
* Fix over-aggressive inlining. (#1223) [John Coiner]
* Fix Ubuntu 17.10 issues. (#1223 partial). [John Coiner]
* Fix compiler warning when WIDTH warning ignored on large compare.
* Fix memory leak in VerilatedVcd dumps. (#1222 partial) [Shareef Jalloq]
* Fix unnecessary Vdly variables. (#1224 partial) [John Coiner]
* Fix conditional slices and add related optimizations.
* Fix \`\` expansion of `defines. (#1225) (#1227) (#1228) [Odd Magne Reitan]
* Fix -E duplicating output. (#1226) [Odd Magne Reitan]
* Fix float-conversion warning. (#1229) [Robert Henry]
* Fix MacOS portability. (#1230) (#1231) [Jeff Bush]


Verilator 3.912 2017-09-23
==========================

**Major:**

* Verilated headers no longer "use namespace std;"
  User's code without "std::" prefixes may need "use namespace std;" to compile.

**Minor:**

* Support or/and/xor array intrinsic methods. (#1210) [Michael Popoloski]
* Support package export. (#1217) [Usuario Eda]
* Support module port parameters without defaults. (#1213) [Michael Popoloski]
* Add performance information to --stats file.
* Simplify VL_CONST_W macro generation for faster compiles.
* Optimize improvements for Shift-And, and replication constructs.
* Fix ordering of arrayed cell wide connections. (#1202 partial) [Michael Popoloski]
* Fix LITENDIAN warning on arrayed cells. (#1202) [Michael Popoloski]
* Fix enum ranges without colons. (#1204) [Michael Popoloski]
* Fix GCC noreturn compile error. (#1209) [Michael Popoloski]
* Fix constant function default parameters. (#1211) [Michael Popoloski]
* Fix non-colon array of interface modports. (#1212) [Michael Popoloski]
* Fix .name connections on interfaces. (#1214) [Michael Popoloski]
* Fix wide array indices causing compile error.


Verilator 3.910 2017-09-07
==========================

**Major:**

* SystemPerl mode (-sp-deprecated) has been removed.

**Minor:**

* Update keyword warnings to include C++11 and others.


Verilator 3.908 2017-08-28
==========================

**Minor:**

* Support x in $readmem. (#1180) [Arthur Kahlich]
* Support packed struct DPI imports. (#1190) [Rob Stoddard]
* Fix GCC 6 warnings.
* Fix compile error on unused VL_VALUEPLUSARGS_IW. (#1181) [Thomas J Whatson]
* Fix undefined VL_POW_WWI. [Clifford Wolf]
* Fix internal error on unconnected inouts. (#1187) [Rob Stoddard]


Verilator 3.906 2017-06-22
==========================

**Minor:**

* Support set_time_unit/set_time_precision in C traces. (#1937)
* Fix extract of packed array with non-zero LSB. (#1172) [James Pallister]
* Fix shifts by more than 32-bit numbers. (#1174) [Clifford Wolf]
* Fix power operator on wide constants. (#761) [Clifford Wolf]
* Fix .* on interface pins. (#1176) [Maciej Piechotka]


Verilator 3.904 2017-05-30
==========================

**Minor:**

* Fix non-cutable ordering loops on clock arrays. (#1009) [Todd Strader]
* Support ports of array of reals. (#1154) [J Briquet]
* Support arrayed parameter overrides. (#1153) [John Stevenson]
* Support $value$plusargs with variables. (#1165) [Wesley Terpstra]
* Support modport access to un-modport objects. (#1161) [Todd Strader]
* Add stack trace when can't optimize function. (#1158) [Todd Strader]
* Add warning on mis-sized literal. (#1156) [Todd Strader]
* Fix interface functions returning wrong parameters. (#996) [Todd Strader]
* Fix non-arrayed cells with interface arrays. (#1153) [John Stevenson]
* Fix --assert with complex case statements. (#1164) [Enzo Chi]


Verilator 3.902 2017-04-02
==========================

**Major:**

* Add -FI option to force includes. (#1916) [Amir Gonnen]
* Add --relative-includes. [Rob Stoddard]

**Minor:**

* Add error on duplicate pattern assignments. (#1145) [Johan Bjork]
* Fix error on improperly widthed default function. (#984) [Todd Strader]
* Fix 2009 localparam syntax, msg2139. [Galen Seitz]
* Fix ugly interface-to-non-interface errors. (#1112) [Johan Bjork]
* Fix LDFLAGS and CFLAGS not preserving order. (#1130) [Olof Kindgren]
* Fix internal error on initializing parameter array. (#1131) [Jie Xu]
* Fix internal error on interface arrays. (#1135) [John Stevenson]
* Fix calling sformatf to display, and elab $displays. (#1139) [Johan Bjork]
* Fix realpath compile issue on MSVC++. (#1141) [Miodrag Milanovic]
* Fix missing error on interface size mismatch. (#1143)  [Johan Bjork]
* Fix error on parameters with dotted references. (#1146) [Johan Bjork]
* Fix wreal not handling continuous assign. (#1150) [J Briquet]
* Fix nested structure parameter selects. (#1150) [J Briquet]


Verilator 3.900 2017-01-15
==========================

**Major:**

* Internal code changes for improved compatibility and performance.

**Minor:**

* Support old-style $display($time). (#467) [John Demme]
* With --bbox-unsup, suppress desassign and mixed edges. (#1120) [Galen Seitz]
* Fix parsing sensitivity with &&. (#934) [Luke Yang]
* Fix internal error on double-for loop unrolling. (#1044) [Jan Egil Ruud]
* Fix internal error on unique casez with --assert. (#1117) [Enzo Chi]
* Fix bad code when tracing array of structs. (#1122) [Andrew Bardsley]


Verilator 3.890 2016-11-25
==========================

**Minor:**

* Honor --output-split on coverage constructors. (#1098) [Johan Bjork]
* Fix various issues when making outside of the kit.
* Fix flex 2.6.2 bug. (#1103) [Sergey Kvachonok]
* Fix error on bad interface name. (#1097) [Todd Strader]
* Fix error on referencing variable in parent. (#1099) [Ian Thompson]
* Fix type parameters with low optimization. (#1101) [Stefan Wallentowitz]


Verilator 3.888 2016-10-14
==========================

**Major:**

* Support foreach. (#1078) [Xuan Guo]

**Minor:**

* Add --no-decoration to remove output comments, msg2015. [Frédéric Requin]
* If VM_PARALLEL_BUILDS=1, use OPT_FAST and OPT_SLOW. [Frédéric Requin]
  Set VM_DEFAULT_RULES=0 for old behavior.
* Add error on DPI functions > 32 bits. (#1898) [Elliot Mednick]
* Improve Verilation performance on internal strings. (#1896) [Johan Bjork]
* Improve Verilation performance on trace duplicates. (#1090) [Johan Bjork]
* Fix SystemC compiles with VPI. (#1081) [Arthur Kahlich]
* Fix error on wide numbers that represent shifts, msg1991. (#1088) [Mandy Xu]


Verilator 3.886 2016-07-30
==========================

**Minor:**

* Fix enum values of 11-16 bits wide using .next/.prev. (#1062) [Brian Flachs]
* Fix false warnings on non-power-2 enums using .next/.prev.
* Fix comparison of unpacked arrays. (#1071) [Andrew Bardsley]
* Fix compiler warning in GCC 6. [David Horton]


Verilator 3.884 2016-05-18
==========================

**Major:**

* Support parameter type. (#376) [Alan Hunter, et al]
* Support command-line -G/+pvalue param overrides. (#1045) [Stefan Wallentowitz]
* Add --l2-name option for controlling "v" naming.
* The default l2 scope name is now the same as the top-level module. (#1050)
  Use "--l2-name v" for the historical behavior.

**Minor:**

* Fix --output-split of constructors. (#1035) [Johan Bjork]
* Fix removal of empty packages, modules and cells. (#1034) [Johan Bjork]
* Fix core dump on Arch Linux/GCC 6.1.1. (#1058) [Jannis Harder]
* Fix $value$plusargs to string. (#1880) [Frédéric Requin]


Verilator 3.882 2016-03-01
==========================

**Minor:**

* Internal Verilation-time performance enhancements. (#1021) [Johan Bjork]
* Support inlining interfaces. (#1018) [Johan Bjork]
* Support SV strings to readmemh. (#1040) [Stefan Wallentowitz]
* Fix unrolling complicated for-loop bounds. (#677) [Johan Bjork]
* Fix stats file containing multiple unroll entries. (#1020) [Johan Bjork]
* Fix using short parameter names on negative params. (#1022) [Duraid Madina]
* Fix read-after-free error. (#1031) [Johan Bjork]
* Fix elaboration-time display warnings. (#1032) [Johan Bjork]
* Fix crash on very deep function trees. (#1028) [Jonathan Kimmitt]
* Fix slicing mix of big and little-endian. (#1033) [Geoff Barrett]
* Fix pattern assignment width propagation. (#1037) [Johan Bjork]


Verilator 3.880 2015-12-19
==========================

**Minor:**

* Support display %u, %v, %p, %z. (#989) [Johan Bjork]
* Fix real parameters causing bad module names. (#992) [Johan Bjork]
* Fix size-changing cast on packed struct. (#993) [Johan Bjork]
* Fix function calls on arrayed interface. (#994) [Johan Bjork]
* Fix arrayed interfaces. (#879) (#1001) [Todd Strader]
* Fix constant function assigned to packed structs. (#997) [Johan Bjork]
* Fix interface inside generate. (#998) [Johan Bjork]
* Fix $signed casts under generates. (#999) [Clifford Wolf]
* Fix genvar constant propagation. (#1003) [Johan Bjork]
* Fix parameter constant propagation from package. (#1004) [Johan Bjork]
* Fix array slicing of non-const indexes. (#1006) [Johan Bjork]
* Fix dotted generated array error. (#1005) [Jeff Bush, Johan Bjork]
* Fix error instead of warning on large concat. (#1865) [Paul Rolfe]
* Fix $bitstoreal constant propagation. (#1012) [Jonathan Kimmitt]
* Fix model restore crash. (#1013) [Jason McMullan]
* Fix arrayed instances to unpacked of same size. (#1015) [Varun Koyyalagunta]
* Fix slices of unpacked arrays with non-zero LSBs.
* Fix ternary operation with unpacked array. (#1017) [Varun Koyyalagunta].


Verilator 3.878 2015-11-01
==========================

**Major:**

* Add --vpi flag, and fix VPI linkage. (#969) [Arthur Kahlich]
* Support genvar indexes into arrayed cells. (#517)  [Todd Strader]
* Support $sformatf. (#977) [Johan Bjork]
* Support elaboration assertions. (#973) [Johan Bjork]
* Support $display with non-format arguments. (#467) [Jamey Hicks]

**Minor:**

* Add VerilatedScopeNameMap for introspection. (#966) [Todd Strader]
* Ignore %l in $display. (#983) [Todd Strader]
* Fix very long module names. (#937) [Todd Strader]
* Fix internal error on dotted refs into generates. (#958) [Jie Xu]
* Fix structure parameter constant propagation. (#968) [Todd Strader]
* Fix enum constant propagation. (#970) [Todd Strader]
* Fix mis-optimizing public DPI functions. (#963) [Wei Song]
* Fix package:scope.scope variable references.
* Fix $fwrite to constant stderr/stdout. (#961) [Wei Song]
* Fix struct.enum.name method calls. (#855) [Jonathon Donaldson]
* Fix dot indexing into arrayed inferfaces. (#978)  [Johan Bjork]
* Fix crash in commandArgsPlusMatch. (#987) [Jamie Iles]
* Fix error message on missing interface. (#985) [Todd Strader]


Verilator 3.876 2015-08-12
==========================

**Minor:**

* Add tracing_on, etc to vlt files. (#932) [Frédéric Requin]
* Support extraction of enum bits. (#951) [Jonathon Donaldson]
* Fix MinGW compiler error. (#927) (#929) [Hans Tichelaar]
* Fix .c files to be treated as .cpp. (#930) [Jonathon Donaldson]
* Fix string-to-int space conversion. (#931) [Fabrizio Ferrandi]
* Fix dpi imports inside generates. [Michael Tresidder]
* Fix rounding in trace $timescale. (#946) [Frédéric Requin]
* Fix $fopen with SV string. (#947) [Sven Stucki]
* Fix hashed error with typedef inside block. (#948) [Sven Stucki]
* Fix makefile with --coverage. (#953) [Eivind Liland]
* Fix coverage documentation. (#954) [Thomas J Whatson]
* Fix parameters with function parameter arguments. (#952) [Jie Xu]
* Fix size casts as second argument of cast item. (#950) [Jonathon Donaldson]


Verilator 3.874 2015-06-06
==========================

**Minor:**

* Add pkg-config .pc file. (#919) [Stefan Wallentowitz]
* Fix installing missing manpages. (#908) [Ahmed El-Mahmoudy]
* Fix sign extension in large localparams. (#910) [Mike Thyer]
* Fix core dump in sync-async warnings. (#911) [Sebastian Dressler]
* Fix truncation warning with -pins-bv. (#912) [Alfonso Martinez]
* Fix Cygwin uint32 compile. (#914) [Matthew Barr]
* Fix preprocessing stringified newline escapes. (#915) [Anton Rapp]
* Fix part-select in constant function. (#916) [Andrew Bardsley]
* Fix width extension on mis-width ports. (#918) [Patrick Maupin]
* Fix width propagation on sized casts. (#925) [Jonathon Donaldson]
* Fix MSVC++ compiler error. (#927) [Hans Tichelaar]


Verilator 3.872 2015-04-05
==========================

**Minor:**

* Add VerilatedVcdFile to allow real-time waveforms. (#890) [HyungKi Jeong]
* Add --clk and related optimizations. (#1840) [Jie Xu]
* Fix order of C style arrays. [Duraid Madina]
* Add --dump-treei-<srcfile>. (#894) [Jie Xu]
* Fix comma-instantiations with parameters. (#884) [Franck Jullien]
* Fix SystemC arrayed bit vectors. (#886) [David Poole]
* Fix compile error on MinGW. (#887) [HyungKi Jeong]


Verilator 3.870 2015-02-12
==========================

**Minor:**

* Suppress COMBDLY when inside always_latch. (#864) [Iztok Jeras]
* Support cast operator with expression size. (#865) [Iztok Jeras]
* Add warning on slice selection out of bounds. (#875) [Cong Van Nguyen].
* Fix member select error broke in 3.868. (#867) [Iztok Jeras]
* Fix $sccanf from string. (#866) [David Pierce]
* Fix VM_PARALLEL_BUILDS broke in 3.868. (#870) [Hiroki Honda]
* Fix non-ANSI modport instantiations. (#868) [Kevin Thompson]
* Fix UNOPTFLAT change detect on multidim arrays. (#872) [Andrew Bardsley]
* Fix slice connections of arrays to ports. (#880) [Varun Koyyalagunta]
* Fix mis-optimizing gate assignments in unopt blocks. (#881) [Mike Thyer]
* Fix sign extension of pattern members. (#882) [Iztok Jeras]
* Fix clang compile warnings.


Verilator 3.868 2014-12-20
==========================

**Major:**

* New verilator_coverage program added to replace SystemPerl's vcoverage.
* PSL support was removed, please use System Verilog assertions.
* SystemPerl mode is deprecated and now untested.

**Minor:**

* Support enum.first/name and similar methods. (#460) (#848)
* Add 'string' printing and comparisons. (#746) (#747) etc.
* Inline C functions that are used only once. (#1838) [Jie Xu]
* Fix tracing SystemC signals with structures. (#858) [Eivind Liland]
  Note that SystemC traces will no longer show the signals
  in the wrapper, they can be seen one level further down.
* Add --stats-vars. (#851) [Jeremy Bennett]
* Fix bare generates in interfaces. (#789) [Bob Newgard]
* Fix underscores in real literals. (#863) [Jonathon Donaldson]


Verilator 3.866 2014-11-15
==========================

**Minor:**

* Fix +define+A+B to define A and B to match other simulators. (#847) [Adam Krolnik]
* Add optimization of wires from arrayed cells. (#1831) [Jie Xu]
* Add optimization of operators between concats. (#1831) [Jie Xu]
* Add public enums. (#833) [Jonathon Donaldson]
* Trace_off now operates on cells. (#826) [Lane Brooks]
* Fix public parameters in unused packages. (#804) [Jonathon Donaldson]
* Fix select when partially out-of-bound. (#823) [Clifford Wolf]
* Fix generate unrolling with function call. (#830) [Steven Slatter]
* Fix cast-to-size context-determined sizing. (#828) [Geoff Barrett]
* Fix not tracing modules following primitives. (#837) [Jie Xu]
* Fix trace overflow on huge arrays. (#834) [Geoff Barrett]
* Fix quoted comment slashes in defines. (#845) [Adam Krolnik]


Verilator 3.864 2014-09-21
==========================

**Minor:**

* Support power operator with real. (#809) [Jonathon Donaldson]
* Improve verilator_profcfunc time attributions. [Jonathon Donaldson]
* Fix duplicate anonymous structures in $root. (#788) [Bob Newgard]
* Fix mis-optimization of bit-swap in wide signal. (#800) [Jie Xu]
* Fix error when tracing public parameters. (#722) [Jonathon Donaldson]
* Fix dpiGetContext in dotted scopes. (#740) [Geoff Barrett]
* Fix over-shift structure optimization error. (#803) [Jeff Bush]
* Fix optional parameter keyword in module #(). (#810) [Iztok Jeras]
* Fix $warning/$error multi-argument ordering. (#816) [Jonathon Donaldson]
* Fix clang warnings. (#818) [Iztok Jeras]
* Fix string formats under deep expressions. (#820) [Iztok Jeras]


Verilator 3.862 2014-06-10
==========================

**Minor:**

* Using command line -Wno-{WARNING} now overrides file-local lint_on.
* Add -P to suppress `line and blanks with preprocessing. (#781) [Derek Lockhart]
* Support SV 2012 package import before port list.
* Change SYMRSVDWORD to print as warning rather than error.
* Fix seg-fault with variable of parameterized interface. (#692) [Jie Xu]
* Fix false name conflict on cells in generate blocks. (#749) [Igor Lesik]
* Fix pattern assignment to basic types. (#767) [Jie Xu]
* Fix pattern assignment to conditionals. (#769) [Jie Xu]
* Fix shift corner-cases. (#765) (#766) (#768) (#772) (#774) (#776) [Clifford Wolf]
* Fix C compiler interpreting signing. (#773) [Clifford Wolf]
* Fix late constant division by zero giving X error. (#775) [Clifford Wolf]
* Fix gate primitives with arrays and non-arrayed pins.
* Fix DETECTARRAY error on packed arrays. (#770) [Jie Xu]
* Fix ENDLABEL warnings on escaped identifiers.
* Fix string corruption. (#780) [Derek Lockhart]


Verilator 3.860 2014-05-11
==========================

**Major:**

* PSL is no longer supported, please use System Verilog assertions.
* Support '{} assignment pattern on arrays. (#355)
* Support streaming operators. (#649) [Glen Gibb]
* Fix expression problems with -Wno-WIDTH. (#729) (#736) (#737) (#759)
  Where WIDTH warnings were ignored this might result in different
  warning messages and results, though it should better match the spec.
  [Clifford Wolf]

**Minor:**

* Add --no-trace-params.
* Add assertions on 'unique if'. (#725) [Jeff Bush]
* Add PINCONNECTEMPTY warning. [Holger Waechtler]
* Support parameter arrays. (#683) [Jeremy Bennett]
* Documentation fixes. (#723) [Glen Gibb]
* Support {} in always sensitivity lists. (#745) [Igor Lesik]
* Fix begin_keywords "1800+VAMS". (#1806)
* Fix tracing of package variables and real arrays.
* Fix tracing of packed arrays without --trace-structs. (#742) [Jie Xu]
* Fix missing coverage line on else-if. (#727) [Sharad Bagri]
* Fix modport function import not-found error.
* Fix power operator calculation. (#730) (#735) [Clifford Wolf]
* Fix reporting struct members as reserved words. (#741) [Chris Randall]
* Fix change detection error on unions. (#758) [Jie Xu]
* Fix -Wno-UNOPTFLAT change detection with 64-bits. (#762) [Clifford Wolf]
* Fix shift-right optimization. (#763) [Clifford Wolf]
* Fix Mac OS-X test issues. [Holger Waechtler]
* Fix C++-2011 warnings.


Verilator 3.856 2014-03-11
==========================

**Minor:**

* Support case inside. (#708) [Jan Egil Ruud]
* Add parameters into trace files. (#706) [Alex Solomatnikov]
* Fix parsing "#0 'b0". (#256)
* Fix array bound checks on real variables.
* Fix --skip-identical mis-detecting on OS-X. (#707)
* Fix missing VL_SHIFTRS_IQI with WIDTH warning. (#714) [Fabrizio Ferrandi]
* Fix signed shift right optimization. (#715) [Fabrizio Ferrandi]
* Fix internal error on "input x =" syntax error. (#716) [Lane Brooks]
* Fix slice extraction from packed array. (#717) [Jan Egil Ruud]
* Fix inside statement EQWILD error. (#718) [Jan Egil Ruud]


Verilator 3.855 2014-01-18
==========================

**Minor:**

* Support modport import. (#696) [Jeremy Bennett]
* Add --trace-structs to show struct names. (#673) [Chris Randall]
* Fix tracing of packed structs. (#705) [Jie Xu]
* Fix --lint-only with MinGW. (#1813) [HyungKi Jeong]
* Fix some delayed assignments of typedefed unpacked arrays.
* Fix wire declarations with size and not range. (#466) [Alex Solomatnikov]
* Fix parameter pin vs. normal pin error. (#704) [Alex Solomatnikov]


Verilator 3.854 2013-11-26
==========================

**Minor:**

* Add UNPACKED warning to convert unpacked structs. [Jeremy Bennett]
* Add --compiler clang to work around compiler bug. (#694) [Stefan Ludwig]
* Support vpi_get of vpiSuppressVal. (#687) [Varun Koyyalagunta]
* Support vpi_get_time. (#688) [Varun Koyyalagunta]
* Fix evaluation of chained parameter functions. (#684) [Ted Campbell]
* Fix enum value extension of '1.
* Fix multiple VPI variable callbacks. (#679) [Rich Porter]
* Fix vpi_get of vpiSize. (#680) [Rich Porter]
* Fix vpi_remove_cb inside callback. (#689) [Varun Koyyalagunta]
* Fix crash with coverage of structures. (#691) [Eivind Liland]
* Fix array assignment from const var. (#693) [Jie Xu]


Verilator 3.853 2013-09-30
==========================

**Minor:**

* Add --no-order-clock-delay to work around #613. [Charlie Brej]


Verilator 3.852 2013-09-29
==========================

**Minor:**

* Support named function and task arguments. [Chris Randall]
* Report SELRANGE warning for non-generate if. (#675) [Roland Kruse]
* Fix ordering of $fgetc. (#1808) [Frédéric Requin]
* Fix --output-split-cfunc to count internal functions. [Chris Randall]
* Fix crash on 32-bit Ubuntu. (#670) [Mark Jackson Pulver]


Verilator 3.851 2013-08-15
==========================

**Minor:**

* Fix ordering of clock enables with delayed assigns. (#613) [Jeremy Bennett]
* Fix vpi_iterate on memory words. (#655) [Rich Porter]
* Fix final duplicate declarations when non-inlined. (#661) [Charlie Brej]
* Fix interface ports with comma lists. (#1779) [Ed Lander]
* Fix parameter real conversion from integer.
* Fix clang warnings. (#668)  [Yutetsu Takatsukasa]


Verilator 3.850 2013-06-02
==========================

**Major:**

* Support interfaces and modports. (#102)  [Byron Bradley, Jeremy Bennett]

**Minor:**

* Duplicate clock gate optimization on by default. (#621)
* Fix arrayed input compile error. (#645) [Krzysztof Jankowski]
* Fix GCC version runtime changes. (#651) [Jeremy Bennett]
* Fix packed array select internal error. (#652) [Krzysztof Jankowski]


Verilator 3.847 2013-05-11
==========================

**Minor:**

* Add ALWCOMBORDER warning. [KC Buckenmaier]
* Add --pins-sc-uint and --pins-sc-biguint. (#638) [Alex Hornung]
* Support "signal[vec]++".
* Fix simulation error when inputs and MULTIDRIVEN. (#634) [Ted Campbell]
* Fix module resolution with __. (#631) [Jason McMullan]
* Fix packed array non-zero right index select crash. (#642) [Krzysztof Jankowski]
* Fix nested union crash. (#643) [Krzysztof Jankowski]


Verilator 3.846 2013-03-09
==========================

**Major:**

* IEEE 1800-2012 is now the default language. This adds 4 new keywords
  and updates the svdpi.h and vpi_user.h header files.
* Add --report-unoptflat. (#611) [Jeremy Bennett]

**Minor:**

* Add duplicate clock gate optimization. (#1772) [Varun Koyyalagunta]
  Disabled unless -OD or -O3 used, please try it as may get some
  significant speedups.
* Support pattern assignment features. (#616) (#617) (#618) [Ed Lander]
* Support bind in $unit. (#602) [Ed Lander]
* Support <number>'() sized casts. (#628) [Ed Lander]
* Fix wrong dot resolution under inlining. [Art Stamness]
* Fix DETECTARRAY on packed structures. (#610) [Jeremy Bennett]
* Fix LITENDIAN on unpacked structures. (#614) [Wai Sum Mong]
* Fix 32-bit OS VPI scan issue. (#615)  [Jeremy Bennett, Rich Porter]
* Fix opening a VerilatedVcdC file multiple times. (#1774) [Frédéric Requin]
* Fix UNOPTFLAT circular array bounds crossing. (#630) [Jie Xu]


Verilator 3.845 2013-02-04
==========================

**Minor:**

* Fix nested packed arrays and struct. (#600) [Jeremy Bennett]
  Packed arrays are now represented as a single linear vector in
  Verilated models. This may affect packed arrays that are public or
  accessed via the VPI.
* Support wires with data types. (#608) [Ed Lander]
* Support bind, to module names only. (#602) [Ed Lander]
* Support VPI product info, warning calls, etc. (#588) [Rick Porter]
* Support $left, $right and related functions. (#448) [Iztok Jeras]
* Support inside expressions.
* Define SYSTEMVERILOG, SV_COV_START and other IEEE mandated predefines.
* Fix pin width mismatch error. (#595)  [Alex Solomatnikov]
* Fix implicit one bit parameter selection. (#603)  [Jeremy Bennett]
* Fix signed/unsigned parameter misconversion. (#606)  [Jeremy Bennett]
* Fix segfault on multidimensional dotted arrays. (#607)  [Jie Xu]
* Fix per-bit array output connection error. (#414)  [Jan Egil Ruud]
* Fix package logic var compile error.
* Fix enums with X values.


Verilator 3.844 2013-01-09
==========================

**Minor:**

* Support "unsigned int" DPI import functions. (#1770)  [Alex Lee]
* Fix package resolution of parameters. (#586) [Jeremy Bennett]
* Fix non-integer vpi_get_value. (#587)  [Rich Porter]
* Fix task inlining under $display and case. (#589) (#598)  [Holger Waechtler]
* Fix package import of non-localparam parameter. (#474) (#591)  [Jeremy Bennett]
* Fix package import of package imports, partial #592. [Jeremy Bennett]
* Fix package import preventing local var. (#599)  [Jeremy Bennett]
* Fix array extraction of implicit vars. (#601)  [Joe Eiler]


Verilator 3.843 2012-12-01
==========================

**Minor:**

* Add +1364-1995ext and similar language options. (#532)  [Jeremy Bennett]
* Fix mis-optimized identical submodule subtract. (#581)  [Charlie Brej]
* Fix crash on dotted references into dead modules. (#583)  [Jeremy Bennett]
* Fix compile issues on MSVCC. (#571) (#577)  [Amir Gonnen]
* Fix --debug overriding preceding --dump-treei. (#580)  [Jeremy Bennett]


Verilator 3.842 2012-11-03
==========================

**Minor:**

* Add -x-initial-edge. (#570)  [Jeremy Bennett]
* Fix parameter pins interspersed with cells broke in 3.840. [Bernard Deadman]
* Fix large shift error on large shift constants. [David Welch]
* Fix $display mangling on GCC 4.7 and speed up. (#1765) (#373) (#574) [R Diez]
* Fix array of struct references giving false error. (#566) [Julius Baxter]
* Fix missing var access functions when no DPI. (#572) [Amir Gonnen]
* Fix name collision on unnamed blocks. (#567)  [Chandan Egbert]
* Fix name collision on task inputs. (#569)  [Chandan Egbert]


Verilator 3.841 2012-09-03
==========================

**Major:**

* Add --savable to support model save/restore. [Jeremy Bennett]

**Minor:**

* Support '{} assignment pattern on structures, part of #355.
* Fix double-deep parameter cell WIDTHs. (#541)  [Hiroki Honda]
* Fix imports under multiple instantiated cells. (#542)  [Alex Solomatnikov]
* Fix defparam in generate broke in 3.840. (#543)  [Alex Solomatnikov]
* Fix duplicate begin error broke in 3.840. (#548)  [Alex Solomatnikov]
* Fix triangle symbol resolution error broke in 3.840. (#550)  [Ted Campbell]


Verilator 3.840 2012-07-31 Beta
===============================

**Major:**

* Rewrote tristate handling; supports tri0, tri1, tristate bit selects,
  concatenates and pullup/pulldowns. (#395) (#56) (#54) (#51)
  [Alex Solomatnikov, Lane Brooks, et al]
* Support packed structures and unions. (#181)
  Note this was a major internal change that may lead to some instability.

**Minor:**

* Support tri0 and tri1. (#462)  [Alex Solomatnikov]
* Support nmos and pmos. (#488)  [Alex Solomatnikov]
* Add INITIALDLY warning on initial assignments. (#478)  [Alex Solomatnikov]
* Add PINMISSING and PINNOCONNECT lint checks.
* Add --converge-limit option.
* Fix generate operators not short circuiting. (#413)  [by Jeremy Bennett]
* Fix parameters not supported in constant functions. (#474) [Alex Solomatnikov]
* Fix duplicate warnings/errors. (#516)  [Alex Solomatnikov]
* Fix signed extending biops with WIDTH warning off. (#511) [Junji Hashimoto]
* Fix ITOD internal error on real conversions. (#491)  [Alex Solomatnikov]
* Fix input and real loosing real data type. (#501)  [Alex Solomatnikov]
* Fix imports causing symbol table error. (#490)  [Alex Solomatnikov]
* Fix newlines in radix values. (#507) [Walter Lavino]
* Fix loop error message to report line. (#513)  [Jeremy Bennett]
* Fix false UNUSED warning on file system calls.
* Fix GCC 4.7.0 compile warnings. (#530)  [Jeremy Bennett]
* Fix svdpi.h compile error on Apple OS.
* Fix compile error under git submodules. (#534) [Aurelien Francillon]


Verilator 3.833 2012-04-15
==========================

**Minor:**

* Support += and -= in standard for loops. (#463) [Alex Solomatnikov]
* Fix processing unused parameterized modules. (#469) (#470)  [Alex Solomatnikov]
* Add SELRANGE as warning instead of error. (#477) [Alex Solomatnikov]
* Add readme.pdf and internal.pdf and doxygen. (#483)  [by Jeremy Bennett]
* Fix change detections on arrays. (#364)  [John Stevenson, Alex Solomatnikov]
* Fix signed array warning. (#456) [Alex Solomatnikov]
* Fix genvar and begin under generate. (#461)  [Alex Solomatnikov]
* Fix real constant parameter functions. (#475)  [Alex Solomatnikov]
* Fix and document --gdb option. (#454) [Jeremy Bennett]
* Fix OpenSolaris compile error. [Sanjay Singh]


Verilator 3.832 2012-03-07
==========================

**Minor:**

* Fix memory delayed assignments from multiple clock domains. [Andrew Ling]
* Support arrayed SystemC I/O pins. [Christophe Joly]
* Report MULTIDRIVEN on memories set in multiple clock domains.
* Report ENDLABEL on mismatching end labels. (#450) [Iztok Jeras]
* Fix expansion of back-slashed escaped macros. (#441) [Alberto Del Rio]
* Fix inheriting real and signed type across untyped parameters.
* Fix core dump with over 100 deep UNOPTFLAT. (#432) [Joe Eiler]
* Fix false command not found warning in makefiles. [Ruben Diez]
* Fix hang when functions inside begin block. [David Welch]
* Fix hang on recursive substitution `defines. (#443) [Alex Solomatnikov]


Verilator 3.831 2012-01-20
==========================

**Major:**

* Support SystemC 2.3.0 prerelease. This requires setting the new
  SYSTEMC_INCLUDE and SYSTEMC_LIBDIR variables in place of now
  deprecated SYSTEMC and SYSTEMC_ARCH. [Iztok Jeras]

**Minor:**

* Suppress VARHIDDEN on dpi import arguments. [Ruben Diez]
* Support "generate for (genvar i=0; ...". [David Kravitz]
* Fix dpi exports with > 32 bit but < 64 bit args. (#423) [Chandan Egbert]
* Fix array of instantiations with sub-range output. (#414) [Jeremy Bennett]
* Fix BLKSEQ warnings on variables declared inside always. [Ruben Diez]


Verilator 3.830 2011-11-27
==========================

**Major:**

* With "--language VAMS" support a touch of Verilog AMS. [Holger Waechtler]

**Minor:**

* Add sc_bv attribute to force bit vectors. (#402) [by Stefan Wallentowitz]
* Search for user -y paths before default current directory. [Ruben Diez]
* Support constants in sensitivity lists. (#412)  [Jeremy Bennett]
* Support $system. [Ruben Diez]
* Support $sscanf with %g. [Holger Waechtler]
* Indicate 'exiting due to errors' if errors, not warnings. [Ruben Diez]
* Fix bad result with if-else-return optimization. (#420) [Alex Solomatnikov]
* Fix reporting not found modules if generate-off. (#403) [Jeremy Bennett]
* Fix $display with %d following %g. [Holger Waechtler]


Verilator 3.824 2011-10-25
==========================

**Minor:**

* Fix "always @ (* )". (#403) (#404)  [Walter Lavino]
* Add ASSIGNIN as suppressible error. [Jeremy Bennett]
* Fix 3.823 constructor core dump on Debian. (#401)  [Ahmed El-Mahmoudy]


Verilator 3.823 2011-10-20
==========================

**Minor:**

* Support $ceil, $floor, etc. [Alex Solomatnikov]
* Add configure options for cc warnings and extended tests. [Ruben Diez]
* Add -Wall reporting ASSIGNDLY on assignment delays. [Ruben Diez]
* Fix UNDRIVEN warnings inside DPI import functions. [Ruben Diez]
* Fix --help output to go to stderr, not stdout. (#397)  [Ruben Diez]
* Fix DPI import output of 64 bits. (#398) [Mike Denio]
* Fix DPI import false BLKSEQ warnings. [Alex Solomatnikov]
* Fix MSVC compile warning with trunc/round. (#394) [Amir Gonnen]
* Fix autoconf and Makefile warnings. (#396)  [Ruben Diez]


Verilator 3.821 2011-09-14
==========================

**Minor:**

* Fix PowerPC runtime error. (#288) [Ahmed El-Mahmoudy]
* Fix internal error on integer casts. (#374) [Chandan Egbert]


Verilator 3.820 2011-07-28
==========================

**Minor:**

* Support 'real' numbers and related functions.
* Support 'const' variables in limited cases; similar to enums. [Alex Solomatnikov]
* Support disable for loop escapes.
* Support $fopen and I/O with integer instead of `verilator_file_descriptor.
* Support coverage in -cc and -sc output modes. [John Li]
  Note this requires SystemPerl 1.338 or newer.
* Use 'vluint64_t' for SystemC instead of (same sized) 'uint64' for MSVC++.
* Fix vpi_register_cb using bad s_cb_data. (#370)  [by Thomas Watts]
* Fix $display missing leading zeros in %0d. (#367)  [Alex Solomatnikov]


Verilator 3.813 2011-06-28
==========================

**Minor:**

* Support bit vectors > 64 bits wide in DPI import and exports.
* Fix out of memory on slice syntax error. (#354)  [Alex Solomatnikov]
* Fix error on enum references to other packages. (#339) [Alex Solomatnikov]
* Fix DPI undeclared svBitVecVal compile error. (#346) [Chandan Egbert]
* Fix DPI bit vector compile errors. (#347) (#359) [Chandan Egbert]
* Fix CDCRSTLOGIC report showing endpoint flops without resets.
* Fix compiler warnings on SPARC. (#288) [Ahmed El-Mahmoudy]


Verilator 3.812 2011-04-06
==========================

**Minor:**

* Add --trace-max-width and --trace-max-array. (#319)  [Alex Solomatnikov]
* Add --Wno-fatal to turn off abort on warnings. [by Stefan Wallentowitz]
* Support ${...} and $(...) env vars in .vc files. [by Stefan Wallentowitz]
* Support $bits(data_type). (#327)  [Alex Solomatnikov]
* Support loop unrolling on width mismatches. (#333) [Joe Eiler]
* Support simple cast operators. (#335)  [Alex Solomatnikov]
* Accelerate bit-selected inversions.
* Add error on circular parameter definitions. (#329) [Alex Solomatnikov]
* Fix concatenates and vectored bufif1. (#326) [Iztok Jeras]


Verilator 3.811 2011-02-14
==========================

**Minor:**

* Report error on duplicated or empty pins. (#321)  [Christian Leber]
* Report error on function call output tied to constant. [Bernard Deadman]
* Throw UNUSED/UNDRIVEN only once per net in a parameterized module.
* Fix internal error on functions called as SV tasks. [Bernard Deadman]
* Fix internal error on non-inlined inout pins. [Jeff Winston]
* Fix false BLKSEQ on non-unrolled for loop indexes. [Jeff Winston]
* Fix block comment not separating identifiers. (#311) [Gene Sullivan]
* Fix warnings to point to lowest net usage, not upper level ports.
* Fix error on constants connected to outputs. (#323)  [Christian Leber]


Verilator 3.810 2011-01-03
==========================

**Major:**

* Add limited support for VPI access to public signals, see docs.
* Add -F option to read relative option files. (#297) [Neil Hamilton]
* Support ++,--,+= etc as standalone statements. [Alex Solomatnikov]
* Add -Wall, -Wwarn-style, -Wno-style to enable code style warnings
  that have been added to this release, and disabled by default:
* With --Wall, add BLKSEQ warning on blocking assignments in seq blocks.
* With --Wall, add DECLFILENAME warning on modules not matching filename.
* With --Wall, add DEFPARAM warning on deprecated defparam statements.
* With --Wall, add IFDEPTH warning on deep if statements.
* With --Wall, add INCABSPATH warning on `include with absolute paths.
* With --Wall, add SYNCASYNCNET warning on mixed sync/async reset nets.
* With --Wall, add UNDRIVEN warning on undriven nets.
* With --Wall, add UNUSED warning on unused nets.

**Minor:**

* When running with VERILATOR_ROOT, optionally find binaries under bin.
* Suppress WIDTH warnings when adding/subtracting 1'b1.
* The VARHIDDEN warning is now disabled by default, use -Wall to enable.


Verilator 3.805 2010-11-02
==========================

**Minor:**

* Add warning when directory contains spaces. (#1705)  [Salman Sheikh]
* Fix wrong filename on include file errors. (#289) [Brad Parker]
* Fix segfault on SystemVerilog "output wire foo=0". (#291)  [Joshua Wise]
* Fix DPI export name not found. (#1703)  [Terry Chen]


Verilator 3.804 2010-09-20
==========================

**Minor:**

* Support tracing/coverage of underscore signals. (#280)  [by Jason McMullan]
* Increase define recursions before error. [Paul Liu]
* On core dump, print debug suggestions.
* Fix preprocessor \`\` of existing base define. (#283)  [Usha Priyadharshini]


Verilator 3.803 2010-07-10
==========================

**Minor:**

* Fix preprocessor preservation of newlines across macro substitutions.
* Fix preprocessor stringification of nested macros.
* Fix some constant parameter functions causing crash. (#253) [Nick Bowler]
* Fix do {...} while() not requiring final semicolon.


Verilator 3.802 2010-05-01
==========================

**Minor:**

* Support runtime access to public signal names.
* Add /*verilator public_flat_rw*/ for timing-specific public access.
* Fix word size to match uint64_t on -m64 systems. (#238) [Joe Eiler]
* Improve error handling on slices of arrays. (#226) [by Byron Bradley]
* Report errors when extra underscores used in meta-comments.
* Fix bit reductions on multi-packed dimensions. (#227) [by Byron Bradley]
* Fix removing $fscanf if assigned to unused var. (#248) [Ashutosh Das]
* Fix "make install" with configure outside srcdir. [Stefan Wallentowitz]
* Fix loop unroller out of memory; change --unroll-stmts. [Ashutosh Das]
* Fix trace files with empty modules crashing some viewers.
* Fix parsing single files > 2GB. [Jeffrey Short]
* Fix installing data files as non-executable. (#168) [by Ahmed El-Mahmoudy]


Verilator 3.801 2010-03-17
==========================

**Minor:**

* Support "break", "continue", "return".
* Support "`default_nettype none|wire". [Dominic Plunkett]
* Skip SystemC tests if not installed. [Iztok Jeras]
* Fix clock-gates with non-AND complex logic. (#220) [Ashutosh Das]
* Fix flushing VCD buffers on $stop. [Ashutosh Das]
* Fix Mac OS-X compile issues. (#217)  [Joshua Wise, Trevor Williams]
* Fix make uninstall. (#216)  [Iztok Jeras]
* Fix parameterized defines with empty arguments.


Verilator 3.800 2010-02-07
==========================

**Major application visible changes:**

* SystemPerl is no longer required for tracing.
  Applications must use VerilatedVcdC class in place of SpTraceVcdC.
* SystemVerilog 1800-2009 is now the default language.
  Thus "global" etc are now keywords. See the --language option.

**Major new features:**

* Support SystemVerilog types "byte", "chandle", "int", "longint",
  "shortint", "time", "var" and "void" in variables and functions.
* Support "program", "package", "import" and $unit.
* Support typedef and enum. [by Donal Casey]
* Support direct programming interface (DPI) "import" and "export".
  Includes an extension to map user $system PLI calls to the DPI.
* Support assignments of multidimensional slices. (#170) [by Byron Bradley]
* Support multidimensional inputs/outputs. (#171) [by Byron Bradley]
* Support "reg [1:0][1:0][1:0]" and "reg x [3][2]". (#176)  [Byron Bradley]
* Support declarations in loop initializers. (#172)  [by Byron Bradley]
* Support $test$plusargs and $value$plusargs, but see the docs!
* Support $sformat and $swrite.
* Support 1800-2009 define defaults and `undefineall.
* Add -CFLAGS, -LDFLAGS, <file>.a, <file>.o, and <file>.so options.
* Speed compiles by avoiding including the STL iostream header.
  Application programs may need to include it themselves to avoid errors.
* Add experimental clock domain crossing checks.
* Add experimental --pipe-filter to filter all Verilog input.
* Add experimental config files to filter warnings outside of the source.
* Add VARHIDDEN warning when signal name hides module name.
* Support optional cell parenthesis. (#179) [by Byron Bradley]
* Support for-loop i++, ++i, i--, --i. (#175) [by Byron Bradley]
* Support 1800-2009 /*comments*/ in define values.
* Add Makefile VM_GLOBAL_FAST, listing objects needed to link executables.
* Add --bbox-unsup option to black-box unsupported UDP tables.
* Add -Wno-MODDUP option to allow duplicate modules.

**Bug fixes:**

* Fix implicit variable issues. (#196) (#201) [Byron Bradley]
* Fix 'for' variable typing. (#205)  [by Byron Bradley]
* Fix tracing with --pins-bv 1. (#195)  [Michael S]
* Fix MSVC++ 2008 compile issues. (#209)  [Amir Gonnen]
* Fix MinGW compilation. (#184) (#214) [by Shankar Giri, Amir Gonnen]
* Fix Cygwin 1.7.x compiler error with uint32_t. (#204)  [Ivan Djordjevic]
* Fix `define argument mis-replacing system task of same name. (#191)
* Fix Verilator core dump on wide integer divides. (#178) [Byron Bradley]
* Fix lint_off/lint_on meta comments on same line as warning.


Verilator 3.720 2009-10-26
==========================

**Major:**

* Support little endian bit vectors ("reg [0:2] x;").
* Support division and modulus of > 64 bit vectors. [Gary Thomas]

**Minor:**

* Fix writing to out-of-bounds arrays writing element 0.
* Fix core dump with SystemVerilog var declarations under unnamed begins.
* Fix VCD files showing internal flattened hierarchy, broke in 3.714.
* Fix cell port connection to unsized integer causing false width warning.
* Fix erroring on strings with backslashed newlines. (#168) [Pete Nixon]


Verilator 3.714 2009-09-18
==========================

**Major:**

* Add --bbox-sys option to blackbox $system calls.

**Minor:**

* Support generate for var++, var--, ++var, --var.
* Improved warning when "do" used as identifier.
* Don't require SYSTEMPERL_INCLUDE if SYSTEMPERL/src exists. [Gary Thomas]
* Fix deep defines causing flex scanner overflows. [Brad Dobbie]
* Fix preprocessing commas in deep parameterized macros. [Brad Dobbie]
* Fix tracing escaped dotted identifiers. (#107)
* Fix $display with uppercase %M.
* Fix --error-limit option being ignored.


Verilator 3.713 2009-08-04
==========================

**Minor:**

* Support constant function calls for parameters. [many!]
* Support SystemVerilog "logic". (#101)  [by Alex Duller]
* Name SYMRSVDWORD error, and allow disabling it. (#103) [Gary Thomas]
* Fix escaped preprocessor identifiers. (#106) [Nimrod Gileadi]


Verilator 3.712 2009-07-14
==========================

**Major:**

* Patching SystemC is no longer required to trace sc_bvs.

**Minor:**

* Add verilator --pins-uint8 option to use sc_in<uint8_t/uint16_t>.
* Add verilator -V option, to show verbose version.
* Add BLKLOOPINIT error code, and describe --unroll-count. [Jeff Winston]
* Support zero-width constants in concatenations. [Jeff Winston]
* On WIDTH warnings, show variable name causing error. [Jeff Winston]


Verilator 3.711 2009-06-23
==========================

**Minor:**

* Support decimal constants of arbitrary widths. [Mark Marshall]
* Fix error on case statement with all duplicate items. (#99) [Gary Thomas]
* Fix segfault on unrolling for's with bad inits. (#90) [Andreas Olofsson]
* Fix tristates causing "Assigned pin is neither...". [by Lane Brooks]
* Fix compiler errors under Fedora release candidate 11. [Chitlesh Goorah]


Verilator 3.710 2009-05-19
==========================

**Major:**

* Verilator is now licensed under LGPL v3 and/or Artistic v2.0.

**Minor:**

* `__FILE__ now expands to a string, per draft SystemVerilog 2010(ish).
* The front end parser has been re-factored to enable more SV parsing.
  Code should parse the same, but minor parsing bugs may pop up.
* Verilator_includer is no longer installed twice. (#48)  [Lane Brooks]
* Fix escaped identifiers with '.' causing conflicts. (#83)  [J Baxter]
* Fix define formal arguments that contain newlines. (#84) [David A]


Verilator 3.703 2009-05-02
==========================

**Minor:**

* Fix $clog2 calculation error with powers-of-2. (#81) [Patricio Kaplan]
* Fix error with tasks that have output first. (#78)  [Andrea Foletto]
* Fix "cloning" error with -y/--top-module. (#76) [Dimitris Nalbantis]
* Fix segfault with error on bad --top-module. (#79) [Dimitris Nalbantis]
* Fix "redefining I" error with complex includes. [Duraid Madina]
* Fix GCC 4.3.2 compile warnings.


Verilator 3.702 2009-03-28
==========================

**Minor:**

* Add --pins-bv option to use sc_bv for all ports. [Brian Small]
* Add SYSTEMPERL_INCLUDE envvar to assist RPM builds. [Chitlesh Goorah]
* Report errors when duplicate labels are used. (#72)  [Vasu Kandadi]
* Fix the SC_MODULE name() to not include __PVT__. [Bob Fredieu]


Verilator 3.701 2009-02-26
==========================

**Minor:**

* Support repeat and forever statements. [Jeremy Bennett]
* Add --debugi-<srcfile> option, for internal debugging. [Dennis Muhlestein]
* Fix compile issues with GCC 4.3. (#47)  [Lane Brooks]
* Fix VL_RANDom to better randomize bits. [Art Stamness]
* Fix error messages to consistently go to stderr. [Jeremy Bennett]
* Fix left associativity for ?: operators.


Verilator 3.700 2009-01-08
==========================

**Major:**

* Support limited tristate inouts. Written by Lane Brooks,
  under support by Ubixum Inc. This allows common pad ring and
  tristate-mux structures to be Verilated. See the documentation for
  more information on supported constructs.
* Add --coverage_toggle for toggle coverage analysis.
  Running coverage now requires SystemPerl 1.301 or newer.
* Add coverage_on/_off metacomments to bracket coverage regions.

**Minor:**

* Support posedge of bit-selected signals. (#45) [Rodney Sinclair]
* Optimize two-level shift and and/or trees, +23% on one test.
* Line coverage now aggregates by hierarchy automatically.
  Previously this would be done inside SystemPerl, which was slower.
* Minor performance improvements of Verilator compiler runtime.
* Coverage of each parameterized module is counted separately. [Bob Fredieu]
* Fix creating parameterized modules when no parameter values are changed.
* Fix certain generate-if cells causing "clone" error. [Stephane Laurent]
* Fix line coverage of public functions. [Soon Koh]
* Fix SystemC 2.2 deprecated warnings about sensitive() and sc_start().
* Fix arrayed variables under function not compiling. (#44) [Ralf Karge]
* Fix --output-split-cfuncs to also split trace code. [Niranjan Prabhu]
* Fix 'bad select range' warning missing some cases. (#43) [Lane Brooks]
* Fix internal signal names containing control characters (broke in 3.680).
* Fix compile error on Ubuntu 8.10. [Christopher Boumenot]
* Fix internal error on "output x; reg x = y;".
* Fix wrong result for read of delayed FSM signal. (#46) [Rodney Sinclair]


Verilator 3.681 2008-11-12
==========================

**Minor:**

* Support SystemVerilog unique and priority case.
* Include Verilog file's directory name in coverage reports.
* Fix 'for' under 'generate-for' causing error. (#38) [Rafael Shirakawa]
* Fix coverage hierarchy being backwards with inlining. [Vasu Arasanipalai]
* Fix GCC 4.3 compile error. (#35)  [Lane Brooks]
* Fix MSVC compile error. (#42)  [John Stroebel]


Verilator 3.680 2008-10-08
==========================

**Major:**

* Support negative bit indexes. [Stephane Laurent]
  Tracing negative indexes requires latest Verilog-Perl and SystemPerl.

**Minor:**

* Suppress width warnings between constant strings and wider vectors.
  [Rodney Sinclair]
* Ignore SystemVerilog timeunit and timeprecision.
* Expand environment variables in -f input files. [Lawrence Butcher]
* Report error if port declaration is missing. (#32) [Guy-Armand Kamendje]
* Fix genvars causing link error when using --public. [Chris Candler]


Verilator 3.671 2008-09-19
==========================

**Major:**

* SystemC uint64_t pins are now the default instead of sc_bv<64>.
  Use --no-pins64 for backward compatibility.
* Support SystemVerilog "cover property" statements.

**Minor:**

* When warnings are disabled on signals that are flattened out, disable
  the warnings on the signal(s) that replace it.
* Add by-design and by-module subtotals to verilator_profcfunc.
* Add IMPERFECTSCH warning, disabled by default.
* Support coverage under SystemPerl 1.285 and newer.
* Support arbitrary characters in identifiers. [Stephane Laurent]
* Fix extra evaluation of pure combo blocks in SystemC output.
* Fix stack overflow on large ? : trees. [John Sanguinetti]


Verilator 3.670 2008-07-23
==========================

**Major:**

* Add --x-assign=fast option, and make it the default.
  This chooses performance over reset debugging. See the manual.
* Add --autoflush, for flushing streams after $display. [Steve Tong]
* Add CASEWITHX lint warning and if disabled fix handling of casez with Xs.

**Minor:**

* Add $feof, $fgetc, $fgets, $fflush, $fscanf, $sscanf. [Holger Waechtler]
* Add $stime. [Holger Waechtler]
* Add $random.
* Add --Wfuture-, for improving forward compatibility.
* Add WIDTH warning to $fopen etc file descriptors.
* Fix verilator_includer not being installed properly. [Holger Waechtler]
* Fix IMPURE errors due to X-assignment temporary variables. [Steve Tong]
* Fix "lvalue" errors with public functions. (#25)  [CY Wang]


Verilator 3.665 2008-06-25
==========================

**Minor:**

* Ignore "// verilator" comments alone on endif lines. [Rodney Sinclair]
* "Make install" now installs verilator_includer and verilator_profcfunc.
* Fix tracing missing changes on undriven public wires. [Rodney Sinclair]
* Fix syntax error when "`include `defname" is ifdefed. [John Dickol]
* Fix error when macro call has commas in concatenate. [John Dickol]
* Fix compile errors under Fedora 9, GCC 4.3.0. [by Jeremy Bennett]
* Fix Makefile to find headers/libraries under prefix. [by Holger Waechtler]


Verilator 3.664 2008-05-08
==========================

**Minor:**

* Fix missing file in kit.


Verilator 3.663 2008-05-07
==========================

**Minor:**

* Add DESTDIR to Makefiles to assist RPM construction. [Gunter Dannoritzer]
* Fix compiler warnings under GCC 4.2.1.
* Fix preprocessor `else after series of `elsif. [Mark Nodine]
* Fix parameterized defines calling define with comma. [Joshua Wise]
* Fix comma separated list of primitives. [by Bryan Brady]


Verilator 3.662 2008-04-25
==========================

**Minor:**

* Add Verilog 2005 $clog2() function.
  This is useful in calculating bus-widths from parameters.
* Support C-style comments in -f option files. [Stefan Thiede]
* Add error message when modules have duplicate names. [Stefan Thiede]
* Support defines terminated in EOF, though against spec. [Stefan Thiede]
* Support optional argument to $finish and $stop. [by Stefan Thiede]
* Support ranges on gate primitive instantiations. [Stefan Thiede]
* Ignore old standard(ish) Verilog-XL defines. [by Stefan Thiede]
* Fix "always @ ((a) or (b))" syntax error. [by Niranjan Prabhu]
* Fix "output reg name=expr;" syntax error. [Martin Scharrer]
* Fix multiple .v files being read in random order. [Stefan Thiede]
* Fix internal error when params get non-constants. [Johan Wouters]
* Fix bug introduced in 3.661 with parameterized defines.


Verilator 3.661 2008-04-04
==========================

**Major:**

* The --enable-defenv configure option added in 3.660 is now the default.
  This hard-codes a default for VERILATOR_ROOT etc in the executables.
* Add --language option for supporting older code. [Stefan Thiede]
* Add --top-module option to select between multiple tops. [Stefan Thiede]

**Minor:**

* Unsized concatenates now give WIDTHCONCAT warnings. [Jonathan Kimmitt]
  Previously they threw fatal errors, which in most cases is correct
  according to spec, but can be incorrect in presence of parameter values.
* Support functions with "input integer". [Johan Wouters]
* Ignore delays attached to gate UDPs. [Stefan Thiede]
* Fix SystemVerilog parameterized defines with \`\` expansion,
  and fix extra whitespace inserted on substitution. [Vladimir Matveyenko]
* Fix no-module include files on command line. [Stefan Thiede]
* Fix dropping of backslash quoted-quote at end of $display.
* Fix task output pin connected to non-variables. [Jonathan Kimmitt]
* Fix missing test_v in install datadir. [Holger Waechtler]
* Fix internal error after MSB < LSB error reported to user. [Stefan Thiede]


Verilator 3.660 2008-03-23
==========================

**Minor:**

* Support hard-coding VERILATOR_ROOT etc in the executables,
  to enable easier use of Verilator RPMs. [Gunter Dannoritzer]
* Allow multiple .v files on command line. [Stefan Thiede]
* Convert re-defining macro error to warning. [Stefan Thiede]
* Add --error-limit option. [Stefan Thiede]
* Allow __ in cell names by quoting them in C. [Stefan Thiede]
* Fix genvar to be signed, so "< 0" works properly. [Niranjan Prabhu]
* Fix assignments to inputs inside functions/tasks. [Patricio Kaplan]
* Fix definitions in main file.v, referenced in library. [Stefan Thiede]
* Fix undefined assigns to be implicit warnings. [Stefan Thiede]


Verilator 3.658 2008-02-25
==========================

**Minor:**

* Fix unistd compile error in 3.657. [Patricio Kaplan, Jonathan Kimmitt]


Verilator 3.657 2008-02-20
==========================

**Minor:**

* Fix assignments of {a,b,c} = {c,b,a}. [Jonathan Kimmitt]
* Fix Perl warning with --lint-only. [by Ding Xiaoliang]
* Fix to avoid creating obj_dir with --lint-only. [Ding Xiaoliang]
* Fix parsing of always @(*). [Patricio Kaplan]


Verilator 3.656 2008-01-18
==========================

**Minor:**

* Wide VL_CONST_W_#X functions are now made automatically. [Bernard Deadman]
  In such cases, a new {prefix}__Inlines.h file will be built and included.
* Fix sign error when extracting from signed memory. [Peter Debacker]
* Fix tracing of SystemC w/o SystemPerl. [Bernard Deadman, Johan Wouters]


Verilator 3.655 2007-11-27
==========================

**Minor:**

* Support "#delay <statement>;" with associated STMTDLY warning.
* Fix generate for loops with constant zero conditions. [Rodney Sinclair]
* Fix divide-by-zero errors in constant propagator. [Rodney Sinclair]
* Fix wrong result with obscure signed-shift underneath a "? :".
* Fix many internal memory leaks, and added leak detector.


Verilator 3.654 2007-10-18
==========================

**Minor:**

* Don't exit early if many warnings but no errors are found. [Stan Mayer]
* Fix parsing module #(parameter x,y) declarations. [Oleg Rodionov]
* Fix parsing system functions with empty parens. [Oleg Rodionov]


Verilator 3.653 2007-08-01
==========================

**Minor:**

* Support SystemVerilog ==? and !=? operators.
* Fix SC_LIBS missing from generated makefiles. [Ding Xiaoliang]


Verilator 3.652 2007-06-21
==========================

**Minor:**

* Report as many warning types as possible before exiting.
* Support V2K portlists with "input a,b,...". [Mark Nodine]
* Support V2K function/task argument lists.
* Optimize constant $display arguments.
* Fix preprocessor dropping some `line directives. [Mark Nodine]


Verilator 3.651 2007-05-22
==========================

**Major:**

* Add verilator_profcfunc utility. [Gene Weber]

**Minor:**

* Treat modules within `celldefine and `endcelldefine as if in library.
* Support functions which return integers. [Mark Nodine]
* Warn if flex is not installed. [Ralf Karge]
* Ignore `protect and `endprotect.
* Fix empty case/endcase blocks.


Verilator 3.650 2007-04-20
==========================

**Major:**

* Add --compiler msvc option. This is now required when Verilated code
  is to be run through MSVC++. This also enables fixing MSVC++ error
  C1061, blocks nested too deeply. [Ralf Karge]
* Add --lint-only option, to lint without creating other output.

**Minor:**

* Add /*verilator lint_save*/ and /*verilator lint_restore*/ to allow
  friendly control over re-enabling lint messages. [Gerald Williams]
* Support SystemVerilog .name and .* interconnect.
* Support while and do-while loops.
* Use $(LINK) instead of $(CXX) for Makefile link rules. [Gerald Williams]
* Add USER_CPPFLAGS and USER_LDFLAGS to Makefiles. [Gerald Williams]
* Fix compile errors under Windows MINGW compiler. [Gerald Williams]
* Fix dotted bit reference to local memory. [Eugene Weber]
* Fix 3.640 `verilog forcing IEEE 1364-1995 only. [David Hewson]


Verilator 3.640 2007-03-12
==========================

**Minor:**

* Support Verilog 2005 `begin_keywords and `end_keywords.
* Updated list of SystemVerilog keywords to correspond to IEEE 1800-2005.
* Add /*verilator public_flat*/. [Eugene Weber]
* Try all +libext's in the exact order given. [Michael Shinkarovsky]
* Fix elimination of public signals assigned to constants. [Eugene Weber]
* Fix internal error when public for loop has empty body. [David Addison]
* Fix "Loops detected" assertion when model exceeds 4GB. [David Hewson]
* Fix display %m names inside named blocks.


Verilator 3.633 2007-02-07
==========================

**Minor:**

* Add --trace-depth option for minimizing VCD file size. [Emerson Suguimoto]
* With VL_DEBUG, show wires causing convergence errors. [Mike Shinkarovsky]
* Fix isolate_assignments when many signals per always. [Mike Shinkarovsky]
* Fix isolate_assignments across task/func temporaries. [Mike Shinkarovsky]
* Fix $display's with array select followed by wide AND. [David Hewson]


Verilator 3.632 2007-01-17
==========================

**Minor:**

* Add /*verilator isolate_assignments*/ attribute. [Mike Shinkarovsky]


Verilator 3.631 2007-01-02
==========================

**Major:**

* Support standard NAME[#] for cells created by arraying or generate for.
  This replaces the non-standard name__# syntax used in earlier versions.

**Minor:**

* Fix again dotted references into generate cells. [David Hewson]
  Verilator no longer accepts duplicated variables inside unique
  generate blocks as this is illegal according to the specification.
* Fix $readmem* with filenames < 8 characters. [Emerson Suguimoto]


Verilator 3.630 2006-12-19
==========================

**Major:**

* Support $readmemb and $readmemh. [Eugene Weber, Arthur Kahlich]

**Minor:**

* When dotted signal lookup fails, help the user by showing known scopes.
* Fix to reduce depth of priority encoded case statements. [Eugene Weber]
* Fix configure and compiling under Solaris. [Bob Farrell]
* Fix dotted references inside generated cells. [David Hewson]
* Fix missed split optimization points underneath other re-split blocks.


Verilator 3.623 2006-12-05
==========================

**Major:**

* Add --output-split-cfuncs for accelerating GCC compile. [Eugene Weber]

**Minor:**

* Add M32 make variable to support -m32 compiles. [Eugene Weber]
* Fix $signed mis-extending when input has a WIDTH violation. [Eugene Weber]


Verilator 3.622 2006-10-17 Stable
=================================

**Minor:**

* Fix --skip-identical without --debug, broken in 3.621. [Andy Meier]


Verilator 3.621 2006-10-11 Beta
===============================

**Major:**

* Add /*verilator no_inline_task*/ to prevent over-expansion. [Eugene Weber]

**Minor:**

* Public functions now allow > 64 bit arguments.
* Remove .vpp intermediate files when not under --debug.
* Fix link error when using --exe with --trace. [Eugene Weber]
* Fix mis-optimization of wide concats with constants.
* Fix core dump on printing error when not under --debug. [Allan Cochrane]


Verilator 3.620 2006-10-04 Stable
=================================

**Minor:**

* Support simple inout task ports. [Eugene Weber]
* Allow overriding Perl, Flex and Bison versions. [by Robert Farrell]
* Optimize variables set to constants within basic blocks for ~3%.
* Default make no longer makes the docs; if you edit the documentation.
  sources, run "make info" to get them.
* Optimize additional Boolean identities (a|a = a, etc.)
* Fix coredump when dotted cross-ref inside task call. [Eugene Weber]
* Fix dotted variables in always sensitivity lists. [Allan Cochrane]


Verilator 3.610 2006-09-20 Stable
=================================

**Minor:**

* Verilator now works under DJGPP (Pentium GCC). [John Stroebel]
* Add default define for VL_PRINTF. [John Stroebel]
* Removed coverage request variable; see Coverage limitations in docs.
* Fix DOS carriage returns in multiline defines. [Ralf Karge]
* Fix printf format warnings on 64-bit linux.


Verilator 3.602 2006-09-11 Stable
=================================

**Minor:**

* Fix function references under top inlined module. [David Hewson]


Verilator 3.601 2006-09-06 Beta
===============================

**Major:**

* Add --inhibit-sim flag for environments using old __Vm_inhibitSim.
* Add `systemc_dtor for destructor extensions. [Allan Cochrane]
* Add -MP to make phony dependencies, ala GCC's.

**Minor:**

* Changed how internal functions are invoked to reduce aliasing.
  Useful when using GCC's -O2 or -fstrict-aliasing, to gain another ~4%.
* Declare optimized lookup tables as 'static', to reduce D-Cache miss rate.
* Fix memory leak when destroying modules. [John Stroebel]
* Fix coredump when unused modules have unused cells. [David Hewson]
* Fix 3.600 internal error with arrayed instances. [David Hewson]
* Fix 3.600 internal error with non-unrolled function loops. [David Hewson]
* Fix $display %m name not matching Verilog name inside SystemC modules.


Verilator 3.600 2006-08-28 Beta
===============================

**Major:**

* Support dotted cross-hierarchy variable and task references.

**Minor:**

* Lint for x's in generate case statements.
* Fix line numbers being off by one when first file starts with newline.
* Fix naming of generate for blocks to prevent non-inline name conflict.
* Fix redundant statements remaining after table optimization.


Verilator 3.542 2006-08-11 Stable
=================================

**Minor:**

* vl_finish and vl_fatal now print via VL_PRINTF rather then cerr/cout.
* Fix extraneous UNSIGNED warning when comparing genvars. [David Hewson]
* Fix extra white space in $display %c. [by David Addison]
* Fix missing VL_CONST_W_24X macro. [Bernard Deadman]


Verilator 3.541 2006-07-05 Beta
===============================

**Minor:**

* Add warning on changeDetect to arrayed structures. [David Hewson]
* Fix "// verilator lint_on" not re-enabling warnings. [David Hewson]
* Fix 3.540's multiple memory assignments to same block. [David Hewson]
* Fix non-zero start number for arrayed instantiations. [Jae Hossell]
* Fix GCC 4.0 header file warnings.


Verilator 3.540 2006-06-27 Beta
===============================

**Minor:**

* Optimize combo assignments that are used only once, ~5-25% faster.
* Optimize delayed assignments to memories inside loops, ~0-5% faster.
* Fix mis-width warning on bit selects of memories. [David Hewson]
* Fix mis-width warning on dead generate-if branches. [Jae Hossell]


Verilator 3.533 2006-06-05 Stable
=================================

**Minor:**

* Add PDF user manual, verilator.pdf.
* Fix delayed bit-selected arrayed assignments. [David Hewson]
* Fix execution path to Perl. [Shanshan Xu]
* Fix Bison compile errors in verilog.y. [by Ben Jackson]


Verilator 3.531 2006-05-10 Stable
=================================

**Minor:**

* Support $c routines which return 64 bit values.
* Fix `include `DEFINE.
* Fix Verilator core dump when have empty public function. [David.Hewson]


Verilator 3.530 2006-04-24 Stable
=================================

**Major:**

* $time is now 64 bits. The macro VL_TIME_I is now VL_TIME_Q, but calls
  the same sc_time_stamp() function to get the current time.


Verilator 3.523 2006-03-06 Stable
=================================

**Minor:**

* Fix error line numbers being off due to multi-line defines. [Mat Zeno]
* Fix GCC sign extending (uint64_t)(a<b). [David Hewson]
* Fix `systemc_imp_header "undefined macro" error.


Verilator 3.522 2006-02-23 Beta
===============================

**Minor:**

* Add UNUSED error message, for forward compatibility.


Verilator 3.521 2006-02-14 Beta
===============================

**Major:**

* Create new --coverage-line and --coverage-user options. [Peter Holmes]

**Minor:**

* Add SystemVerilog 'x,'z,'0,'1, and new string literals.
* Fix public module's parent still getting inlined.


Verilator 3.520 2006-01-14 Stable
=================================

**Major:**

* Support $fopen, $fclose, $fwrite, $fdisplay.
  See documentation, as the file descriptors differ from the standard.


Verilator 3.510 2005-12-17 Stable
=================================

**Major:**

* Improve trace-on performance on large multi-clock designs by 2x or more.
  This adds a small ~2% performance penalty if traces are compiled in,
  but not turned on. For best non-tracing performance, do not use --trace.

**Minor:**

* Fix $'s in specify delays causing bad PLI errors. [Mat Zeno]
* Fix public functions not setting up proper symbol table. [Mat Zeno]
* Fix genvars generating trace compile errors. [Mat Zeno]
* Fix VL_MULS_WWW compile error with MSVC++. [Wim Michiels]


Verilator 3.502 2005-11-30 Stable
=================================

**Minor:**

* Fix local non-IO variables in public functions and tasks.
* Fix bad lifetime optimization when same signal is assigned multiple
  times in both branch of an if. [Danny Ding]


Verilator 3.501 2005-11-16 Stable
=================================

**Major:**

* Add --prof-cfuncs for correlating profiles back to Verilog.

**Minor:**

* Fix functions where regs are declared before inputs. [Danny Ding]
* Fix bad deep expressions with bit-selects and rotate. [Prabhat Gupta]


Verilator 3.500 2005-10-30 Stable
=================================

**Major:**

* Support signed numbers, >>>, $signed, $unsigned. [MANY!]
* Support multi-dimensional arrays. [Eugen Fekete]
* Support very limited Property Specification Language
  (aka PSL or Sugar). The format and keywords are now very limited, but will
  grow with future releases. The --assert switch enables this feature.
* With --assert, generate assertions for synthesis parallel_case and full_case.

**Minor:**

* Fix generate if's with empty if/else blocks. [Mat Zeno]
* Fix generate for cell instantiations with same name. [Mat Zeno]


Verilator 3.481 2005-10-12 Stable
=================================

**Minor:**

* Add /*verilator tracing_on/off*/ for waveform control.
* Fix split optimization reordering $display statements.


Verilator 3.480 2005-09-27 Beta
===============================

**Major:**

* Allow coverage of flattened modules, and multiple points per line.
  Coverage analysis requires SystemPerl 1.230 or newer.

**Minor:**

* Add preprocessor changes to support meta-comments.
* Optimize sequential assignments of different bits of same bus; ~5% faster.
* Optimize away duplicate lookup tables.
* Optimize wide concatenates into individual words. [Ralf Karge]
* Optimize local variables from delayed array assignments.


Verilator 3.470 2005-09-06 Stable
=================================

**Minor:**

* Optimize staging flops under reset blocks.
* Add '-Werror-...' to upgrade specific warnings to errors.
* Add GCC branch prediction hints on generated if statements.
* Fix bad simulation when same function called twice in same expression.
* Fix preprocessor substitution of quoted parameterized defines.


Verilator 3.464 2005-08-24 Stable
=================================

**Major:**

* Add `systemc_imp_header, for use when using --output-split.
* Add --stats option to dump design statistics.

**Minor:**

* Fix core dump with clock inversion optimizations.


Verilator 3.463 2005-08-05 Stable
=================================

**Minor:**

* Fix case defaults when not last statement in case list. [Wim Michiels]


Verilator 3.462 2005-08-03 Stable
=================================

**Minor:**

* Fix reordering of delayed assignments to same memory index. [Wim Michiels]
* Fix compile error with Flex 2.5.1. [Jens Arm]
* Fix multiply-instantiated public tasks generating non-compilable code.


Verilator 3.461 2005-07-28 Beta
===============================

**Minor:**

* Fix compile error with older versions of bison. [Jeff Dutton]


Verilator 3.460 2005-07-27 Beta
===============================

**Major:**

* Add -output-split option to enable faster parallel GCC compiles.
  To support --output-split, the makefiles now split VM_CLASSES
  into VM_CLASSES_FAST and VM_CLASSES_SLOW. This may require a
  change to local makefiles.
* Support -v argument to read library files.

**Minor:**

* When issuing unoptimizable warning, show an example path.
* Internal tree dumps now indicate edit number that changed the node.
* Fix false warning when a clock is constant.
* Fix X/Z in decimal numbers. [Wim Michiels]
* Fix genvar statements in non-named generate blocks.
* Fix core dump when missing newline in `define. [David van der Bokke]


Verilator 3.450 2005-07-12
==========================

**Major:**

* $finish will no longer exit, but set Verilated::gotFinish().
  This enables support for final statements, and for other cleanup code.
  If this is undesired, redefine the vl_user_finish routine. Top level
  loops should use Verilated::gotFinish() as an exit condition for their
  loop, and then call top->final(). To prevent an infinite loop, a double
  $finish will still exit; this may be removed in future releases.
* Support SystemVerilog keywords $bits, $countones, $isunknown,
  $onehot, $onehot0, always_comb, always_ff, always_latch, finish.

**Minor:**

* Fix "=== 1'bx" to always be false, instead of random.


Verilator 3.440 2005-06-28 Stable
=================================

**Major:**

* Add Verilog 2001 generate for/if/case statements.



Verilator 3.431 2005-06-24 Stable
=================================

**Minor:**

* Fix selection bugs introduced in 3.430 beta.


Verilator 3.430 2005-06-22 Beta
===============================

**Minor:**

* Add Verilog 2001 variable part selects [n+:m] and [n-:m]. [Wim Michiels]


Verilator 3.422 2005-06-10 Stable
=================================

**Minor:**

* Add Verilog 2001 power (**) operator. [Danny Ding]
* Fix crash and added error message when assigning to inputs. [Ralf Karge]
* Fix tracing of modules with public functions.


Verilator 3.421 2005-06-02 Beta
===============================

**Minor:**

* Fix error about reserved word on non-public signals.
* Fix missing initialization compile errors in 3.420 beta. [Ralf Karge]


Verilator 3.420 2005-06-02 Beta
===============================

**Minor:**

* Performance improvements worth ~20%
* Add -x-assign options; ~5% faster if use -x-assign=0.
* Add error message when multiple defaults in case statement.
* Optimize shifts out of conditionals and if statements.
* Optimize local 'short' wires.
* Fix case defaults when not last statement in case list. [Ralf Karge]
* Fix crash when wire self-assigns x=x.
* Fix gate optimization with top-flattened modules. [Mahesh Kumashikar]


Verilator 3.411 2005-05-30 Stable
=================================

**Minor:**

* Fix compile error in GCC 2.96. [Jeff Dutton]


Verilator 3.410 2005-05-25 Beta
===============================

**Major:**

* Allow functions and tasks to be declared public.
  They will become public C++ functions, with appropriate C++ types.
  This allows users to make public accessor functions/tasks, instead
  of having to use public variables and `systemc_header hacks.

**Minor:**

* Skip producing output files if all inputs are identical
  This uses timestamps, similar to make. Disable with --no-skip-identical.
* Improved compile performance with large case statements.
* Fix internal error in V3Table. [Jeff Dutton]
* Fix compile error in GCC 2.96, and with SystemC 1.2. [Jeff Dutton]


Verilator 3.400 2005-04-29 Beta
===============================

**Major:**

* Internal changes to support future clocking features.
* Verilog-Perl and SystemPerl are no longer required for C++ or SystemC
  output. If you want tracing or coverage analysis, they are still needed.
* Add --sc to create pure SystemC output not requiring SystemPerl.
* Add --pins64 to create 64 bit SystemC outputs instead of sc_bv<64>.
* The --exe flag is now required to produce executables inside the makefile.
  This was previously the case any time .cpp files were passed on the
  command line.
* Add -O3 and --inline-mult for performance tuning. [Ralf Karge]
  One experiment regained 5% performance, at a cost of 300% in compile time.

**Minor:**

* Improved performance of large case/always statements with low fanin
  by converting to internal lookup tables (ROMs).
* Initialize SystemC port names. [S Shuba]
* Add Doxygen comments to Verilated includes.
* Fix -cc pins 8 bits wide and less to be uint8_t instead of uint16_t.
* Fix crash when Mdir has same name as .v file. [Gernot Koch]
* Fix crash with size mismatches on case items. [Gernot Koch]


Verilator 3.340 2005-02-18 Stable
=================================

**Minor:**

* Report misconnected pins across all modules, instead of just first error.
* Improved large netlist compile times.
* Fix over-active inlining, resulting in compile slowness.


Verilator 3.332 2005-01-27
==========================

**Major:**

* Add -E preprocess only flag, similar to GCC.
* Add CMPCONSTLR when comparison is constant due to > or < with all ones.

**Minor:**

* Fix loss of first -f file argument, introduced in 3.331.


Verilator 3.331 2005-01-18
==========================

**Major:**

* The Verilog::Perl preprocessor is now C++ code inside of Verilator.
  This improves performance, makes compilation easier, and enables
  some future features.

**Minor:**

* Support arrays of instantiations (non-primitives only). [Wim Michiels]
* Fix unlinked error with defparam. [Shawn Wang]


Verilator 3.320 2004-12-10
==========================

**Major:**

* NEWS is now renamed Changes, to support CPAN indexing.
* If Verilator is passed a C file, create a makefile link rule.
  This saves several user steps when compiling small projects.

**Minor:**

* Add new COMBDLY warning in place of fatal error. [Shawn Wang]
* Fix mis-simulation with wide-arrays under bit selects. [Ralf Karge]
* Add NC Verilog as alternative to VCS for reference tests.
* Support implicit wire declarations on input-only signals.
  (Dangerous, as leads to wires without drivers, but allowed by spec.)
* Fix compile warnings on Suse 9.1


Verilator 3.311 2004-11-29
==========================

**Major:**

* Support implicit wire declarations (as a warning). [Shawn Wang]

**Minor:**

* Fix over-shift difference in Verilog vs C++. [Ralf Karge]


Verilator 3.310 2004-11-15
==========================

**Major:**

* Support defparam.
* Support gate primitives: buf, not, and, nand, or, nor, xor, xnor.

**Minor:**

* Ignore all specify blocks.


Verilator 3.302 2004-11-12
==========================

**Minor:**

* Support NAND and NOR operators.
* Better warnings when port widths don't match.
* Fix internal error due to some port width mismatches. [Ralf Karge]
* Fix WIDTH warnings on modules that are only used
  parameterized, not in 'default' state.
* Fix selection of SystemC library on cygwin systems. [Shawn Wang]
* Fix runtime bit-selection of parameter constants.


Verilator 3.301 2004-11-04
==========================

**Minor:**

* Fix 64 bit [31:0] = {#{}} mis-simulation. [Ralf Karge]
* Fix shifts greater then word width mis-simulation. [Ralf Karge]
* Fix to work around GCC 2.96 negation bug.


Verilator 3.300 2004-10-21
==========================

**Major:**

* New backend that eliminates most VL macros.
  Improves performance 20%-50%, depending on frequency of use of signals
  over 64 bits. GCC compile times with -O2 shrink by a factor of 10.

**Minor:**

* Fix "setting unsigned int from signed value" warning.


Verilator 3.271 2004-10-21
==========================

**Minor:**

* Fix "loops detected" error with some negedge clocks.
* Fix some output code spacing issues.


Verilator 3.270 2004-10-15
==========================

**Minor:**

* Support Verilog 2001 parameters in module headers. [Ralf Karge]
* Faster code to support compilers not inlining all Verilated functions.
* Fix numeric fault when dividing by zero.



Verilator 3.260 2004-10-07
==========================

**Major:**

* Support Verilog 2001 named parameter instantiation. [Ralf Karge]

**Minor:**

* Return 1's when one bit wide extract indexes outside array bounds.
* Fix compile warnings on 64-bit operating systems.
* Fix incorrect dependency in .d file when setting VERILATOR_BIN.


Verilator 3.251 2004-09-09
==========================

**Minor:**

* Fix parenthesis overflow in Microsoft Visual C++ [Renga Sundararajan]


Verilator 3.250 2004-08-30
==========================

**Major:**

* Support Microsoft Visual C++ [Renga Sundararajan]

**Minor:**

* SystemPerl 1.161+ is required.


Verilator 3.241 2004-08-17
==========================

**Minor:**

* Support ,'s to separate multiple assignments. [Paul Nitza]
* Fix shift sign extension problem using non-GCC compilers.


Verilator 3.240 2004-08-13
==========================

**Major:**

* Verilator now uses 64 bit math where appropriate.
  Inputs and outputs of 33-64 bits wide to the C++ Verilated model must
  now be uint64_t's; SystemC has not changed, they will remain sc_bv's.
  This increases performance by ~ 9% on x86 machines, varying with how
  frequently 33-64 bit signals occur. Signals 9-16 bits wide are now
  stored as 16 bit shorts instead of longs, this aids cache packing.

**Minor:**

* Fix SystemC compile error with feedthrus. [Paul Nitza]
* Fix concat value error introduced in 3.230.


Verilator 3.230 2004-08-10
==========================

**Minor:**

* Add coverage output to test_sp example, SystemPerl 1.160+ is required.
* Fix time 0 value of signals. [Hans Van Antwerpen]
  Earlier versions would not evaluate some combinatorial signals
  until posedge/negedge blocks had been activated.
* Fix wide constant inputs to public submodules [Hans Van Antwerpen]
* Fix wide signal width extension bug.
  Only applies when width mismatch warnings were overridden.


Verilator 3.220 2004-06-22
==========================

**Major:**

* Many waveform tracing changes:
* Tracing is now supported on C++ standalone simulations. [John Brownlee]

**Minor:**

* When tracing, SystemPerl 1.150 or newer is required.
* When tracing, Verilator must be called with the --trace switch.
* Add SystemPerl example to documentation. [John Brownlee]
* Various Cygwin compilation fixes. [John Brownlee]


Verilator 3.210 2004-04-01
==========================

**Major:**

* Compiler optimization switches have changed
  See the BENCHMARKING section of the documentation.
* With Verilog-Perl 2.3 or newer, Verilator supports SystemVerilog
  preprocessor extensions.

**Minor:**

* Add localparam. [Thomas Hawkins]
* Add warnings for SystemVerilog reserved words.


Verilator 3.203 2004-03-10
==========================

**Minor:**

* Notes and repairs for Solaris. [Fred Ma]


Verilator 3.202 2004-01-27
==========================

**Major:**

* The beta version is now the primary release. See below for many changes.
  If you have many problems, you may wish to try release 3.125.
* Verilated::traceEverOn(true) must be called at time 0 if you will ever
  turn on tracing (waveform dumping) of signals. Future versions will
  need this switch to disable trace incompatible optimizations.

**Minor:**

* Optimize common replication operations.
* Fix several tracing bugs


Verilator 3.201-beta 2003-12-10
===============================

**Major:**

* BETA VERSION, USE 3.124 for stable release!
* Version 3.2XX includes an all new back-end.
  This includes automatic inlining, flattening of signals between
  hierarchy, and complete ordering of statements. This results in
  60-300% execution speedups, though less pretty C++ output. Even
  better results are possible using GCC 3.2.2 (part of Redhat 9.1), as
  GCC has fixed some optimization problems which Verilator exposes.

  If you are using `systemc_ctor, beware pointers to submodules are now
  initialized after the constructor is called for a module, to avoid
  segfaults, move statements that reference subcells into initial
  statements.
* C++ Constructor that creates a verilog module may take a char* name.
  This name will be used to prefix any $display %m arguments, so users may
  distinguish between multiple Verilated modules in a single executable.


Verilator 3.125 2004-01-27
==========================

**Minor:**

* Optimize bit replications


Verilator 3.124 2003-12-05
==========================

**Major:**

* An optimized executable will be made by default, in addition to a debug
  executable. Invoking Verilator with --debug will pick the debug version.

**Minor:**

* Many minor invisible changes to support the next version.


Verilator 3.123 2003-11-10
==========================

**Minor:**

* Wide bus performance enhancements.
* Fix function call bug when width warning suppressed. [Leon Wildman]
* Fix __DOT__ compile problem with funcs in last revision. [Leon Wildman]


Verilator 3.122 2003-10-29
==========================

**Major:**

* Modules which are accessed from external code now must be marked with
  /*verilator public_module*/ unless they already contain public signals.
  To enforce this, private cell names now have a string prepended.

**Minor:**

* Fix replicated function calls in one statement. [Robert A. Clark]
* Fix function call bug when width warning suppressed. [Leon Wildman]


Verilator 3.121 2003-09-29
==========================

**Minor:**

* Support multiplication over 32 bits. [Chris Boumenot]
  Also improved speed of addition and subtraction over 32 bits.
* Detect bit selection out of range errors.
* Detect integer width errors.
* Fix width problems on function arguments. [Robert A. Clark]


Verilator 3.120 2003-09-24
==========================

**Minor:**

* $finish now exits the model (via vl_finish function).
* Support inputs/outputs in tasks.
* Support V2K "integer int = {INITIAL_VALUE};"
* Ignore floating point delay values. [Robert A. Clark]
* Ignore `celldefine, `endcelldefine, etc. [Robert A. Clark]
* Optimize reduction operators.
* Fix converting "\ooo" into octal values.
* Fix $display("%x");


Verilator 3.112 2003-09-16
==========================

**Minor:**

* Fix functions in continuous assignments. [Robert A. Clark]
* Fix inlining of modules with 2-level deep outputs.


Verilator 3.111 2003-09-15
==========================

**Minor:**

* Fix declaration of functions before using that module. [Robert A. Clark]
* Fix module inlining bug with outputs.


Verilator 3.110 2003-09-12
==========================

**Major:**

* Support Verilog 2001 style input/output declarations. [Robert A. Clark]
* Support local vars in headers of function/tasks. [Leon Wildman]


Verilator 3.109 2003-08-28
==========================

**Major:**

* Support local variables in named begin blocks. [Leon Wildman]


Verilator 3.108 2003-08-11
==========================

**Major:**

* Support functions.

**Minor:**

* Signals 8 bits and shorter are now stored as chars
  instead of uint32_t's. This improves Dcache packing and
  improves performance by ~7%.
* $display now usually results in a single VL_PRINT rather then many.
* Optimize conditionals (?:)


Verilator 3.107 2003-07-15
==========================

**Major:**

* --private and --l2name are now the default,
  as this enables additional optimizations.
  Use --noprivate or --nol2name to get the older behavior.

**Minor:**

* Now support $display of binary and wide format data.
* Add detection of incomplete case statements,
  and added related optimizations worth ~4%.
* Work around flex bug in Redhat 8.0. [Eugene Weber]
* Add some additional C++ reserved words.
* Additional constant optimizations, ~5% speed improvement.


Verilator 3.106 2003-06-17
==========================

**Major:**

* $c can now take multiple expressions as arguments.
  For example $c("foo","bar(",32+1,");") will insert "foobar(33);"
  This makes it easier to pass the values of signals.
* Several changes to support future versions that may have
  signal-eliminating optimizations. Users should try to use these switch
  on designs, they will become the default in later versions.
* Add --private switch and /*verilator public*/ metacomment.
  This renames all signals so that compile errors will result if any
  signals referenced by C++ code are missing a /*verilator public*/
  metacomment.
* With --l2name, the second level cell C++ cell is now named "v".
  Previously it was named based on the name of the verilog code. This
  means to get to signals, scope to "{topcell} ->v ->{mysignal}" instead
  of "{topcell} ->{verilogmod}. {mysignal}". This allows different
  modules to be substituted for the cell without requiring source
  changes.

**Minor:**

* Several cleanups for Redhat 8.0.


Verilator 3.105 2003-05-08
==========================

**Minor:**

* Fix more GCC 3.2 errors. [David Black]


Verilator 3.104 2003-04-30
==========================

**Major:**

* Indicate direction of ports with VL_IN and VL_OUT.
* Allow $c32, etc, to specify width of the $c statement for VCS.
* Numerous performance improvements, worth about 25%

**Minor:**

* Fix false "indent underflow" error inside `systemc_ctor sections.
* Fix missing ordering optimizations when outputs also used internally.
* Assign constant cell pins in initial blocks rather then every cycle.
* Promote subcell's combo logic to sequential evaluation when possible.
* Fix GCC 3.2 compile errors. [Narayan Bhagavatula]


Verilator 3.103 2003-01-28
==========================

**Minor:**

* Fix missing model evaluation when clock generated several levels of
  hierarchy across from where it is used as a clock. [Richard Myers]
* Fix sign-extension bug introduced in 3.102.


Verilator 3.102 2003-01-24
==========================

**Minor:**

* Fix sign-extension of X/Z's ("32'hx")


Verilator 3.101 2003-01-13
==========================

**Minor:**

* Fix 'parameter FOO=#'bXXXX' [Richard Myers]
* Allow spaces inside numbers ("32'h 1234") [Sam Gladstone]


Verilator 3.100 2002-12-23
==========================

**Major:**

* Support for simple tasks w/o vars or I/O. [Richard Myers]

**Minor:**

* Ignore DOS carriage returns in Linux files. [Richard Myers]


Verilator 3.012 2002-12-18
==========================

**Minor:**

* Fix parsing bug with casex statements containing case items
  with bit extracts of parameters. [Richard Myers]
* Fix bug which could cause writes of non-power-of-2 sized arrays to
  corrupt memory beyond the size of the array. [Dan Lussier]
* Fix bug which did not detect UNOPT problems caused by
  submodules. See the description in the verilator man page. [John Deroo]
* Fix compile with threaded Perl. [Ami Keren]


Verilator 3.010 2002-11-03
==========================

**Major:**

* Support SystemC 2.0.1. SystemPerl version 1.130 or newer is required.

**Minor:**

* Fix bug with inlined modules under other inlined modules. [Scott Bleiweiss]


Verilator 3.005 2002-10-21
==========================

**Minor:**

* Fix X's in case (not casex/z) to constant propagate correctly.
* Fix missing include. [Kurachi]


Verilator 3.004 2002-10-10
==========================

**Minor:**

* Add module_inline metacomment and associated optimizations.
* Allow coverage_block_off metacomment in place of `coverage_block_off.
  This prevents problems with Emacs AUTORESET. [Ray Strouble]
* Fix `coverage_block_off also disabling subsequent blocks.
* Fix unrolling of loops with multiple simple statements.
* Fix compile warnings on newer GCC. [Kurachi]
* Additional concatenation optimizations.


Verilator 3.003 2002-09-13
==========================

**Minor:**

* Now compiles on Windows 2000 with Cygwin.
* Fix bug with pin assignments to wide memories.
* Optimize wire assignments to constants.


Verilator 3.002 2002-08-19
==========================

**Major:**

* First public release of version 3.


Verilator 3.000 2002-08-03
==========================

**Major:**

* All new code base. Many changes too numerous to mention.

**Minor:**

* Approximately 4 times faster then Verilator 2.
* Support initial statements
* Support correct blocking/nonblocking assignments
* Support `defines across multiple modules
* Optimize call ordering, constant propagation, and dead code elimination.


Verilator 2.1.8 2002-04-03
==========================

**Major:**

* All applications must now link against include/verilated.cpp

**Minor:**

* Paths specified to verilator_make should be absolute, or be formed
  to allow for execution in the object directory (prepend ../ to each path.)
  This allows relative filenames for makes which hash and cache dependencies.
* Add warning when parameter constants are too large. [John Deroo]
* Add warning when x/?'s used in non-casez statements.
* Add warning when blocking assignments used in posedge blocks. [Dan Lussier]
* Split evaluation function into clocked and non-clocked, 20% perf gain.


Verilator 2.1.5 2001-12-01
==========================

**Major:**

* Add coverage analysis. In conjunction with SystemC provide line
  coverage reports, without SystemC, provide a hook to user written
  accumulation function. See --coverage option of verilator_make.

**Minor:**

* Relaxed multiply range checking
* Support for constants up to 128 bits
* Randomize values used when assigning to X's.
* Add -guard option of internal testing.
* Changed indentation in emitted code to be automatically generated.
* Fix corruption of assignments of signal over 32 bits with non-0 lsb.


Verilator 2.1.4 2001-11-16
==========================

**Major:**

* Add $c("c_commands();");  for embedding arbitrary C code in Verilog.


Verilator 2.1.3 2001-11-03
==========================

**Major:**

* Support for parameters.


Verilator 2.1.2 2001-10-25
==========================

**Major:**

* Verilog Errors now reference the .v file rather then the .vpp file.

**Minor:**

* Support strings in assignments:  reg [31:0] foo = "STRG";
* Support %m in format strings. Ripped out old $info support, use
  Verilog-Perl's vpm program instead.
* Convert $stop to call of v_stop() which user can define.
* Fix bug where a==b==c would have wrong precedence rule.
* Fix bug where XNOR on odd-bit-widths (~^ or ^~) had bad value.


Verilator 2.1.1 2001-05-17
==========================

**Major:**

* New test_sp directory for System-Perl (SystemC) top level instantiation
  of the Verilated code, lower modules are still C++ code. (Experimental).
* New test_spp directory for Pure System-Perl (SystemC) where every module
  is true SystemC code. (Experimental)

**Minor:**

* Input ports are now loaded by pointer reference into the sub-cell.
  This is faster on I-386 machines, as the stack must be used when there are
  a large number of parameters. Also, this simplifies debugging as the value
  of input ports exists for tracing.
* Many code cleanups towards standard C++ style conventions.


Verilator 2.1.0 2001-05-08
==========================

**Minor:**

* Many code cleanups towards standard C++ style conventions.


Version history lost
====================


Verilator 1.8 1996-07-08
========================

[Versions 0 to 1.8 were by Paul Wasson]
* Fix single bit in concat from instance output incorrect offset bug.


Verilator 1.7 1996-05-20
========================

* Mask unused bits of DONTCAREs.


Verilator 1.6 1996-05-13
========================

* Add fasttrace script


Verilator 1.5 1996-01-09
========================

* Pass structure pointer into translated code, so multiple instances can use same functions.
* Fix static value concat on casex items.


Verilator 1.1 1995-03-30
========================

* Bug fixes, added verimake_partial script, performance improvements.


Verilator 1.0c 1994-09-30
=========================

* Initial release of Verilator


Verilator 0.0 1994-07-08
========================

* First code written.

.. ----------------------------------------------------------------------

Copyright
=========

Copyright 2001-2024 by Wilson Snyder. This program is free software; you
can redistribute it and/or modify it under the terms of either the GNU
Lesser General Public License Version 3 or the Perl Artistic License
Version 2.0.

SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
