.. Copyright 2003-2024 by <PERSON>.
.. SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

*********************************
Executable and Argument Reference
*********************************

This section describes the executables that are part of Verilator, and the
options to each executable.

.. toctree::
   :maxdepth: 1
   :hidden:

   exe_verilator.rst
   exe_verilator_coverage.rst
   exe_verilator_gantt.rst
   exe_verilator_profcfunc.rst
   exe_sim.rst
