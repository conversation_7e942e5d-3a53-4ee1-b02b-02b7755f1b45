.. Copyright 2003-2024 by <PERSON>.
.. SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

Environment
===========

This section describes the environment variables used by Verilator and
associated programs.

.. option:: LD_LIBRARY_PATH

   A generic Linux/OS variable specifying what directories have shared
   object (.so) files.  This path should include SystemC and other
   shared objects needed at simulation runtime.

.. option:: <PERSON><PERSON>

   Names the executable of the make command invoked when using the
   :vlopt:`--build` option.  Some operating systems may require "gmake" to
   this variable to launch GNU make.  If this variable is not specified,
   "make" is used.

.. option:: MAKEFLAGS

   Flags created by :command:`make` to pass to submakes. Verilator searches
   this variable to determine if a jobserver is used; see
   :vlopt:`--build-jobs`.

.. option:: OBJCACHE

   Optionally specifies a caching or distribution program to place in front
   of all runs of the C++ compiler.  For example, "ccache" or "sccache".  If using
   :command:`distcc` or :command:`icecc`/:command:`icecream`, they would
   generally be run under :command:`ccache`; see the documentation for
   those programs.  If OBJCACHE is not set, and at configure time ccache
   was present, ccache will be used as a default.

.. option:: SYSTEMC

   Deprecated.  Used only if :option:`SYSTEMC_INCLUDE` or
   :option:`SYSTEMC_LIBDIR` is not set.  If set, specifies the directory
   containing the SystemC distribution.  If not specified, it will come
   from a default optionally specified at configure time (before Verilator
   was compiled).

.. option:: SYSTEMC_ARCH

   Deprecated.  Used only if :option:`SYSTEMC_LIBDIR` is not set.
   Specifies the architecture name used by the SystemC kit.  This is the
   part after the dash in the "lib-{...}" directory name created by a
   :command:`make` in the SystemC distribution.  If not set, Verilator will
   try to intuit the proper setting, or use the default optionally
   specified at configure time (before Verilator was compiled).

.. option:: SYSTEMC_CXX_FLAGS

   Specifies additional flags that are required to be passed to GCC when
   building the SystemC model.  System 2.3.0 may need this set to
   "-pthread".

.. option:: SYSTEMC_INCLUDE

   If set, specifies the directory containing the systemc.h header file.
   If not specified, it will come from a default optionally specified at
   configure time (before Verilator was compiled), or computed from
   SYSTEMC/include.

.. option:: SYSTEMC_LIBDIR

   If set, specifies the directory containing the libsystemc.a library.  If
   not specified, it will come from a default optionally specified at
   configure time (before Verilator was compiled), or computed from
   SYSTEMC/lib-SYSTEMC_ARCH.

.. option:: VERILATOR_BIN

   If set, specifies an alternative name of the ``verilator`` binary.  May
   be used for debugging and selecting between multiple operating system
   builds.

.. option:: VERILATOR_COVERAGE_BIN

   If set, specifies an alternative name of the ``verilator_coverage``
   binary.  May be used for debugging and selecting between multiple
   operating system builds.

.. option:: VERILATOR_GDB

   If set, the command to run when using the :vlopt:`--gdb` option, such as
   "ddd".  If not specified, it will use "gdb".

.. option:: VERILATOR_ROOT

   The ``VERILATOR_ROOT`` environment variable is used in several places:

   * At ``./configure`` time: If set, it is embedded into the binary, and
     at runtime if ``VERILATOR_ROOT`` is not set, the embedded value is
     used for the runtime default.

   * When ``verilator`` is run: If ``VERILATOR_ROOT`` is set it will be
     used to find the ``verilator_bin`` executable (this is the actual
     Verilator binary; ``verilator`` is a Perl wrapper). If not set, the
     ``verilator`` script uses other methods to find ``verilator_bin``
     (looking in the same directory and falling back to ``$PATH``).

   * When ``make`` is run on the Makefile generated by ``verilator``: The
     value of ``VERILATOR_ROOT`` (falling back to the value embedded in the
     binary if not set) is used to find the include files
     (``include/verilated.mk``).

   If you are using a pre-compiled Verilator package, you should not need
   to set ``VERILATOR_ROOT`` - the value embedded in the binary should be
   correct.  In fact this option *does not work* with Verilator packages
   that have been installed with ``make install``. If a Verilator package
   has been installed using ``./configure --prefix=/some/path && make
   install`` and then moved to another location, you cannot use
   ``VERILATOR_ROOT`` to point to the new version.

   See :ref:`Installation` for more details.

.. option:: VERILATOR_SOLVER

   If set, the command to run as a constrained randomization backend, such
   as :command:`cvc4 --lang=smt2 --incremental`.  If not specified, it will use
   the one supplied or found during configure, or :command:`z3 --in` if empty.

.. option:: VERILATOR_VALGRIND

   If set, the command to run when using the :vlopt:`--valgrind` option, such as
   "valgrind --tool=callgrind".  If not specified, it will use "valgrind".


Make Variables
==============

This section describes the make variables used by Verilator. These may be
set by passing them to make e.g. ``make CXX=my-gcc ...``.

.. option:: AR

   Optionally overrides the default :command:`ar` (archive) binary used by
   the Verilated makefiles. If AR is not set, the version found at
   configure time is used.

.. option:: CXX

   Optionally overrides the default compiler binary used by the Verilated
   makefiles. If CXX is not set, the version found at configure time is
   used.  Note the default flags passed to the compiler are determined at
   configuration time, so changing the CXX compiler version using this
   variable, as opposed to passing it at configuration time, may not give
   desired results.

.. option:: LINK

   Optionally overrides the default linker binary used by the Verilated
   makefiles. If LINK is not set, the version found at configure time is
   used.  Note the default flags passed to the linker are determined at
   configuration time, so changing the LINK version using this variable, as
   opposed to passing it at configuration time, may not give desired
   results.

.. option:: PERL

   Optionally overrides the default :command:`perl` binary used by the
   Verilated makefiles. If PERL is not set, the version found at configure
   time, and compiled into the Verilator binary, is used.

.. option:: PYTHON3

   Optionally overrides the default :command:`python3` binary used by the
   Verilated makefiles. If PYTHON3 is not set, the version found at
   configure time is used.
