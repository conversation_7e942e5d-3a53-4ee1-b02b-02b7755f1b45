.. Copyright 2003-2024 by <PERSON>.
.. SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

.. _Examples:

========
Examples
========

This section covers the following examples:

* :ref:`Example Create-Binary Execution`
* :ref:`Example C++ Execution`
* :ref:`Example SystemC Execution`
* :ref:`Examples in the Distribution`

.. toctree::
   :maxdepth: 1
   :hidden:

   example_binary.rst
   example_cc.rst
   example_sc.rst
   example_dist.rst
