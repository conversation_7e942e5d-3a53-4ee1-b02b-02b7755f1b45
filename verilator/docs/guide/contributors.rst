.. Copyright 2003-2024 by <PERSON>.
.. SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

************************
Contributors and Origins
************************

Authors
=======

When possible, please instead report bugs at `Verilator Issues
<https://verilator.org/issues>`_.

The primary author is <PERSON> <<EMAIL>>.

Major concepts by <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Yu<PERSON><PERSON>
Ta<PERSON>, and <PERSON><PERSON>.


Contributors
============

Many people have provided ideas and other assistance with Verilator.

Verilator is receiving significant development support from the `CHIPS
Alliance <https://chipsalliance.org>`_, `Antmicro Ltd
<https://antmicro.com>`_ and `Shunyao CAD <https://shunyaocad.com>`_.

Previous major corporate sponsors of Verilator, by providing significant
contributions of time or funds include: Antmicro Ltd., Atmel Corporation,
Compaq Corporation, Digital Equipment Corporation, Embecosm Ltd., Hicamp
Systems, Intel Corporation, Marvell Inc., Mindspeed Technologies Inc.,
MicroTune Inc., picoChip Designs Ltd., Sun Microsystems Inc., Nauticus
Networks Inc., SiCortex Inc, Shunyao CAD, and Western Digital Inc.

The contributors of major functionality are: Jeremy Bennett, Krzysztof
Bieganski, Byron Bradley, Lane Brooks, John Coiner, Duane Galbi, Geza Lore,
Todd Strader, Yutetsu Takatsukasa, Stefan Wallentowitz, Paul Was<PERSON>, Ji<PERSON>
Xu, and <PERSON> Snyder.

Some of the people who have provided ideas, and feedback for Verilator
include:

<PERSON> Addison, Tariq B. Ahmad, Nikana Anastasiadis, John David Anglin,
Frederic Antonin, Hans Van Antwerpen, Vasu Arasanipalai, Jens Arm, Rohan
Arshid, Gökçe Aydos, Adam Bagley, Sharad Bagri, Robert Balas, Marco
Balboni, Matthew Ballance, Andrew Bardsley, Ilya Barkov, Matthew Barr,
Geoff Barrett, Kaleb Barrett, Daniel Bates, Julius Baxter, Michael Berman,
Jean Berniolles, Victor Besyakov, Narayan Bhagavatula, Moinak
Bhattacharyya, Kritik Bhimani, David Biancolin, David Binderman, Piotr
Binkowski, Johan Björk, David Black, Tymoteusz Blazejczyk, Scott Bleiweiss,
David van der Bokke, Daniel Bone, Guy Bonneau, Krzysztof Boroński, Gregg
Bouchard, Christopher Boumenot, Nick Bowler, Bryan Brady, Maarten De
Braekeleer, Charlie Brej, J Briquet, John Brownlee, KC Buckenmaier, Jeff
Bush, Lawrence Butcher, Tony Bybell, Iru Cai, Ted Campbell, Anthony Campos,
Chris Candler, Lauren Carlson, Gregory Carver, Donal Casey, Sebastien Van
Cauwenberghe, Alex Chadwick, Greg Chadwick, Marcel Chang, Aliaksei
Chapyzhenka, Chih-Mao Chen, Guokai Chen, Terry Chen, Yi-Chung Chen, Yurii
Cherkasov, Hennadii Chernyshchyk, Enzo Chi, Robert A. Clark, Ryan Clarke,
Allan Cochrane, Keith Colbert, Quentin Corradi, Nassim Corteggiani,
Gianfranco Costamagna, February Cozzocrea, Sean Cross, George Cuan, Michal
Czyz, Joe DErrico, Jim Dai, Lukasz Dalek, Laurens van Dam, Gunter
Dannoritzer, Ashutosh Das, Julian Daube, Bernard Deadman, Peter Debacker,
John Demme, Mike Denio, John Deroo, Philip Derrick, Aadi Desai, John
Dickol, Ruben Diez, Danny Ding, Jacko Dirks, Ivan Djordjevic, Brad Dobbie,
Paul Donahue, Jonathon Donaldson, Anthony Donlon, Caleb Donovick, Larry
Doolittle, Leendert van Doorn, Sebastian Dressler, Jonathan Drolet, Maciej
Dudek, Alex Duller, Jeff Dutton, Tomas Dzetkulic, Usuario Eda, Charles
Eddleston, Chandan Egbert, Joe Eiler, Ahmed El-Mahmoudy, Trevor Elbourne,
Mats Engstrom, Robert Farrell, Julien Faucher, Olivier Faure, Eugen Fekete,
Fabrizio Ferrandi, Udi Finkelstein, Brian Flachs, Bill Flynn, Andrea
Foletto, Alex Forencich, Aurelien Francillon, Bob Fredieu, Manuel
Freiberger, Mostafa Gamal, Vito Gamberini, Mostafa Garnal, Benjamin
Gartner, Christian Gelinek, Richard E George, Peter Gerst, Glen Gibb,
Michael Gielda, Barbara Gigerl, Nimrod Gileadi, Shankar Giri, Dan
Gisselquist, Petr Gladkikh, Sam Gladstone, Mariusz Glebocki, Embedded Go,
Andrew Goessling, Amir Gonnen, Chitlesh Goorah, Tomasz Gorochowik, Kai
Gossner, Tarik Graba, Sergi Granell, Al Grant, Nathan Graybeal, Alexander
Grobman, Qian Gu, Xuan Guo, Prabhat Gupta, Driss Hafdi, Neil Hamilton,
James Hanlon, Tang Haojin, Øyvind Harboe, Jannis Harder, David Harris,
Junji Hashimoto, Thomas Hawkins, Mitch Hayenga, Harald Heckmann, Robert
Henry, Stephen Henry, Sebastian Hesselbarth, David Hewson, Jamey Hicks,
Joel Holdsworth, Andrew Holme, Peter Holmes, Hiroki Honda, Alex Hornung,
Pierre-Henri Horrein, David Horton, Peter Horvath, Jae Hossell, Kuoping
Hsu, Teng Huang, Steven Hugg, Huanghuang Zhou, Alan Hunter, James
Hutchinson, Tim Hutt, Ehab Ibrahim, Edgar E. Iglesias, Shahid Ikram, Jamie
Iles, Vighnesh Iyer, Ben Jackson, Daniel Jacques, Shareef Jalloq, Marlon
James, Krzysztof Jankowski, Eyck Jentzsch, HyungKi Jeong, Iztok Jeras,
Alexandre Joannou, James Johnson, Christophe Joly, Justin Jones,
William D. Jones, Larry Darryl Lee Jr., Franck Jullien, James Jung,
Yoshitomo Kaneda, Mike Kagen, Arthur Kahlich, Kaalia Kahn, Guy-Armand
Kamendje, Vasu Kandadi, Kanad Kanhere, Patricio Kaplan, Pieter Kapsenberg,
Rafal Kapuscik, Ralf Karge, Per Karlsson, Dan Katz, Sol Katzman, Ian
Kennedy, Ami Keren, Michael Killough, Sun Kim, Jonathan Kimmitt, Olof
Kindgren, Kevin Kiningham, Cameron Kirk, Dan Kirkham, Aleksander Kiryk,
Sobhan Klnv, Gernot Koch, Jack Koenig, Soon Koh, Nathan Kohagen, Steve
Kolecki, Brett Koonce, Will Korteland, Andrei Kostovski, Wojciech Koszek,
Varun Koyyalagunta, Arkadiusz Kozdra, Markus Krause, David Kravitz, Adam
Krolnik, Roland Kruse, Mahesh Kumashikar, Andreas Kuster, Sergey Kvachonok,
Charles Eric LaForest, Kevin Laeufer, Ed Lander, Steve Lang, Pierre
Laroche, Stephane Laurent, Walter Lavino, Christian Leber, David Ledger,
Alex Lee, Larry Lee, Yoda Lee, Michaël Lefebvre, Dag Lem, Igor Lesik, John
Li, Kay Li, Zixi Li, Davide Libenzi, Nandor Licker, Eivind Liland, Ícaro
Lima, Kevin Lin, Yu-Sheng Lin, Charlie Lind, Andrew Ling, Jiuyang Liu, Joey
Liu, Paul Liu, Derek Lockhart, Jake Longo, Arthur Low, Jose Loyola, Stefan
Ludwig, Dan Lussier, Konstantin Lübeck, Fred Ma, Liwei Ma, Duraid Madina,
Oleh Maksymenko, Affe Mao, Julien Margetts, Chick Markley, Alexis Marquet,
Mark Marshall, Alfonso Martinez, Unai Martinez-Corral, Adrien Le Masle,
Yves Mathieu, Vladimir Matveyenko, Patrick Maupin, Stan Mayer, Conor
McCullough, Jason McMullan, Elliot Mednick, Yuan Mei, Andy Meier,
Rodrigo A. Melo, Benjamin Menküc, Jake Merdich, David Metz, Wim Michiels,
Miodrag Milanović, Darryl Miles, Kevin Millis, Andrew Miloradovsky, Wai Sum
Mong, Peter Monsson, Sean Moore, Stuart Morris, Dennis Muhlestein, John
Murphy, Matt Myers, Nathan Myers, Richard Myers, Alex Mykyta, Dimitris
Nalbantis, Peter Nelson, Felix Neumärker, Bob Newgard, Cong Van Nguyen,
Rachit Nigam, Toru Niina, Paul Nitza, Yossi Nivin, Pete Nixon, Lisa Noack,
Mark Nodine, Michael Nolan, Andrew Nolte, Joseph Nwabueze, Kuba Ober,
Andreas Olofsson, Baltazar Ortiz, Aleksander Osman, Don Owen, Tim Paine,
Deepa Palaniappan, James Pallister, Vassilis Papaefstathiou, Sanggyu Park,
Brad Parker, Risto Pejašinović, Morten Borup Petersen, Dan Petrisko, Wesley
Piard, Maciej Piechotka, David Pierce, Cody Piersall, T. Platz, Michael
Platzer, Dominic Plunkett, David Poole, Michael Popoloski, Roman Popov,
Aylon Chaim Porat, Oron Port, Rich Porter, Rick Porter, Stefan Post,
Niranjan Prabhu, Damien Pretet, Harald Pretl, Bill Pringlemeir, Usha
Priyadharshini, Mark Jackson Pulver, Prateek Puri, Jiacheng Qian, Marshal
Qiao, Raynard Qiao, Yujia Qiao, Jasen Qin, Frank Qiu, Nandu Raj, Kamil
Rakoczy, Danilo Ramos, Drew Ranck, Chris Randall, Anton Rapp, Josh Redford,
Odd Magne Reitan, Frédéric Requin, Dustin Richmond, Samuel Riedel, Alberto
Del Rio, Eric Rippey, Oleg Rodionov, Ludwig Rogiers, Paul Rolfe, Michail
Rontionov, Arjen Roodselaar, Tobias Rosenkranz, Yernagula Roshit, Ryszard
Rozak, Huang Rui, Graham Rushton, Jan Egil Ruud, Denis Rystsov, Pawel
Sagan, Robert Sammelson, John Sanguinetti, Josep Sans, Luca Sasselli,
Martin Scharrer, Martin Schmidt, Julie Schwartz, Galen Seitz, Joseph
Shaker, Mark Shaw, Salman Sheikh, Zhou Shen, Hao Shi, James Shi, Michael
Shinkarovsky, Rafael Shirakawa, Jeffrey Short, S Shuba, Fan Shupei, Ethan
Sifferman, Anderson Ignacio da Silva, Rodney Sinclair, Ameya Vikram Singh,
Sanjay Singh, Frans Skarman, Nate Slager, Steven Slatter, Mladen
Slijepcevic, Brian Small, Garrett Smith, Gus Smith, Tim Snyder, Maciej
Sobkowski, Stan Sokorac, Alex Solomatnikov, Flavien Solt, Wei Song, Trefor
Southwell, Martin Stadler, Art Stamness, David Stanford, John Stevenson,
Pete Stevenson, Patrick Stewart, Rob Stoddard, Tood Strader, John Stroebel,
Ray Strouble, Sven Stucki, Howard Su, Emerson Suguimoto, Gene Sullivan,
Qingyao Sun, Renga Sundararajan, Kuba Sunderland-Ober, Gustav Svensk,
Rupert Swarbrick, Jevin Sweval, Shinya T-Y, Thierry Tambe, Jesse Taube,
Drew Taussig, Jose Tejada, Sören Tempel, Peter Tengstrand, Wesley Terpstra,
Rui Terra, Stefan Thiede, Justin Thiel, Gary Thomas, Ian Thompson, Kevin
Thompson, Mike Thyer, Hans Tichelaar, Tudor Timi, Viktor Tomov, Steve Tong,
Topa Topino, Àlex Torregrosa, Topa Tota, Michael Tresidder, Lenny Truong,
David Turner, Neil Turton, Hideto Ueno, Mike Urbach, Joel Vandergriendt,
Srini Vemuri, Srinivasan Venkataramanan, Yuri Victorovich, Ivan Vnučec,
Bogdan Vukobratovic, Holger Waechtler, Philipp Wagner, Johannes Walter, CY
Wang, Chuxuan Wang, Shawn Wang, Zhanglei Wang, Greg Waters, Thomas Watts,
Eugene Weber, John Wehle, Tianrui Wei, David Welch, Thomas J Whatson,
Martin Whitaker, Marco Widmer, Leon Wildman, Daniel S. Wilkerson, Daniel
Wilkerson, Gerald Williams, Trevor Williams, Don Williamson, Jan Van
Winkel, Jeff Winston, Joshua Wise, Clifford Wolf, Johan Wouters, Paul
Wright, Tobias Wölfel, Junyi Xi, Ding Xiaoliang, Liu Xiaoyi, Mandy Xu,
Shanshan Xu, Yinan Xu, SU YANG, Felix Yan, Luke Yang, Amir Yazdanbakhsh,
Chentai (Seven) Yuan, Florian Zaruba, Mat Zeno, Keyi Zhang, Xi Zhang, Yike
Zhou, Jiamin Zhu.

Thanks to them, and all those we've missed mentioning above, and to those
whom have wished to remain anonymous.


Historical Origins
==================

Verilator was conceived in 1994 by Paul Wasson at the Core Logic Group at
Digital Equipment Corporation.  The Verilog code that was converted to C
was then merged with a C-based CPU model of the Alpha processor and
simulated in a C-based environment called CCLI.

In 1995 Verilator started being used for Multimedia and Network Processor
development inside Digital.  Duane Galbi took over the active development
of Verilator, and added several performance enhancements, and CCLI was
still being used as the shell.

In 1998, through the efforts of existing DECies, mainly Duane Galbi,
Digital graciously agreed to release the source code.  (Subject to the code
not being resold, which is compatible with the GNU Public License.)

In 2001, Wilson Snyder took the kit, added a SystemC mode, and called
it Verilator2.  This was the first packaged public release.

In 2002, Wilson Snyder created Verilator 3.000 by rewriting Verilator from
scratch in C++.  This added many optimizations, yielding about a 2-5x
performance gain.

In 2009, major SystemVerilog and DPI language support was added.

In 2018, Verilator 4.000 was released with multithreaded support.

In 2019, Verilator joined the `CHIPS Alliance
<https://chipsalliance.org>`_.

In 2022, Verilator 5.000 was released with IEEE scheduling semantics,
fork/join, delay handling, DFG performance optimizations, and other
improvements.

Currently, various language features and performance enhancements are added
as the need arises, focusing on completing Universal Verification
Methodology (UVM, IEEE 1800.2-2017) support.
