PROJECT_NAME           = "Verilator"
INPUT                  = ../../include
OUTPUT_DIRECTORY       = _build/doxygen/verilated

EXTRACT_ALL            = NO
EXTRACT_LOCAL_CLASSES  = NO
EXTRACT_PRIVATE        = NO
EXTRACT_STATIC         = YES
FULL_PATH_NAMES        = NO
GENERATE_HTML          = NO
GENERATE_LATEX         = NO
GENERATE_XML           = YES
HIDE_FRIEND_COMPOUNDS  = YES
HIDE_IN_BODY_DOCS      = YES
HIDE_UNDOC_CLASSES     = YES
HIDE_UNDOC_MEMBERS     = YES
INTERNAL_DOCS          = NO
OPTIMIZE_OUTPUT_FOR_C  = NO
RECURSIVE              = NO
SHOW_INCLUDE_FILES     = NO
WARN_IF_UNDOCUMENTED   = NO

ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = YES
PREDEFINED             = \
                         "DOXYGEN=1" \
                         "VL_ATTR_NORETURN=" \
                         "VL_ATTR_PRINTF()=" \
                         "VL_ATTR_WEAK=" \
                         "VL_GUARDED_BY()=" \
                         "VL_MT_SAFE=" \
                         "VL_MT_SAFE_EXCLUDES()=" \
                         "VL_MT_SAFE_POSTINIT=" \
                         "VL_MT_UNSAFE=" \
                         "VL_MT_UNSAFE_ONE=" \
                         "VL_NOT_FINAL=" \
                         "VL_PURE=" \
                         "VL_REQUIRES()=" \
                         "__restrict=" \
