---
name: Feature Request
about: Use this to request something should be supported, or a new feature added. (Note our contributor agreement at https://github.com/verilator/verilator/blob/master/docs/CONTRIBUTING.rst)
title: ''
labels: new
assignees: ''

---

Thanks for taking the time to report this.

What would you like added/supported?

What 'verilator --version' are you using?  Did you try it with the git master version?

Can you attach an example that runs on other simulators?  (Must be openly licensed, ideally in test_regress format.)

May we assist you in trying to fix this in Verilator yourself?
