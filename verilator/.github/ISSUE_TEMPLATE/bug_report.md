---
name: Bug report
about: Use this to report that something isn't working as expected, and it isn't "Unsupported." (Note our contributor agreement at https://github.com/verilator/verilator/blob/master/docs/CONTRIBUTING.rst)
title: ''
labels: new
assignees: ''

---

Thanks for taking the time to report this.

Can you attach an example that shows the issue?  (Must be openly licensed, ideally in test_regress format.)

What 'verilator' command line do we use to run your example?

What 'verilator --version' are you using?  Did you try it with the git master version?

What OS and distribution are you using?

May we assist you in trying to fix this in Verilator yourself?

(Please avoid attaching screenshots that show text - you can convert images to text using e.g. https://ocr.space)
