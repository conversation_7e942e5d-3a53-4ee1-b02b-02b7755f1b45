# DESCRIPTION: Github actions config
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

name: coverage

on:
  workflow_dispatch:
  schedule:
  - cron: '0 0 * * 0' # weekly

env:
  CI_OS_NAME: linux
  CI_COMMIT: ${{ github.sha }}
  COVERAGE: 1
  VERILATOR_ARCHIVE: verilator-coverage-${{ github.sha }}.tar.gz

defaults:
  run:
    shell: bash
    working-directory: repo

jobs:


  Build:
    runs-on: ubuntu-22.04
    env:
      CI_BUILD_STAGE_NAME: build
      CI_RUNS_ON: ubuntu-22.04
    steps:

    - name: Checkout
      uses: actions/checkout@v4
      with:
        path: repo

    - name: Install packages for build
      run: ./ci/ci-install.bash

    - name: Build
      run: ./ci/ci-script.bash

    - name: Tar up repository
      working-directory: ${{ github.workspace }}
      run: tar --posix -c -z -f ${{ env.VERILATOR_ARCHIVE }} repo

    - name: Upload tar archive
      uses: actions/upload-artifact@v4
      with:
        path: ${{ github.workspace }}/${{ env.VERILATOR_ARCHIVE }}
        name: ${{ env.VERILATOR_ARCHIVE }}


  Test:
    needs: Build
    strategy:
      fail-fast: false
      matrix:
        test:
        - vlt-
        - vltmt-
        num:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        include:
        - { test: dist, num: '' }
    runs-on: ubuntu-22.04
    name: test-${{ matrix.test }}${{ matrix.num }}
    env:
      CI_BUILD_STAGE_NAME: test
      CI_RUNS_ON: ubuntu-22.04
    steps:

    - name: Download tar archive
      uses: actions/download-artifact@v4
      with:
        name: ${{ env.VERILATOR_ARCHIVE }}
        path: ${{ github.workspace }}

    - name: Unpack tar archive
      working-directory: ${{ github.workspace }}
      run: tar -x -z -f ${{ env.VERILATOR_ARCHIVE }}

    - name: Install test dependencies
      run: ./ci/ci-install.bash

    - name: Test
      env:
        TESTS: coverage-${{ matrix.test }}${{ matrix.num }}
      run: ./ci/ci-script.bash

    - name: Upload coverage data to Codecov
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      run: |
        find . -name '*.gcno' -exec rm {} \;
        ./ci/codecov -v upload-process -Z --sha ${{ github.sha }} -f nodist/obj_dir/coverage/app_total.info
