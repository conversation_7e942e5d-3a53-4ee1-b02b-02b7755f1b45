// -*- mode: C++; c-file-style: "cc-mode" -*-
//=============================================================================
//
// THIS MODULE IS PUBLICLY LICENSED
//
// Copyright 2001-2024 by <PERSON>. This program is free software; you
// can redistribute it and/or modify it under the terms of either the GNU
// Lesser General Public License Version 3 or the Perl Artistic License
// Version 2.0.
// SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
//
//=============================================================================
///
/// \file
/// \brief Verilated tracing in FST for SystemC implementation code
///
/// This file is deprecated, only verilated_fst_sc.h is needed.
/// It is provided only for backward compatibility with user's linker scripts.
///
//=============================================================================

#ifdef VL_NO_LEGACY
#error "verilated_fst_sc.cpp is deprecated; verilated_fst_sc.h is self-sufficient"
#endif
