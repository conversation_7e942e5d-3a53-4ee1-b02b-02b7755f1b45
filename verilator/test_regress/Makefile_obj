# -*- Makefile -*-
#*****************************************************************************
#
# DESCRIPTION: Verilator Example: Makefile for inside object directory
#
# This is executed in the object directory, and called by ../Makefile
#
# Copyright 2003-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
#
#*****************************************************************************

default: $(VM_PREFIX)

include $(VM_PREFIX).mk

# So t/t_foobar.cpp is found (due to test_regress using -Mdir)
VPATH += ../..

#######################################################################

# Needed by DPI tests
CPPFLAGS += -DVERILATOR=1

# Debugging
CPPFLAGS += -DVL_DEBUG=1
# Assertions disabled as SystemC libraries are not clean
#CPPFLAGS += -D_GLIBCXX_DEBUG

# Needed by tracing routines
CPPFLAGS += -DTEST_OBJ_DIR=$(TEST_OBJ_DIR)
CPPFLAGS += -DVM_PREFIX=$(VM_PREFIX)
CPPFLAGS += -DVM_PREFIX_INCLUDE="<$(VM_PREFIX).h>"
CPPFLAGS += -DVM_PREFIX_ROOT_INCLUDE="<$(VM_PREFIX)___024root.h>"
CPPFLAGS += $(CPPFLAGS_DRIVER)
CPPFLAGS += $(CPPFLAGS_DRIVER2)
CPPFLAGS += $(CPPFLAGS_ADD)

# Reduce spin count for faster testing
CPPFLAGS += -DVL_LOCK_SPINS=10000

ifeq ($(CFG_WITH_LONGTESTS),yes)
  ifeq ($(DRIVER_STD),newest)
    CPPFLAGS += $(CFG_CXXFLAGS_STD)
  endif
endif

#######################################################################
