// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2005 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/
   // Inputs
   clk
   );

   input clk;
   integer cyc; initial cyc=1;

   reg [127:0]          i;
   wire [127:0]         q1;
   wire [127:0]         q32;
   wire [127:0]         q64;
   wire [63:0]          q64_low;

   assign q1 = {
       i[24*4],  i[25*4],   i[26*4],   i[27*4],   i[28*4],  i[29*4],   i[30*4],   i[31*4],
       i[16*4],  i[17*4],   i[18*4],   i[19*4],   i[20*4],  i[21*4],   i[22*4],   i[23*4],
       i[8*4],   i[9*4],    i[10*4],   i[11*4],   i[12*4],  i[13*4],   i[14*4],   i[15*4],
       i[0*4],   i[1*4],    i[2*4],    i[3*4],    i[4*4],   i[5*4],    i[6*4],    i[7*4],

       i[24*4+1], i[25*4+1], i[26*4+1], i[27*4+1], i[28*4+1], i[29*4+1], i[30*4+1], i[31*4+1],
       i[16*4+1], i[17*4+1], i[18*4+1], i[19*4+1], i[20*4+1], i[21*4+1], i[22*4+1], i[23*4+1],
       i[8*4+1],  i[9*4+1],  i[10*4+1], i[11*4+1], i[12*4+1], i[13*4+1], i[14*4+1], i[15*4+1],
       i[0*4+1],  i[1*4+1],  i[2*4+1],  i[3*4+1],  i[4*4+1],  i[5*4+1],  i[6*4+1],  i[7*4+1],

       i[24*4+2], i[25*4+2], i[26*4+2], i[27*4+2], i[28*4+2], i[29*4+2], i[30*4+2], i[31*4+2],
       i[16*4+2], i[17*4+2], i[18*4+2], i[19*4+2], i[20*4+2], i[21*4+2], i[22*4+2], i[23*4+2],
       i[8*4+2],  i[9*4+2],  i[10*4+2], i[11*4+2], i[12*4+2], i[13*4+2], i[14*4+2], i[15*4+2],
       i[0*4+2],  i[1*4+2],  i[2*4+2],  i[3*4+2],  i[4*4+2],  i[5*4+2],  i[6*4+2],  i[7*4+2],

       i[24*4+3], i[25*4+3], i[26*4+3], i[27*4+3], i[28*4+3], i[29*4+3], i[30*4+3], i[31*4+3],
       i[16*4+3], i[17*4+3], i[18*4+3], i[19*4+3], i[20*4+3], i[21*4+3], i[22*4+3], i[23*4+3],
       i[8*4+3],  i[9*4+3],  i[10*4+3], i[11*4+3], i[12*4+3], i[13*4+3], i[14*4+3], i[15*4+3],
       i[0*4+3],  i[1*4+3],  i[2*4+3],  i[3*4+3],  i[4*4+3],  i[5*4+3],  i[6*4+3],  i[7*4+3]};

   assign q64[127:64] = {
       i[24*4],  i[25*4],   i[26*4],   i[27*4],   i[28*4],  i[29*4],   i[30*4],   i[31*4],
       i[16*4],  i[17*4],   i[18*4],   i[19*4],   i[20*4],  i[21*4],   i[22*4],   i[23*4],
       i[8*4],   i[9*4],    i[10*4],   i[11*4],   i[12*4],  i[13*4],   i[14*4],   i[15*4],
       i[0*4],   i[1*4],    i[2*4],    i[3*4],    i[4*4],   i[5*4],    i[6*4],    i[7*4],

       i[24*4+1], i[25*4+1], i[26*4+1], i[27*4+1], i[28*4+1], i[29*4+1], i[30*4+1], i[31*4+1],
       i[16*4+1], i[17*4+1], i[18*4+1], i[19*4+1], i[20*4+1], i[21*4+1], i[22*4+1], i[23*4+1],
       i[8*4+1],  i[9*4+1],  i[10*4+1], i[11*4+1], i[12*4+1], i[13*4+1], i[14*4+1], i[15*4+1],
       i[0*4+1],  i[1*4+1],  i[2*4+1],  i[3*4+1],  i[4*4+1],  i[5*4+1],  i[6*4+1],  i[7*4+1]};
   assign q64[63:0] = {
       i[24*4+2], i[25*4+2], i[26*4+2], i[27*4+2], i[28*4+2], i[29*4+2], i[30*4+2], i[31*4+2],
       i[16*4+2], i[17*4+2], i[18*4+2], i[19*4+2], i[20*4+2], i[21*4+2], i[22*4+2], i[23*4+2],
       i[8*4+2],  i[9*4+2],  i[10*4+2], i[11*4+2], i[12*4+2], i[13*4+2], i[14*4+2], i[15*4+2],
       i[0*4+2],  i[1*4+2],  i[2*4+2],  i[3*4+2],  i[4*4+2],  i[5*4+2],  i[6*4+2],  i[7*4+2],

       i[24*4+3], i[25*4+3], i[26*4+3], i[27*4+3], i[28*4+3], i[29*4+3], i[30*4+3], i[31*4+3],
       i[16*4+3], i[17*4+3], i[18*4+3], i[19*4+3], i[20*4+3], i[21*4+3], i[22*4+3], i[23*4+3],
       i[8*4+3],  i[9*4+3],  i[10*4+3], i[11*4+3], i[12*4+3], i[13*4+3], i[14*4+3], i[15*4+3],
       i[0*4+3],  i[1*4+3],  i[2*4+3],  i[3*4+3],  i[4*4+3],  i[5*4+3],  i[6*4+3],  i[7*4+3]};

   assign q64_low = {
       i[24*4+2], i[25*4+2], i[26*4+2], i[27*4+2], i[28*4+2], i[29*4+2], i[30*4+2], i[31*4+2],
       i[16*4+2], i[17*4+2], i[18*4+2], i[19*4+2], i[20*4+2], i[21*4+2], i[22*4+2], i[23*4+2],
       i[8*4+2],  i[9*4+2],  i[10*4+2], i[11*4+2], i[12*4+2], i[13*4+2], i[14*4+2], i[15*4+2],
       i[0*4+2],  i[1*4+2],  i[2*4+2],  i[3*4+2],  i[4*4+2],  i[5*4+2],  i[6*4+2],  i[7*4+2],

       i[24*4+3], i[25*4+3], i[26*4+3], i[27*4+3], i[28*4+3], i[29*4+3], i[30*4+3], i[31*4+3],
       i[16*4+3], i[17*4+3], i[18*4+3], i[19*4+3], i[20*4+3], i[21*4+3], i[22*4+3], i[23*4+3],
       i[8*4+3],  i[9*4+3],  i[10*4+3], i[11*4+3], i[12*4+3], i[13*4+3], i[14*4+3], i[15*4+3],
       i[0*4+3],  i[1*4+3],  i[2*4+3],  i[3*4+3],  i[4*4+3],  i[5*4+3],  i[6*4+3],  i[7*4+3]};

   assign q32[127:96] = {
       i[24*4],  i[25*4],   i[26*4],   i[27*4],   i[28*4],  i[29*4],   i[30*4],   i[31*4],
       i[16*4],  i[17*4],   i[18*4],   i[19*4],   i[20*4],  i[21*4],   i[22*4],   i[23*4],
       i[8*4],   i[9*4],    i[10*4],   i[11*4],   i[12*4],  i[13*4],   i[14*4],   i[15*4],
       i[0*4],   i[1*4],    i[2*4],    i[3*4],    i[4*4],   i[5*4],    i[6*4],    i[7*4]};
   assign q32[95:64] = {
       i[24*4+1], i[25*4+1], i[26*4+1], i[27*4+1], i[28*4+1], i[29*4+1], i[30*4+1], i[31*4+1],
       i[16*4+1], i[17*4+1], i[18*4+1], i[19*4+1], i[20*4+1], i[21*4+1], i[22*4+1], i[23*4+1],
       i[8*4+1],  i[9*4+1],  i[10*4+1], i[11*4+1], i[12*4+1], i[13*4+1], i[14*4+1], i[15*4+1],
       i[0*4+1],  i[1*4+1],  i[2*4+1],  i[3*4+1],  i[4*4+1],  i[5*4+1],  i[6*4+1],  i[7*4+1]};
   assign q32[63:32] = {
       i[24*4+2], i[25*4+2], i[26*4+2], i[27*4+2], i[28*4+2], i[29*4+2], i[30*4+2], i[31*4+2],
       i[16*4+2], i[17*4+2], i[18*4+2], i[19*4+2], i[20*4+2], i[21*4+2], i[22*4+2], i[23*4+2],
       i[8*4+2],  i[9*4+2],  i[10*4+2], i[11*4+2], i[12*4+2], i[13*4+2], i[14*4+2], i[15*4+2],
       i[0*4+2],  i[1*4+2],  i[2*4+2],  i[3*4+2],  i[4*4+2],  i[5*4+2],  i[6*4+2],  i[7*4+2]};
   assign q32[31:0] = {
       i[24*4+3], i[25*4+3], i[26*4+3], i[27*4+3], i[28*4+3], i[29*4+3], i[30*4+3], i[31*4+3],
       i[16*4+3], i[17*4+3], i[18*4+3], i[19*4+3], i[20*4+3], i[21*4+3], i[22*4+3], i[23*4+3],
       i[8*4+3],  i[9*4+3],  i[10*4+3], i[11*4+3], i[12*4+3], i[13*4+3], i[14*4+3], i[15*4+3],
       i[0*4+3],  i[1*4+3],  i[2*4+3],  i[3*4+3],  i[4*4+3],  i[5*4+3],  i[6*4+3],  i[7*4+3]};

   always @ (posedge clk) begin
      if (cyc!=0) begin
         cyc <= cyc + 1;
`ifdef TEST_VERBOSE
         $write("%x %x\n", q1, i);
`endif
         if (cyc==1) begin
            i <= 128'hed388e646c843d35de489bab2413d770;
         end
         if (cyc==2) begin
            i <= 128'h0e17c88f3d5fe51a982646c8e2bd68c3;
            if (q1 != 128'h06f0b17c6551e269e3ab07723b26fb10) $stop;
            if (q1 != q32) $stop;
            if (q1 != q64) $stop;
            if (q1[63:0] != q64_low) $stop;
         end
         if (cyc==3) begin
            i <= 128'he236ddfddddbdad20a48e039c9f395b8;
            if (q1 != 128'h8c6f018c8a992c979a3e7859f29ac36d) $stop;
            if (q1 != q32) $stop;
            if (q1 != q64) $stop;
            if (q1[63:0] != q64_low) $stop;
         end
         if (cyc==4) begin
            i <= 128'h45e0eb7642b148537491f3da147e7f26;
            if (q1 != 128'hf45fc07e4fa8524cf9571425f17f9ad7) $stop;
            if (q1 != q32) $stop;
            if (q1 != q64) $stop;
            if (q1[63:0] != q64_low) $stop;
         end
         if (cyc==9) begin
            $write("*-* All Finished *-*\n");
            $finish;
         end
      end
   end
endmodule
