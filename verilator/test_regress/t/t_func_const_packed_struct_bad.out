%Warning-USERFATAL: "f_add = 15"
                    ... For warning description see https://verilator.org/warn/USERFATAL?v=latest
                    ... Use "/* verilator lint_off USERFATAL */" and lint_on around source to disable this message.
%Error: t/t_func_const_packed_struct_bad.v:14:21: Expecting expression to be constant, but can't determine constant for FUNCREF 'f_add2'
                                                : ... note: In instance 't'
        t/t_func_const_packed_struct_bad.v:25:9: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_func_const_packed_struct_bad.v:33:16: ... Called from 'f_add()' with parameters:
           params = [0 = '{a: 32'h7, b: 32'h22b}, 1 = '{a: 32'h3039, b: 32'h8}]
        t/t_func_const_packed_struct_bad.v:14:21: ... Called from 'f_add2()' with parameters:
           a = ?32?h7
           b = ?32?h8
           c = ?32?h9
   14 |    localparam P24 = f_add2(7, 8, 9);
      |                     ^~~~~~
%Error: Exiting due to
