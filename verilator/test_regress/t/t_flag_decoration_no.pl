#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(vlt => 1);

top_filename("t/t_flag_decoration.v");

compile(
    verilator_flags2 => ["--no-decoration"],
    );

file_grep("$Self->{obj_dir}/V$Self->{name}.h", qr!\n// CONSTRUCTORS!);
file_grep_not("$Self->{obj_dir}/V$Self->{name}.h", qr!\n    // CONSTRUCTORS!);
file_grep_not("$Self->{obj_dir}/V$Self->{name}.h", qr!/\*t/t_flag_decoration!);

ok(1);
1;
