// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2013 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/);

   function automatic string get_csr_name(input logic [11:0] csr_addr);
      // verilator no_inline_task
      unique case (csr_addr)
        12'd0000: return "xx0000xxxxx";
        12'd0001: return "xx0001xxxx";
        12'd1952: return "xx1952xxxxx";
        12'd1953: return "xx1953xxx1";
        12'd1954: return "xx1954xxx2";
        12'd1955: return "xx1955xxx3";
        12'd1968: return "xx1968xx";
        12'd1969: return "xx1969x";
        12'd1970: return "xx1970xxxxxx";
        12'd0256: return "xx0256xxxxx";
        12'd0258: return "xx0258xxxxx";
        12'd0259: return "xx0259xxxxx";
        12'd0260: return "xx0260x";
        12'd0261: return "xx0261xxx";
        12'd0262: return "xx0262xxxxxxxx";
        12'd2816: return "xx2816xxxx";
        12'd2818: return "xx2818xxxxxx";
        12'd2819: return "xx2819xxxxxxxxx3";
        12'd2820: return "xx2820xxxxxxxxx4";
        12'd2821: return "xx2821xxxxxxxxx5";
        12'd2822: return "xx2822xxxxxxxxx6";
        12'd2823: return "xx2823xxxxxxxxx7";
        12'd2824: return "xx2824xxxxxxxxx8";
        12'd2825: return "xx2825xxxxxxxxx9";
        12'd2826: return "xx2826xxxxxxxxx10";
        12'd2827: return "xx2827xxxxxxxxx11";
        12'd2828: return "xx2828xxxxxxxxx12";
        12'd2829: return "xx2829xxxxxxxxx13";
        12'd2830: return "xx2830xxxxxxxxx14";
        12'd2831: return "xx2831xxxxxxxxx15";
        12'd2832: return "xx2832xxxxxxxxx16";
        12'd2833: return "xx2833xxxxxxxxx17";
        12'd2834: return "xx2834xxxxxxxxx18";
        12'd2835: return "xx2835xxxxxxxxx19";
        12'd2836: return "xx2836xxxxxxxxx20";
        12'd2837: return "xx2837xxxxxxxxx21";
        12'd2838: return "xx2838xxxxxxxxx22";
        12'd2839: return "xx2839xxxxxxxxx23";
        12'd2840: return "xx2840xxxxxxxxx24";
        12'd2841: return "xx2841xxxxxxxxx25";
        12'd2842: return "xx2842xxxxxxxxx26";
        12'd2843: return "xx2843xxxxxxxxx27";
        12'd2844: return "xx2844xxxxxxxxx28";
        12'd2845: return "xx2845xxxxxxxxx29";
        12'd2846: return "xx2846xxxxxxxxx30";
        12'd2847: return "xx2847xxxxxxxxx31";
        12'd2944: return "xx2944xxxxx";
        12'd2946: return "xx2946xxxxxxx";
        12'd2947: return "xx2947xxxxxxxxx3x";
        12'd2948: return "xx2948xxxxxxxxx4x";
        12'd2949: return "xx2949xxxxxxxxx5x";
        12'd2950: return "xx2950xxxxxxxxx6x";
        12'd2951: return "xx2951xxxxxxxxx7x";
        12'd2952: return "xx2952xxxxxxxxx8x";
        12'd2953: return "xx2953xxxxxxxxx9x";
        12'd2954: return "xx2954xxxxxxxxx10x";
        12'd2955: return "xx2955xxxxxxxxx11x";
        12'd2956: return "xx2956xxxxxxxxx12x";
        12'd2957: return "xx2957xxxxxxxxx13x";
        12'd2958: return "xx2958xxxxxxxxx14x";
        12'd2959: return "xx2959xxxxxxxxx15x";
        12'd2960: return "xx2960xxxxxxxxx16x";
        12'd2961: return "xx2961xxxxxxxxx17x";
        12'd2962: return "xx2962xxxxxxxxx18x";
        12'd2963: return "xx2963xxxxxxxxx19x";
        12'd2964: return "xx2964xxxxxxxxx20x";
        12'd2965: return "xx2965xxxxxxxxx21x";
        12'd2966: return "xx2966xxxxxxxxx22x";
        12'd2967: return "xx2967xxxxxxxxx23x";
        12'd2968: return "xx2968xxxxxxxxx24x";
        12'd2969: return "xx2969xxxxxxxxx25x";
        12'd2970: return "xx2970xxxxxxxxx26x";
        12'd2971: return "xx2971xxxxxxxxx27x";
        12'd2972: return "xx2972xxxxxxxxx28x";
        12'd2973: return "xx2973xxxxxxxxx29x";
        12'd2974: return "xx2974xxxxxxxxx30x";
        12'd2975: return "xx2975xxxxxxxxx31x";
        12'd0002: return "xx0002x";
        12'd3072: return "xx3072xxx";
        12'd3073: return "xx3073xx";
        12'd3074: return "xx3074xxxxx";
        12'd3075: return "xx3075xxxxxxxx3";
        12'd3076: return "xx3076xxxxxxxx4";
        12'd3077: return "xx3077xxxxxxxx5";
        12'd3078: return "xx3078xxxxxxxx6";
        12'd3079: return "xx3079xxxxxxxx7";
        12'd3080: return "xx3080xxxxxxxx8";
        12'd3081: return "xx3081xxxxxxxx9";
        12'd3082: return "xx3082xxxxxxxx10";
        12'd3083: return "xx3083xxxxxxxx11";
        12'd3084: return "xx3084xxxxxxxx12";
        12'd3085: return "xx3085xxxxxxxx13";
        12'd3086: return "xx3086xxxxxxxx14";
        12'd3087: return "xx3087xxxxxxxx15";
        12'd3088: return "xx3088xxxxxxxx16";
        12'd3089: return "xx3089xxxxxxxx17";
        12'd3090: return "xx3090xxxxxxxx18";
        12'd3091: return "xx3091xxxxxxxx19";
        12'd3092: return "xx3092xxxxxxxx20";
        12'd3093: return "xx3093xxxxxxxx21";
        12'd3094: return "xx3094xxxxxxxx22";
        12'd3095: return "xx3095xxxxxxxx23";
        12'd3096: return "xx3096xxxxxxxx24";
        12'd3097: return "xx3097xxxxxxxx25";
        12'd3098: return "xx3098xxxxxxxx26";
        12'd3099: return "xx3099xxxxxxxx27";
        12'd3100: return "xx3100xxxxxxxx28";
        12'd3101: return "xx3101xxxxxxxx29";
        12'd3102: return "xx3102xxxxxxxx30";
        12'd3103: return "xx3103xxxxxxxx31";
        12'd3200: return "xx3200xxxx";
        12'd3201: return "xx3201xxx";
        12'd3202: return "xx3202xxxxxx";
        12'd3203: return "xx3203xxxxxxxx3x";
        12'd3204: return "xx3204xxxxxxxx4x";
        12'd3205: return "xx3205xxxxxxxx5x";
        12'd3206: return "xx3206xxxxxxxx6x";
        12'd3207: return "xx3207xxxxxxxx7x";
        12'd3208: return "xx3208xxxxxxxx8x";
        12'd3209: return "xx3209xxxxxxxx9x";
        12'd0320: return "xx0320xxxxxx";
        12'd3210: return "xx3210xxxxxxxx10x";
        12'd3211: return "xx3211xxxxxxxx11x";
        12'd3212: return "xx3212xxxxxxxx12x";
        12'd3213: return "xx3213xxxxxxxx13x";
        12'd3214: return "xx3214xxxxxxxx14x";
        12'd3215: return "xx3215xxxxxxxx15x";
        12'd3216: return "xx3216xxxxxxxx16x";
        12'd3217: return "xx3217xxxxxxxx17x";
        12'd3218: return "xx3218xxxxxxxx18x";
        12'd3219: return "xx3219xxxxxxxx19x";
        12'd3220: return "xx3220xxxxxxxx20x";
        12'd3221: return "xx3221xxxxxxxx21x";
        12'd3222: return "xx3222xxxxxxxx22x";
        12'd3223: return "xx3223xxxxxxxx23x";
        12'd3224: return "xx3224xxxxxxxx24x";
        12'd3225: return "xx3225xxxxxxxx25x";
        12'd3226: return "xx3226xxxxxxxx26x";
        12'd3227: return "xx3227xxxxxxxx27x";
        12'd3228: return "xx3228xxxxxxxx28x";
        12'd3229: return "xx3229xxxxxxxx29x";
        12'd3230: return "xx3230xxxxxxxx30x";
        12'd3231: return "xx3231xxxxxxxx31x";
        12'd3857: return "xx3857xxxxxxx";
        12'd3858: return "xx3858xxxxx";
        12'd3859: return "xx3859xxxx";
        12'd3860: return "xx3860xxxxx";
        12'd0512: return "xx0512xxxxx";
        12'd0514: return "xx0514xxxxx";
        12'd0515: return "xx0515xxxxx";
        12'd0516: return "xx0516x";
        12'd0517: return "xx0517xxx";
        12'd0576: return "xx0576xxxxxx";
        12'd0577: return "xx0577xx";
        12'd0578: return "xx0578xxxx";
        12'd0579: return "xx0579xxxxxx";
        12'd0580: return "xx0580x";
        12'd0768: return "xx0768xxxxx";
        12'd0769: return "xx0769xx";
        12'd0770: return "xx0770xxxxx";
        12'd0771: return "xx0771xxxxx";
        12'd0772: return "xx0772x";
        12'd0773: return "xx0773xxx";
        12'd0774: return "xx0774xxxxxxxx";
        12'd0800: return "xx0800xxxxxxxxxxx";
        12'd0803: return "xx0803xxxxxxx3";
        12'd0804: return "xx0804xxxxxxx4";
        12'd0805: return "xx0805xxxxxxx5";
        12'd0806: return "xx0806xxxxxxx6";
        12'd0807: return "xx0807xxxxxxx7";
        12'd0808: return "xx0808xxxxxxx8";
        12'd0809: return "xx0809xxxxxxx9";
        12'd0810: return "xx0810xxxxxxx10";
        12'd0811: return "xx0811xxxxxxx11";
        12'd0812: return "xx0812xxxxxxx12";
        12'd0813: return "xx0813xxxxxxx13";
        12'd0814: return "xx0814xxxxxxx14";
        12'd0815: return "xx0815xxxxxxx15";
        12'd0816: return "xx0816xxxxxxx16";
        12'd0817: return "xx0817xxxxxxx17";
        12'd0818: return "xx0818xxxxxxx18";
        12'd0819: return "xx0819xxxxxxx19";
        12'd0820: return "xx0820xxxxxxx20";
        12'd0821: return "xx0821xxxxxxx21";
        12'd0822: return "xx0822xxxxxxx22";
        12'd0823: return "xx0823xxxxxxx23";
        12'd0824: return "xx0824xxxxxxx24";
        12'd0825: return "xx0825xxxxxxx25";
        12'd0826: return "xx0826xxxxxxx26";
        12'd0827: return "xx0827xxxxxxx27";
        12'd0828: return "xx0828xxxxxxx28";
        12'd0829: return "xx0829xxxxxxx29";
        12'd0830: return "xx0830xxxxxxx30";
        12'd0831: return "xx0831xxxxxxx31";
        12'd0832: return "xx0832xxxxxx";
        12'd0833: return "xx0833xx";
        12'd0834: return "xx0834xxxx";
        12'd0835: return "xx0835xxx";
        12'd0836: return "xx0836x";
        12'd0896: return "xx0896xxx";
        12'd0897: return "xx0897xxxx";
        12'd0898: return "xx0898xxxx";
        12'd0899: return "xx0899xxxxx";
        12'd0900: return "xx0900xxxx";
        12'd0901: return "xx0901xxxxx";
        12'd0928: return "xx0928xxxx0";
        12'd0929: return "xx0929xxxx1";
        12'd0930: return "xx0930xxxx2";
        12'd0931: return "xx0931xxxx3";
        12'd0944: return "xx0944xxxxx0";
        12'd0945: return "xx0945xxxxx1";
        12'd0946: return "xx0946xxxxx2";
        12'd0947: return "xx0947xxxxx3";
        12'd0948: return "xx0948xxxxx4";
        12'd0949: return "xx0949xxxxx5";
        12'd0950: return "xx0950xxxxx6";
        12'd0951: return "xx0951xxxxx7";
        12'd0952: return "xx0952xxxxx8";
        12'd0953: return "xx0953xxxxx9";
        12'd0954: return "xx0954xxxxx10";
        12'd0955: return "xx0955xxxxx11";
        12'd0956: return "xx0956xxxxx12";
        12'd0957: return "xx0957xxxxx13";
        12'd0958: return "xx0958xxxxx14";
        12'd0959: return "xx0959xxxxx15";
        default: return $sformatf("0x%x", csr_addr);
      endcase
   endfunction

   int i;

   initial begin
      if (get_csr_name(12'd0957) != "xx0957xxxxx13") $stop;
      if (get_csr_name(12'd2830) != "xx2830xxxxxxxxx14") $stop;

      $write("*-* All Finished *-*\n");
      $finish;
   end

endmodule
