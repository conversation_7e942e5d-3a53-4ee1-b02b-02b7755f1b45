`define DEF_A0
`define PREDEF_COMMAND_LINE
`define SV_COV_ASSERTION 20
`define SV_COV_CHECK 3
`define SV_COV_ERROR -1
`define SV_COV_FSM_STATE 21
`define SV_COV_HIER 11
`define SV_COV_MODULE 10
`define SV_COV_NOCOV 0
`define SV_COV_OK 1
`define SV_COV_OVERFLOW -2
`define SV_COV_PARTIAL 2
`define SV_COV_RESET 2
`define SV_COV_START 0
`define SV_COV_STATEMENT 22
`define SV_COV_STOP 1
`define SV_COV_TOGGLE 23
`define SYSTEMVERILOG 1
`define TEST_DUMPFILE obj_vlt/t_preproc_defines/simx.vcd
`define TEST_OBJ_DIR obj_vlt/t_preproc_defines
`define VERILATOR 1
`define WITH_ARG(a) (a)(a)
`define a x,y
`define bar(a, b) test a b
`define baz(a, b) test``a``b
`define coverage_block_off /*verilator coverage_block_off*/
`define foo test
`define quux(x) `qux(`"x`")
`define qux(x) string boo = x;
`define verilator 1
`define verilator3 1
