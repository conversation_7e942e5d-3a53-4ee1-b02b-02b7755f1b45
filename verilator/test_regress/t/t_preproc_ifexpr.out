`begin_keywords "1800-2023"
  "ok  ( ONE )"
  "ok   ( ! ONE )"
  "ok  ( ! ZERO )"
  "ok  ( ZERO || ZERO || ONE )"
  "ok  ( ONE && ONE && ONE )"
  "ok  ( ZERO && ZERO || ONE )"
  "ok  ( ZERO -> ZERO)"
  "ok  ( ZERO -> ONE)"
  "ok  ( ZERO -> ONE)"
  "ok  ( ZERO -> ONE)"
  "ok  ( ONE -> ZERO)"
  "ok  ( ONE -> ONE)"
  "ok  ( ZERO <-> ZERO)"
  "ok  ( ZERO <-> ONE)"
  "ok  ( ONE <-> ZERO)"
  "ok  ( ONE <-> ONE)"
  "ok "
Line: 117
