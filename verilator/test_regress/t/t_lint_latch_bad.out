%Warning-COMBDLY: t/t_lint_latch_bad.v:18:10: Non-blocking assignment '<=' in combinational logic process
                                            : ... This will be executed as a blocking assignment '='!
   18 |       bl <= a;   
      |          ^~
                  ... For warning description see https://verilator.org/warn/COMBDLY?v=latest
                  ... Use "/* verilator lint_off COMBDLY */" and lint_on around source to disable this message.
                  *** See https://verilator.org/warn/COMBDLY before disabling this,
                  else you may end up with different sim results.
%Warning-NOLATCH: t/t_lint_latch_bad.v:17:4: No latches detected in always_latch block
   17 |    always_latch begin
      |    ^~~~~~~~~~~~
%Warning-COMBDLY: t/t_lint_latch_bad.v:25:10: Non-blocking assignment '<=' in combinational logic process
                                            : ... This will be executed as a blocking assignment '='!
   25 |       bc <= a;   
      |          ^~
%Error: Exiting due to
