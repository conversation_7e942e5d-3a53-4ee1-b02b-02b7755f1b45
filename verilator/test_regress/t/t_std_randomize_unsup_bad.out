%Warning-CONSTRAINTIGN: t/t_std_randomize_unsup_bad.v:10:16: std::randomize ignored (unsupported)
                                                           : ... note: In instance 't'
   10 |       if (std::randomize(a, b) != 1) $stop;
      |                ^~~~~~~~~
                        ... For warning description see https://verilator.org/warn/CONSTRAINTIGN?v=latest
                        ... Use "/* verilator lint_off CONSTRAINTIGN */" and lint_on around source to disable this message.
%Warning-CONSTRAINTIGN: t/t_std_randomize_unsup_bad.v:11:16: std::randomize ignored (unsupported)
                                                           : ... note: In instance 't'
   11 |       if (std::randomize(a, b) with { 2 < a; a < 7; b < a; } != 1) $stop;
      |                ^~~~~~~~~
%Error: Exiting due to
