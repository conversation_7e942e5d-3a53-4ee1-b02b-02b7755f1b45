#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(simulator => 1);

top_filename("t_func_wide_out.v");

compile(
    verilator_flags2 => ["-Wno-WIDTHTRUNC"],
    v_flags2 => ["+define+T_FUNC_WIDE_OUT_NOINL +define+TEST_NOINLINE t/t_func_wide_out_c.cpp"],
    );

execute(
    check_finished => 1,
    );

ok(1);
1;
