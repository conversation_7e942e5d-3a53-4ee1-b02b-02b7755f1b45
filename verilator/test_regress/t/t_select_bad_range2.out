%Warning-SELRANGE: t/t_select_bad_range2.v:51:21: Selection index out of range: 3:2 outside 1:0
                                                : ... note: In instance 't.test'
   51 |    assign out32 = in[3:2];
      |                     ^
                   ... For warning description see https://verilator.org/warn/SELRANGE?v=latest
                   ... Use "/* verilator lint_off SELRANGE */" and lint_on around source to disable this message.
%Error: Exiting due to
