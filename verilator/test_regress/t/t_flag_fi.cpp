// -*- mode: C++; c-file-style: "cc-mode" -*-
//
// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2017 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

#include <verilated.h>

#include VM_PREFIX_INCLUDE

//======================================================================

unsigned int main_time = 0;

double sc_time_stamp() { return main_time; }

VM_PREFIX* topp = nullptr;
bool gotit = false;

void myfunction() { gotit = true; }

int main(int argc, char* argv[]) {
    Verilated::debug(0);
    Verilated::commandArgs(argc, argv);

    topp = new VM_PREFIX;

    topp->eval();
    if (!gotit) vl_fatal(__FILE__, __LINE__, "dut", "Never got call to myfunction");

    topp->final();
    VL_DO_DANGLING(delete topp, topp);

    return 0;
}
