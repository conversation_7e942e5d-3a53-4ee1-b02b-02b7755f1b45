{"type": "NETLIST", "name": "$root", "addr": "(B)", "loc": "a,0:0,0:0", "timeunit": "1ps", "timeprecision": "1ps", "typeTablep": "(C)", "constPoolp": "(D)", "dollarUnitPkgp": "UNLINKED", "stdPackagep": "UNLINKED", "evalp": "(E)", "evalNbap": "(F)", "dpiExportTriggerp": "UNLINKED", "delaySchedulerp": "UNLINKED", "nbaEventp": "UNLINKED", "nbaEventTriggerp": "UNLINKED", "topScopep": "(G)", "modulesp": [{"type": "MODULE", "name": "$root", "addr": "(H)", "loc": "f,7:8,7:9", "origName": "$root", "level": 1, "modPublic": true, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "CLK", "addr": "(I)", "loc": "f,8:17,8:20", "dtypep": "(J)", "origName": "CLK", "isSc": false, "isPrimaryIO": true, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": true, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "WIRE", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "RESET", "addr": "(K)", "loc": "f,9:17,9:22", "dtypep": "(J)", "origName": "RESET", "isSc": false, "isPrimaryIO": true, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlFirstIteration", "addr": "(L)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VstlFirstIteration", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__Vtrigprevexpr___TOP__CLK__0", "addr": "(N)", "loc": "f,7:8,7:9", "dtypep": "(J)", "origName": "__Vtrigprevexpr___TOP__CLK__0", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactContinue", "addr": "(O)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VactContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactIterCount", "addr": "(P)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "origName": "__VactIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__Vm_traceActivity", "addr": "(R)", "loc": "f,7:8,7:9", "dtypep": "(S)", "origName": "__Vm_traceActivity", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlTriggered", "addr": "(T)", "loc": "f,7:8,7:9", "dtypep": "(U)", "origName": "__VstlTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactTriggered", "addr": "(V)", "loc": "f,7:8,7:9", "dtypep": "(W)", "origName": "__VactTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaTriggered", "addr": "(X)", "loc": "f,7:8,7:9", "dtypep": "(W)", "origName": "__VnbaTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CELL", "name": "t", "addr": "(Y)", "loc": "f,7:8,7:9", "origName": "t", "recursive": false, "modp": "(Z)", "pinsp": [], "paramsp": [], "rangep": [], "intfRefsp": []}, {"type": "TOPSCOPE", "name": "", "addr": "(G)", "loc": "f,7:8,7:9", "senTreesp": [], "scopep": [{"type": "SCOPE", "name": "TOP", "addr": "(AB)", "loc": "f,7:8,7:9", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(H)", "varsp": [], "blocksp": []}]}, {"type": "CFUNC", "name": "trace_init_sub__TOP__0", "addr": "(BB)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(CB)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(DB)", "loc": "f,7:8,7:9", "shortText": "const int c = vlSymsp->__Vm_baseCode;..."}]}], "stmtsp": [{"type": "TRACEPUSHPREFIX", "name": "", "addr": "(EB)", "loc": "f,7:8,7:9"}, {"type": "STMTEXPR", "name": "", "addr": "(FB)", "loc": "f,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(GB)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "funcName": "trace_init_sub__TOP__t__0", "funcp": "(IB)", "argsp": []}]}, {"type": "TRACEPOPPREFIX", "name": "", "addr": "(JB)", "loc": "f,7:8,7:9"}, {"type": "TRACEDECL", "name": "CLK", "addr": "(KB)", "loc": "f,8:17,8:20", "dtypep": "(J)", "code": 19, "valuep": []}, {"type": "TRACEDECL", "name": "RESET", "addr": "(LB)", "loc": "f,9:17,9:22", "dtypep": "(J)", "code": 20, "valuep": []}], "finalsp": []}, {"type": "CFUNC", "name": "trace_init_sub__TOP__t__0", "addr": "(IB)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(MB)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(NB)", "loc": "f,7:8,7:9", "shortText": "const int c = vlSymsp->__Vm_baseCode;..."}]}], "stmtsp": [{"type": "TRACEDECL", "name": "CLK", "addr": "(OB)", "loc": "f,8:17,8:20", "dtypep": "(J)", "code": 19, "valuep": []}, {"type": "TRACEDECL", "name": "RESET", "addr": "(PB)", "loc": "f,9:17,9:22", "dtypep": "(J)", "code": 1, "valuep": []}, {"type": "TRACEPUSHPREFIX", "name": "", "addr": "(QB)", "loc": "f,14:9,14:13"}, {"type": "STMTEXPR", "name": "", "addr": "(RB)", "loc": "f,14:9,14:13", "exprp": [{"type": "CCALL", "name": "", "addr": "(SB)", "loc": "f,14:9,14:13", "dtypep": "(HB)", "funcName": "trace_init_sub__TOP__t__glbl__0", "funcp": "(TB)", "argsp": []}]}, {"type": "TRACEPOPPREFIX", "name": "", "addr": "(UB)", "loc": "f,14:9,14:13"}, {"type": "TRACEPUSHPREFIX", "name": "", "addr": "(VB)", "loc": "f,17:20,17:21"}, {"type": "TRACEDECL", "name": "", "addr": "(WB)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "code": 2, "valuep": []}, {"type": "TRACEPOPPREFIX", "name": "", "addr": "(YB)", "loc": "f,17:20,17:21"}, {"type": "TRACEDECL", "name": "val", "addr": "(ZB)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "code": 4, "valuep": []}, {"type": "TRACEPUSHPREFIX", "name": "", "addr": "(BC)", "loc": "f,7:8,7:9"}, {"type": "TRACEDECL", "name": "clk", "addr": "(CC)", "loc": "f,68:10,68:13", "dtypep": "(J)", "code": 19, "valuep": []}, {"type": "TRACEDECL", "name": "i8", "addr": "(DC)", "loc": "f,72:14,72:16", "dtypep": "(EC)", "code": 5, "valuep": []}, {"type": "TRACEDECL", "name": "i48", "addr": "(FC)", "loc": "f,73:15,73:18", "dtypep": "(GC)", "code": 6, "valuep": []}, {"type": "TRACEDECL", "name": "i128", "addr": "(HC)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "code": 8, "valuep": []}, {"type": "TRACEPOPPREFIX", "name": "", "addr": "(JC)", "loc": "f,7:8,7:9"}, {"type": "TRACEPUSHPREFIX", "name": "", "addr": "(KC)", "loc": "f,7:8,7:9"}, {"type": "TRACEDECL", "name": "clk", "addr": "(LC)", "loc": "f,53:10,53:13", "dtypep": "(J)", "code": 19, "valuep": []}, {"type": "TRACEDECL", "name": "i8", "addr": "(MC)", "loc": "f,56:15,56:17", "dtypep": "(NC)", "code": 12, "valuep": []}, {"type": "TRACEDECL", "name": "i48", "addr": "(OC)", "loc": "f,57:17,57:20", "dtypep": "(PC)", "code": 13, "valuep": []}, {"type": "TRACEDECL", "name": "i128", "addr": "(QC)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "code": 15, "valuep": []}, {"type": "TRACEPOPPREFIX", "name": "", "addr": "(SC)", "loc": "f,7:8,7:9"}], "finalsp": []}, {"type": "CFUNC", "name": "trace_init_sub__TOP__t__glbl__0", "addr": "(TB)", "loc": "f,14:9,14:13", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(TC)", "loc": "f,14:9,14:13", "exprsp": [{"type": "TEXT", "name": "", "addr": "(UC)", "loc": "f,14:9,14:13", "shortText": "const int c = vlSymsp->__Vm_baseCode;..."}]}], "stmtsp": [{"type": "TRACEDECL", "name": "GSR", "addr": "(VC)", "loc": "f,47:8,47:11", "dtypep": "(J)", "code": 21, "valuep": []}], "finalsp": []}, {"type": "CFUNC", "name": "trace_init_top", "addr": "(WC)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(XC)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(YC)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "trace_init_sub__TOP__0", "funcp": "(BB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_static", "addr": "(ZC)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(AD)", "loc": "f,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(BD)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "funcName": "_eval_static__TOP__t", "funcp": "(CD)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(DD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(FD)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(GD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(HD)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(ID)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(KD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(LD)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(MD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(ND)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(OD)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial", "addr": "(PD)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(QD)", "loc": "f,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(RD)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "funcName": "_eval_initial__TOP__t", "funcp": "(SD)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(TD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(UD)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(VD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(WD)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(XD)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(YD)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(ZD)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(AE)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(BE)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(CE)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(DE)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "rhsp": [{"type": "VARREF", "name": "CLK", "addr": "(EE)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__CLK__0", "addr": "(FE)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_final", "addr": "(GE)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_eval_settle", "addr": "(HE)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VstlIterCount", "addr": "(IE)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "origName": "__VstlIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlContinue", "addr": "(JE)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VstlContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(KE)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(LE)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "lhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(ME)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(IE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NE)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(OE)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(PE)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(QE)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(RE)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(SE)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(JE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(TE)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(UE)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(JE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(VE)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(WE)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(XE)", "loc": "a,0:0,0:0", "dtypep": "(JD)"}], "rhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(YE)", "loc": "a,0:0,0:0", "dtypep": "(Q)", "access": "RD", "varp": "(IE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(ZE)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(AF)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(BF)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(CF)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_dump_triggers__stl", "funcp": "(DF)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(EF)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(FF)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_trace_public.v\", 7, \"\", "}, {"type": "TEXT", "name": "", "addr": "(GF)", "loc": "a,0:0,0:0", "shortText": "\"Settle region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(HF)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "ADD", "name": "", "addr": "(IF)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(JF)", "loc": "f,7:8,7:9", "dtypep": "(JD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(KF)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(LF)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "RD", "varp": "(IE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(MF)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(IE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(OF)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(PF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(JE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(QF)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(RF)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "funcName": "_eval_phase__stl", "funcp": "(SF)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(TF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(UF)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(VF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(JE)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(WF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(XF)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(YF)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "incsp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_triggers__stl", "addr": "(ZF)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(AG)", "loc": "f,7:8,7:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(BG)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(CG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(DG)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}, {"type": "CCAST", "name": "", "addr": "(EG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(FG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}, {"type": "TEXTBLOCK", "name": "", "addr": "(GG)", "loc": "f,7:8,7:9", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(HG)", "loc": "f,7:8,7:9", "shortText": "#ifdef VL_DEBUG..."}, {"type": "TEXT", "name": "", "addr": "(IG)", "loc": "f,7:8,7:9", "shortText": "if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {..."}, {"type": "STMTEXPR", "name": "", "addr": "(JG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(KG)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_dump_triggers__stl", "funcp": "(DF)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(LG)", "loc": "f,7:8,7:9", "shortText": "}..."}, {"type": "TEXT", "name": "", "addr": "(MG)", "loc": "f,7:8,7:9", "shortText": "#endif..."}]}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__stl", "addr": "(DF)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(NG)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(OG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(PG)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(QG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(RG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(SG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(TG)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(UG)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(VG)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(WG)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(YG)", "loc": "f,7:8,7:9", "dtypep": "(XG)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(ZG)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(BH)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(CH)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(DH)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         'stl' region trigger index 0 is active: Internal 'stl' trigger - first iteration\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_stl", "addr": "(EH)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(FH)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(GH)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(HH)", "loc": "f,7:8,7:9", "dtypep": "(XG)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(IH)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(JH)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(KH)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(LH)", "loc": "f,9:17,9:22", "exprp": [{"type": "CCALL", "name": "", "addr": "(MH)", "loc": "f,9:17,9:22", "dtypep": "(HB)", "funcName": "_nba_sequent__TOP__0", "funcp": "(NH)", "argsp": []}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__stl", "addr": "(SF)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VstlExecute", "addr": "(OH)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VstlExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(PH)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(QH)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_eval_triggers__stl", "funcp": "(ZF)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(RH)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(SH)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(TH)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(UH)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(OH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(VH)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(WH)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(OH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(XH)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(YH)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_eval_stl", "funcp": "(EH)", "argsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(ZH)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(AI)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(OH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_triggers__act", "addr": "(BI)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(CI)", "loc": "f,7:8,7:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(DI)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(EI)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(FI)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}, {"type": "AND", "name": "", "addr": "(GI)", "loc": "f,20:14,20:21", "dtypep": "(ED)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(HI)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "VARREF", "name": "CLK", "addr": "(II)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "rhsp": [{"type": "NOT", "name": "", "addr": "(JI)", "loc": "f,20:14,20:21", "dtypep": "(ED)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(KI)", "loc": "f,20:14,20:21", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__CLK__0", "addr": "(LI)", "loc": "f,20:14,20:21", "dtypep": "(ED)", "access": "RD", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}]}]}, {"type": "ASSIGN", "name": "", "addr": "(MI)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "rhsp": [{"type": "VARREF", "name": "CLK", "addr": "(NI)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__CLK__0", "addr": "(OI)", "loc": "f,20:22,20:25", "dtypep": "(ED)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "TEXTBLOCK", "name": "", "addr": "(PI)", "loc": "f,7:8,7:9", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(QI)", "loc": "f,7:8,7:9", "shortText": "#ifdef VL_DEBUG..."}, {"type": "TEXT", "name": "", "addr": "(RI)", "loc": "f,7:8,7:9", "shortText": "if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {..."}, {"type": "STMTEXPR", "name": "", "addr": "(SI)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(TI)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_dump_triggers__act", "funcp": "(UI)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(VI)", "loc": "f,7:8,7:9", "shortText": "}..."}, {"type": "TEXT", "name": "", "addr": "(WI)", "loc": "f,7:8,7:9", "shortText": "#endif..."}]}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__act", "addr": "(UI)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(XI)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(YI)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(ZI)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(AJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(BJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(CJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(DJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(EJ)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(FJ)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(GJ)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(HJ)", "loc": "f,7:8,7:9", "dtypep": "(XG)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(IJ)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(JJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(KJ)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(LJ)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         'act' region trigger index 0 is active: @(posedge CLK)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__nba", "addr": "(MJ)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(NJ)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(OJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(PJ)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(QJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(RJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(SJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(TJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(UJ)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(VJ)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(WJ)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(XJ)", "loc": "f,7:8,7:9", "dtypep": "(XG)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(YJ)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(ZJ)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(AK)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(BK)", "loc": "f,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         'nba' region trigger index 0 is active: @(posedge CLK)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_act", "addr": "(CK)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_nba_sequent__TOP__0", "addr": "(NH)", "loc": "f,9:17,9:22", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGNW", "name": "", "addr": "(DK)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "rhsp": [{"type": "VARREF", "name": "RESET", "addr": "(EK)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "RD", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "RESET", "addr": "(GK)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "WR", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": [], "strengthSpecp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_nba", "addr": "(F)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(HK)", "loc": "f,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(IK)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(JK)", "loc": "f,7:8,7:9", "dtypep": "(XG)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(KK)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(LK)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(MK)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(NK)", "loc": "f,80:7,80:11", "exprp": [{"type": "CCALL", "name": "", "addr": "(OK)", "loc": "f,80:7,80:11", "dtypep": "(HB)", "funcName": "_nba_sequent__TOP__t__0", "funcp": "(PK)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(QK)", "loc": "f,80:7,80:11", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(RK)", "loc": "f,80:7,80:11", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(SK)", "loc": "f,80:7,80:11", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(TK)", "loc": "f,80:7,80:11", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(UK)", "loc": "f,80:7,80:11", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "STMTEXPR", "name": "", "addr": "(VK)", "loc": "f,9:17,9:22", "exprp": [{"type": "CCALL", "name": "", "addr": "(WK)", "loc": "f,9:17,9:22", "dtypep": "(HB)", "funcName": "_nba_sequent__TOP__0", "funcp": "(NH)", "argsp": []}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__act", "addr": "(XK)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VpreTriggered", "addr": "(YK)", "loc": "f,7:8,7:9", "dtypep": "(W)", "origName": "__VpreTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactExecute", "addr": "(ZK)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VactExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(AL)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(BL)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_eval_triggers__act", "funcp": "(BI)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(CL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(DL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(EL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(FL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(ZK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(GL)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(HL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(ZK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(IL)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "andNot", "addr": "(JL)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "fromp": [{"type": "VARREF", "name": "__VpreTriggered", "addr": "(KL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(YK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(LL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}, {"type": "VARREF", "name": "__VnbaTriggered", "addr": "(ML)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(NL)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "thisOr", "addr": "(OL)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(PL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(QL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(RL)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(SL)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_eval_act", "funcp": "(CK)", "argsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(TL)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(UL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(ZK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__nba", "addr": "(VL)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaExecute", "addr": "(WL)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VnbaExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(XL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(YL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(ZL)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(AM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(WL)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(BM)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(CM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(WL)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(DM)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(EM)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_eval_nba", "funcp": "(F)", "argsp": []}]}, {"type": "STMTEXPR", "name": "", "addr": "(FM)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "clear", "addr": "(GM)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(HM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "WR", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(IM)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(JM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(WL)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval", "addr": "(E)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaIterCount", "addr": "(KM)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "origName": "__VnbaIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaContinue", "addr": "(LM)", "loc": "f,7:8,7:9", "dtypep": "(M)", "origName": "__VnbaContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(MM)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(NM)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(OM)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(KM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(PM)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(QM)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(RM)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(LM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(SM)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(TM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(LM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(UM)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(VM)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(WM)", "loc": "a,0:0,0:0", "dtypep": "(JD)"}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(XM)", "loc": "a,0:0,0:0", "dtypep": "(Q)", "access": "RD", "varp": "(KM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(YM)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(ZM)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(AN)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(BN)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_dump_triggers__nba", "funcp": "(MJ)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(CN)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(DN)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_trace_public.v\", 7, \"\", "}, {"type": "TEXT", "name": "", "addr": "(EN)", "loc": "a,0:0,0:0", "shortText": "\"NBA region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(FN)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "ADD", "name": "", "addr": "(GN)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(HN)", "loc": "f,7:8,7:9", "dtypep": "(JD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(IN)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(JN)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "RD", "varp": "(KM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(KN)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(KM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(LN)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(MN)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(NN)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(LM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(ON)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(PN)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(QN)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(RN)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(SN)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(TN)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(UN)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(VN)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "access": "RD", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(WN)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(XN)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(YN)", "loc": "a,0:0,0:0", "dtypep": "(JD)"}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(ZN)", "loc": "a,0:0,0:0", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(AO)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(BO)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(CO)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(DO)", "loc": "a,0:0,0:0", "dtypep": "(HB)", "funcName": "_dump_triggers__act", "funcp": "(UI)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(EO)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(FO)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_trace_public.v\", 7, \"\", "}, {"type": "TEXT", "name": "", "addr": "(GO)", "loc": "a,0:0,0:0", "shortText": "\"Active region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(HO)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "rhsp": [{"type": "ADD", "name": "", "addr": "(IO)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(JO)", "loc": "f,7:8,7:9", "dtypep": "(JD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(KO)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(LO)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(MO)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "access": "WR", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(OO)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(PO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(QO)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(RO)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "funcName": "_eval_phase__act", "funcp": "(XK)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(SO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(TO)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(UO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}, {"type": "IF", "name": "", "addr": "(VO)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(WO)", "loc": "a,0:0,0:0", "dtypep": "(ED)", "funcName": "_eval_phase__nba", "funcp": "(VL)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(XO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(YO)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(ZO)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "access": "WR", "varp": "(LM)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}], "finalsp": []}, {"type": "CFUNC", "name": "trace_register", "addr": "(AP)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "TEXT", "name": "", "addr": "(BP)", "loc": "f,7:8,7:9", "shortText": "tracep->addConstCb("}, {"type": "ADDROFCFUNC", "name": "", "addr": "(CP)", "loc": "f,7:8,7:9", "dtypep": "(DP)"}, {"type": "TEXT", "name": "", "addr": "(EP)", "loc": "f,7:8,7:9", "shortText": ", "}, {"type": "CONST", "name": "32'h0", "addr": "(FP)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}, {"type": "TEXT", "name": "", "addr": "(GP)", "loc": "f,7:8,7:9", "shortText": ", vlSelf);..."}, {"type": "TEXT", "name": "", "addr": "(HP)", "loc": "f,7:8,7:9", "shortText": "tracep->addFullCb("}, {"type": "ADDROFCFUNC", "name": "", "addr": "(IP)", "loc": "f,7:8,7:9", "dtypep": "(DP)"}, {"type": "TEXT", "name": "", "addr": "(JP)", "loc": "f,7:8,7:9", "shortText": ", "}, {"type": "CONST", "name": "32'h0", "addr": "(KP)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}, {"type": "TEXT", "name": "", "addr": "(LP)", "loc": "f,7:8,7:9", "shortText": ", vlSelf);..."}, {"type": "TEXT", "name": "", "addr": "(MP)", "loc": "f,7:8,7:9", "shortText": "tracep->addChgCb("}, {"type": "ADDROFCFUNC", "name": "", "addr": "(NP)", "loc": "f,7:8,7:9", "dtypep": "(DP)"}, {"type": "TEXT", "name": "", "addr": "(OP)", "loc": "f,7:8,7:9", "shortText": ", "}, {"type": "CONST", "name": "32'h0", "addr": "(PP)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}, {"type": "TEXT", "name": "", "addr": "(QP)", "loc": "f,7:8,7:9", "shortText": ", vlSelf);..."}, {"type": "TEXT", "name": "", "addr": "(RP)", "loc": "f,7:8,7:9", "shortText": "tracep->addCleanupCb("}, {"type": "ADDROFCFUNC", "name": "", "addr": "(SP)", "loc": "f,7:8,7:9", "dtypep": "(DP)"}, {"type": "TEXT", "name": "", "addr": "(TP)", "loc": "f,7:8,7:9", "shortText": ", vlSelf);..."}], "finalsp": []}, {"type": "CFUNC", "name": "trace_const_0", "addr": "(UP)", "loc": "f,7:8,7:9", "slow": true, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(VP)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(WP)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vt_trace_public_sig_vlt___024root*>(voidSelf);..."}]}, {"type": "CSTMT", "name": "", "addr": "(XP)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(YP)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;..."}]}], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "trace_full_0", "addr": "(ZP)", "loc": "f,7:8,7:9", "slow": true, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(AQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(BQ)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vt_trace_public_sig_vlt___024root*>(voidSelf);..."}]}, {"type": "CSTMT", "name": "", "addr": "(CQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(DQ)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;..."}]}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(EQ)", "loc": "f,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(FQ)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "funcName": "trace_full_0_sub_0", "funcp": "(GQ)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "trace_chg_0", "addr": "(HQ)", "loc": "f,7:8,7:9", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(IQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(JQ)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vt_trace_public_sig_vlt___024root*>(voidSelf);..."}]}, {"type": "CSTMT", "name": "", "addr": "(KQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(LQ)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;..."}]}, {"type": "CSTMT", "name": "", "addr": "(MQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(NQ)", "loc": "f,7:8,7:9", "shortText": "if (VL_UNLIKELY(!vlSymsp->__Vm_activity)) return;..."}]}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(OQ)", "loc": "f,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(PQ)", "loc": "f,7:8,7:9", "dtypep": "(HB)", "funcName": "trace_chg_0_sub_0", "funcp": "(QQ)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "trace_full_0_sub_0", "addr": "(GQ)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(RQ)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(SQ)", "loc": "f,7:8,7:9", "shortText": "uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode);..."}]}], "stmtsp": [{"type": "TRACEINC", "name": "", "addr": "(TQ)", "loc": "f,9:17,9:22", "dtypep": "(J)", "declp": "(PB)", "valuep": [{"type": "VARREF", "name": "RESET", "addr": "(UQ)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "RD", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(VQ)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "declp": "(WB)", "valuep": [{"type": "VARREF", "name": "vec", "addr": "(WQ)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "access": "RD", "varp": "(XQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(YQ)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "declp": "(ZB)", "valuep": [{"type": "VARREF", "name": "val", "addr": "(ZQ)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(BR)", "loc": "f,72:14,72:16", "dtypep": "(EC)", "declp": "(DC)", "valuep": [{"type": "VARREF", "name": "little.i8", "addr": "(CR)", "loc": "f,72:14,72:16", "dtypep": "(DR)", "access": "RD", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(FR)", "loc": "f,73:15,73:18", "dtypep": "(GC)", "declp": "(FC)", "valuep": [{"type": "VARREF", "name": "little.i48", "addr": "(GR)", "loc": "f,73:15,73:18", "dtypep": "(HR)", "access": "RD", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(JR)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "declp": "(HC)", "valuep": [{"type": "VARREF", "name": "little.i128", "addr": "(KR)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(MR)", "loc": "f,56:15,56:17", "dtypep": "(NC)", "declp": "(MC)", "valuep": [{"type": "VARREF", "name": "neg.i8", "addr": "(NR)", "loc": "f,56:15,56:17", "dtypep": "(DR)", "access": "RD", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(PR)", "loc": "f,57:17,57:20", "dtypep": "(PC)", "declp": "(OC)", "valuep": [{"type": "VARREF", "name": "neg.i48", "addr": "(QR)", "loc": "f,57:17,57:20", "dtypep": "(RR)", "access": "RD", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(TR)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "declp": "(QC)", "valuep": [{"type": "VARREF", "name": "neg.i128", "addr": "(UR)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(WR)", "loc": "f,8:17,8:20", "dtypep": "(J)", "declp": "(KB)", "valuep": [{"type": "VARREF", "name": "CLK", "addr": "(XR)", "loc": "f,8:17,8:20", "dtypep": "(ED)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(YR)", "loc": "f,9:17,9:22", "dtypep": "(J)", "declp": "(LB)", "valuep": [{"type": "VARREF", "name": "RESET", "addr": "(ZR)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "RD", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(AS)", "loc": "f,47:8,47:11", "dtypep": "(J)", "declp": "(VC)", "valuep": [{"type": "VARREF", "name": "GSR", "addr": "(BS)", "loc": "f,47:8,47:11", "dtypep": "(ED)", "access": "RD", "varp": "(CS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "trace_chg_0_sub_0", "addr": "(QQ)", "loc": "f,7:8,7:9", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(DS)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(ES)", "loc": "f,7:8,7:9", "shortText": "uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode + 1);..."}]}], "stmtsp": [{"type": "IF", "name": "", "addr": "(FS)", "loc": "f,7:8,7:9", "condp": [{"type": "ARRAYSEL", "name": "", "addr": "(GS)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(HS)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(IS)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "thensp": [{"type": "TRACEINC", "name": "", "addr": "(JS)", "loc": "f,9:17,9:22", "dtypep": "(J)", "declp": "(PB)", "valuep": [{"type": "VARREF", "name": "RESET", "addr": "(KS)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "RD", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(LS)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "declp": "(WB)", "valuep": [{"type": "VARREF", "name": "vec", "addr": "(MS)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "access": "RD", "varp": "(XQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(NS)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "declp": "(ZB)", "valuep": [{"type": "VARREF", "name": "val", "addr": "(OS)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(PS)", "loc": "f,72:14,72:16", "dtypep": "(EC)", "declp": "(DC)", "valuep": [{"type": "VARREF", "name": "little.i8", "addr": "(QS)", "loc": "f,72:14,72:16", "dtypep": "(DR)", "access": "RD", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(RS)", "loc": "f,73:15,73:18", "dtypep": "(GC)", "declp": "(FC)", "valuep": [{"type": "VARREF", "name": "little.i48", "addr": "(SS)", "loc": "f,73:15,73:18", "dtypep": "(HR)", "access": "RD", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(TS)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "declp": "(HC)", "valuep": [{"type": "VARREF", "name": "little.i128", "addr": "(US)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(VS)", "loc": "f,56:15,56:17", "dtypep": "(NC)", "declp": "(MC)", "valuep": [{"type": "VARREF", "name": "neg.i8", "addr": "(WS)", "loc": "f,56:15,56:17", "dtypep": "(DR)", "access": "RD", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(XS)", "loc": "f,57:17,57:20", "dtypep": "(PC)", "declp": "(OC)", "valuep": [{"type": "VARREF", "name": "neg.i48", "addr": "(YS)", "loc": "f,57:17,57:20", "dtypep": "(RR)", "access": "RD", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(ZS)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "declp": "(QC)", "valuep": [{"type": "VARREF", "name": "neg.i128", "addr": "(AT)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "elsesp": []}, {"type": "TRACEINC", "name": "", "addr": "(BT)", "loc": "f,8:17,8:20", "dtypep": "(J)", "declp": "(KB)", "valuep": [{"type": "VARREF", "name": "CLK", "addr": "(CT)", "loc": "f,8:17,8:20", "dtypep": "(ED)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(DT)", "loc": "f,9:17,9:22", "dtypep": "(J)", "declp": "(LB)", "valuep": [{"type": "VARREF", "name": "RESET", "addr": "(ET)", "loc": "f,9:17,9:22", "dtypep": "(ED)", "access": "RD", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "TRACEINC", "name": "", "addr": "(FT)", "loc": "f,47:8,47:11", "dtypep": "(J)", "declp": "(VC)", "valuep": [{"type": "VARREF", "name": "GSR", "addr": "(GT)", "loc": "f,47:8,47:11", "dtypep": "(ED)", "access": "RD", "varp": "(CS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "trace_cleanup", "addr": "(HT)", "loc": "f,7:8,7:9", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(AB)", "argsp": [], "initsp": [{"type": "CSTMT", "name": "", "addr": "(IT)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(JT)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vt_trace_public_sig_vlt___024root*>(voidSelf);..."}]}, {"type": "CSTMT", "name": "", "addr": "(KT)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(LT)", "loc": "f,7:8,7:9", "shortText": "Vt_trace_public_sig_vlt__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;..."}]}], "stmtsp": [{"type": "CSTMT", "name": "", "addr": "(MT)", "loc": "f,7:8,7:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(NT)", "loc": "f,7:8,7:9", "shortText": "vlSymsp->__Vm_activity = false;..."}]}, {"type": "ASSIGN", "name": "", "addr": "(OT)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(PT)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(QT)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(RT)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(ST)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(TT)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(UT)", "loc": "f,7:8,7:9", "dtypep": "(ED)"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(VT)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "fromp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(WT)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(XT)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_debug_assertions", "addr": "(YT)", "loc": "f,7:8,7:9", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(ZT)", "loc": "f,8:17,8:20", "condp": [{"type": "AND", "name": "", "addr": "(AU)", "loc": "f,8:17,8:20", "dtypep": "(J)", "lhsp": [{"type": "VARREF", "name": "CLK", "addr": "(BU)", "loc": "f,8:17,8:20", "dtypep": "(J)", "access": "RD", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "CONST", "name": "8'hfe", "addr": "(CU)", "loc": "f,8:17,8:20", "dtypep": "(DU)"}]}], "thensp": [{"type": "CSTMT", "name": "", "addr": "(EU)", "loc": "f,8:17,8:20", "exprsp": [{"type": "TEXT", "name": "", "addr": "(FU)", "loc": "f,8:17,8:20", "shortText": "Verilated::overWidthError(\"CLK\");"}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(GU)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "CRESET", "name": "", "addr": "(HU)", "loc": "f,8:17,8:20", "varrefp": [{"type": "VARREF", "name": "CLK", "addr": "(IU)", "loc": "f,8:17,8:20", "dtypep": "(J)", "access": "WR", "varp": "(I)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(JU)", "loc": "f,9:17,9:22", "varrefp": [{"type": "VARREF", "name": "RESET", "addr": "(KU)", "loc": "f,9:17,9:22", "dtypep": "(J)", "access": "WR", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(LU)", "loc": "f,7:8,7:9", "varrefp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__CLK__0", "addr": "(MU)", "loc": "f,7:8,7:9", "dtypep": "(J)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(NU)", "loc": "f,7:8,7:9", "varrefp": [{"type": "VARREF", "name": "__Vm_traceActivity", "addr": "(OU)", "loc": "f,7:8,7:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CUSE", "name": "t", "addr": "(PU)", "loc": "f,7:8,7:9", "useType": "INT_FWD"}], "activesp": []}, {"type": "MODULE", "name": "t", "addr": "(Z)", "loc": "f,7:8,7:9", "origName": "t", "level": 2, "modPublic": true, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "CLK", "addr": "(QU)", "loc": "f,8:17,8:20", "dtypep": "(J)", "origName": "CLK", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": true, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "WIRE", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "RESET", "addr": "(FK)", "loc": "f,9:17,9:22", "dtypep": "(J)", "origName": "RESET", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "neg.i8", "addr": "(OR)", "loc": "f,56:15,56:17", "dtypep": "(NC)", "origName": "i8", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "little.i8", "addr": "(ER)", "loc": "f,72:14,72:16", "dtypep": "(EC)", "origName": "i8", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "val", "addr": "(AR)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "origName": "val", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "neg.i128", "addr": "(VR)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "origName": "i128", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "little.i128", "addr": "(LR)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "origName": "i128", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "neg.i48", "addr": "(SR)", "loc": "f,57:17,57:20", "dtypep": "(PC)", "origName": "i48", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "little.i48", "addr": "(IR)", "loc": "f,73:15,73:18", "dtypep": "(GC)", "origName": "i48", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "vec", "addr": "(XQ)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "origName": "vec", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CELL", "name": "glbl", "addr": "(RU)", "loc": "f,14:9,14:13", "origName": "glbl", "recursive": false, "modp": "(SU)", "pinsp": [], "paramsp": [], "rangep": [], "intfRefsp": []}, {"type": "SCOPE", "name": "t", "addr": "(TU)", "loc": "f,7:8,7:9", "aboveScopep": "(AB)", "aboveCellp": "(Y)", "modp": "(Z)", "varsp": [], "blocksp": []}, {"type": "CFUNC", "name": "_eval_static__TOP__t", "addr": "(CD)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(TU)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(UU)", "loc": "f,19:22,19:23", "dtypep": "(AC)", "rhsp": [{"type": "CONST", "name": "32'sh0", "addr": "(VU)", "loc": "f,19:22,19:23", "dtypep": "(WU)"}], "lhsp": [{"type": "VARREF", "name": "val", "addr": "(XU)", "loc": "f,19:22,19:23", "dtypep": "(AC)", "access": "WR", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial__TOP__t", "addr": "(SD)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(TU)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(YU)", "loc": "f,27:18,27:19", "dtypep": "(ED)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(ZU)", "loc": "f,27:20,27:24", "dtypep": "(ED)"}], "lhsp": [{"type": "VARREF", "name": "RESET", "addr": "(AV)", "loc": "f,27:12,27:17", "dtypep": "(ED)", "access": "WR", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(BV)", "loc": "f,56:30,56:31", "dtypep": "(DR)", "rhsp": [{"type": "CONST", "name": "8'h0", "addr": "(CV)", "loc": "f,56:32,56:34", "dtypep": "(DR)"}], "lhsp": [{"type": "VARREF", "name": "neg.i8", "addr": "(DV)", "loc": "f,56:27,56:29", "dtypep": "(DR)", "access": "WR", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(EV)", "loc": "f,57:34,57:35", "dtypep": "(RR)", "rhsp": [{"type": "CONST", "name": "48'h0", "addr": "(FV)", "loc": "f,57:36,57:38", "dtypep": "(RR)"}], "lhsp": [{"type": "VARREF", "name": "neg.i48", "addr": "(GV)", "loc": "f,57:30,57:33", "dtypep": "(RR)", "access": "WR", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(HV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(JV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(KV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(LV)", "loc": "f,58:31,58:35", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(MV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(OV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(PV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(QV)", "loc": "f,58:31,58:35", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(RV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(SV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(TV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(UV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(VV)", "loc": "f,58:31,58:35", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(WV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(YV)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(ZV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(AW)", "loc": "f,58:31,58:35", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(BW)", "loc": "f,58:36,58:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(CW)", "loc": "f,72:29,72:30", "dtypep": "(DR)", "rhsp": [{"type": "CONST", "name": "8'h0", "addr": "(DW)", "loc": "f,72:31,72:33", "dtypep": "(DR)"}], "lhsp": [{"type": "VARREF", "name": "little.i8", "addr": "(EW)", "loc": "f,72:26,72:28", "dtypep": "(DR)", "access": "WR", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(FW)", "loc": "f,73:32,73:33", "dtypep": "(HR)", "rhsp": [{"type": "CONST", "name": "49'h0", "addr": "(GW)", "loc": "f,73:34,73:36", "dtypep": "(HR)"}], "lhsp": [{"type": "VARREF", "name": "little.i48", "addr": "(HW)", "loc": "f,73:28,73:31", "dtypep": "(HR)", "access": "WR", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(IW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(JW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(KW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(LW)", "loc": "f,74:31,74:35", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(MW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(OW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(PW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(QW)", "loc": "f,74:31,74:35", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(RW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(SW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(TW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(UW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(VW)", "loc": "f,74:31,74:35", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(WW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(YW)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(ZW)", "loc": "f,74:36,74:37", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(AX)", "loc": "f,74:31,74:35", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(BX)", "loc": "f,74:36,74:37", "dtypep": "(JD)"}]}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_nba_sequent__TOP__t__0", "addr": "(PK)", "loc": "f,80:7,80:11", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(TU)", "argsp": [], "initsp": [{"type": "VAR", "name": "__Vdly__val", "addr": "(CX)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "origName": "__Vdly__val", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(DX)", "loc": "f,19:16,19:19", "varrefp": [{"type": "VARREF", "name": "__Vdly__val", "addr": "(EX)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "access": "WR", "varp": "(CX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__Vdlyvval__vec__v0", "addr": "(FX)", "loc": "f,17:16,17:19", "dtypep": "(GX)", "origName": "__Vdlyvval__vec__v0", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(HX)", "loc": "f,17:16,17:19", "varrefp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v0", "addr": "(IX)", "loc": "f,17:16,17:19", "dtypep": "(GX)", "access": "WR", "varp": "(FX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__Vdlyvval__vec__v1", "addr": "(JX)", "loc": "f,17:16,17:19", "dtypep": "(GX)", "origName": "__Vdlyvval__vec__v1", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(KX)", "loc": "f,17:16,17:19", "varrefp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v1", "addr": "(LX)", "loc": "f,17:16,17:19", "dtypep": "(GX)", "access": "WR", "varp": "(JX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__Vtemp_1", "addr": "(MX)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "origName": "__Vtemp_1", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "STMTTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__Vtemp_4", "addr": "(NX)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "origName": "__Vtemp_4", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "STMTTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGNPRE", "name": "", "addr": "(OX)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "rhsp": [{"type": "VARREF", "name": "val", "addr": "(PX)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vdly__val", "addr": "(QX)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "access": "WR", "varp": "(CX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(RX)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(SX)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(TX)", "loc": "f,80:16,80:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(UX)", "loc": "f,80:16,80:20", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(VX)", "loc": "f,80:16,80:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(WX)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(XX)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "WR", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(YX)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(ZX)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(AY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(BY)", "loc": "f,80:16,80:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(CY)", "loc": "f,80:16,80:20", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(DY)", "loc": "f,80:16,80:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(EY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(FY)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "WR", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(GY)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(HY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(IY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(JY)", "loc": "f,80:16,80:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(KY)", "loc": "f,80:16,80:20", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(LY)", "loc": "f,80:16,80:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(MY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(NY)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "WR", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(OY)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(PY)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(QY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(RY)", "loc": "f,80:16,80:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(SY)", "loc": "f,80:16,80:20", "dtypep": "(IC)", "access": "RD", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(TY)", "loc": "f,80:16,80:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(UY)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(VY)", "loc": "f,80:7,80:11", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(WY)", "loc": "f,80:12,80:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XY)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(YY)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(ZY)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "RD", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(AZ)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(BZ)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(CZ)", "loc": "f,80:7,80:11", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(DZ)", "loc": "f,80:12,80:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(EZ)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(FZ)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(GZ)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "RD", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(HZ)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(IZ)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(JZ)", "loc": "f,80:7,80:11", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(KZ)", "loc": "f,80:12,80:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(LZ)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(MZ)", "loc": "f,80:15,80:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_1", "addr": "(NZ)", "loc": "f,80:15,80:16", "dtypep": "(IC)", "access": "RD", "varp": "(MX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(OZ)", "loc": "f,80:15,80:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(PZ)", "loc": "f,80:12,80:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "little.i128", "addr": "(QZ)", "loc": "f,80:7,80:11", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(RZ)", "loc": "f,80:12,80:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(SZ)", "loc": "f,79:11,79:13", "dtypep": "(HR)", "rhsp": [{"type": "AND", "name": "", "addr": "(TZ)", "loc": "f,79:14,79:15", "dtypep": "(HR)", "lhsp": [{"type": "CONST", "name": "64'h1ffffffffffff", "addr": "(UZ)", "loc": "f,79:14,79:15", "dtypep": "(XG)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(VZ)", "loc": "f,79:14,79:15", "dtypep": "(HR)", "lhsp": [{"type": "VARREF", "name": "little.i48", "addr": "(WZ)", "loc": "f,79:15,79:18", "dtypep": "(HR)", "access": "RD", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}], "lhsp": [{"type": "VARREF", "name": "little.i48", "addr": "(XZ)", "loc": "f,79:7,79:10", "dtypep": "(HR)", "access": "WR", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(YZ)", "loc": "f,78:10,78:12", "dtypep": "(DR)", "rhsp": [{"type": "AND", "name": "", "addr": "(ZZ)", "loc": "f,78:13,78:14", "dtypep": "(DR)", "lhsp": [{"type": "CONST", "name": "32'hff", "addr": "(AAB)", "loc": "f,78:13,78:14", "dtypep": "(JD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(BAB)", "loc": "f,78:13,78:14", "dtypep": "(DR)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(CAB)", "loc": "f,78:14,78:16", "dtypep": "(DR)", "size": 32, "lhsp": [{"type": "VARREF", "name": "little.i8", "addr": "(DAB)", "loc": "f,78:14,78:16", "dtypep": "(DR)", "access": "RD", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}], "lhsp": [{"type": "VARREF", "name": "little.i8", "addr": "(EAB)", "loc": "f,78:7,78:9", "dtypep": "(DR)", "access": "WR", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(FAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(GAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(HAB)", "loc": "f,63:16,63:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(IAB)", "loc": "f,63:16,63:20", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(JAB)", "loc": "f,63:16,63:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(KAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(LAB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "WR", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(MAB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(NAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(OAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(PAB)", "loc": "f,63:16,63:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(QAB)", "loc": "f,63:16,63:20", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(RAB)", "loc": "f,63:16,63:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(SAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(TAB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "WR", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(UAB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(VAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(WAB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(XAB)", "loc": "f,63:16,63:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(YAB)", "loc": "f,63:16,63:20", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(ZAB)", "loc": "f,63:16,63:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(ABB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(BBB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "WR", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(CBB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(DBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "rhsp": [{"type": "NOT", "name": "", "addr": "(EBB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(FBB)", "loc": "f,63:16,63:20", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(GBB)", "loc": "f,63:16,63:20", "dtypep": "(RC)", "access": "RD", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(HBB)", "loc": "f,63:16,63:20", "dtypep": "(JD)"}]}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(IBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(JBB)", "loc": "f,63:7,63:11", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h0", "addr": "(KBB)", "loc": "f,63:12,63:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(LBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(MBB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(NBB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "RD", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(OBB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(PBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(QBB)", "loc": "f,63:7,63:11", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h1", "addr": "(RBB)", "loc": "f,63:12,63:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(SBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(TBB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(UBB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "RD", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(VBB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(WBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(XBB)", "loc": "f,63:7,63:11", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h2", "addr": "(YBB)", "loc": "f,63:12,63:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(ZBB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "rhsp": [{"type": "WORDSEL", "name": "", "addr": "(ACB)", "loc": "f,63:15,63:16", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "__Vtemp_4", "addr": "(BCB)", "loc": "f,63:15,63:16", "dtypep": "(RC)", "access": "RD", "varp": "(NX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(CCB)", "loc": "f,63:15,63:16", "dtypep": "(JD)"}]}], "lhsp": [{"type": "WORDSEL", "name": "", "addr": "(DCB)", "loc": "f,63:12,63:14", "dtypep": "(IV)", "fromp": [{"type": "VARREF", "name": "neg.i128", "addr": "(ECB)", "loc": "f,63:7,63:11", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "32'h3", "addr": "(FCB)", "loc": "f,63:12,63:14", "dtypep": "(JD)"}]}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(GCB)", "loc": "f,62:11,62:13", "dtypep": "(RR)", "rhsp": [{"type": "AND", "name": "", "addr": "(HCB)", "loc": "f,62:14,62:15", "dtypep": "(RR)", "lhsp": [{"type": "CONST", "name": "64'hffffffffffff", "addr": "(ICB)", "loc": "f,62:14,62:15", "dtypep": "(XG)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(JCB)", "loc": "f,62:14,62:15", "dtypep": "(RR)", "lhsp": [{"type": "VARREF", "name": "neg.i48", "addr": "(KCB)", "loc": "f,62:15,62:18", "dtypep": "(RR)", "access": "RD", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}], "lhsp": [{"type": "VARREF", "name": "neg.i48", "addr": "(LCB)", "loc": "f,62:7,62:10", "dtypep": "(RR)", "access": "WR", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(MCB)", "loc": "f,61:10,61:12", "dtypep": "(DR)", "rhsp": [{"type": "AND", "name": "", "addr": "(NCB)", "loc": "f,61:13,61:14", "dtypep": "(DR)", "lhsp": [{"type": "CONST", "name": "32'hff", "addr": "(OCB)", "loc": "f,61:13,61:14", "dtypep": "(JD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(PCB)", "loc": "f,61:13,61:14", "dtypep": "(DR)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(QCB)", "loc": "f,61:14,61:16", "dtypep": "(DR)", "size": 32, "lhsp": [{"type": "VARREF", "name": "neg.i8", "addr": "(RCB)", "loc": "f,61:14,61:16", "dtypep": "(DR)", "access": "RD", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}], "lhsp": [{"type": "VARREF", "name": "neg.i8", "addr": "(SCB)", "loc": "f,61:7,61:9", "dtypep": "(DR)", "access": "WR", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(TCB)", "loc": "f,21:21,21:23", "dtypep": "(AC)", "rhsp": [{"type": "COND", "name": "", "addr": "(UCB)", "loc": "f,21:24,21:25", "dtypep": "(WU)", "condp": [{"type": "CCAST", "name": "", "addr": "(VCB)", "loc": "f,21:10,21:15", "dtypep": "(ED)", "size": 32, "lhsp": [{"type": "VARREF", "name": "RESET", "addr": "(WCB)", "loc": "f,21:10,21:15", "dtypep": "(ED)", "access": "RD", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thenp": [{"type": "CONST", "name": "32'sh0", "addr": "(XCB)", "loc": "f,21:24,21:25", "dtypep": "(WU)"}], "elsep": [{"type": "ADD", "name": "", "addr": "(YCB)", "loc": "f,22:22,22:23", "dtypep": "(AC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(ZCB)", "loc": "f,22:24,22:25", "dtypep": "(JD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(ADB)", "loc": "f,22:24,22:25", "dtypep": "(WU)"}]}], "rhsp": [{"type": "VARREF", "name": "val", "addr": "(BDB)", "loc": "f,22:18,22:21", "dtypep": "(AC)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}], "lhsp": [{"type": "VARREF", "name": "__Vdly__val", "addr": "(CDB)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "access": "WR", "varp": "(CX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(DDB)", "loc": "f,23:14,23:16", "dtypep": "(EDB)", "rhsp": [{"type": "AND", "name": "", "addr": "(FDB)", "loc": "f,23:20,23:21", "dtypep": "(EDB)", "lhsp": [{"type": "CONST", "name": "32'h3", "addr": "(GDB)", "loc": "f,23:20,23:21", "dtypep": "(JD)"}], "rhsp": [{"type": "VARREF", "name": "val", "addr": "(HDB)", "loc": "f,23:17,23:20", "dtypep": "(EDB)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v0", "addr": "(IDB)", "loc": "f,23:14,23:16", "dtypep": "(EDB)", "access": "WR", "varp": "(FX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(JDB)", "loc": "f,24:14,24:16", "dtypep": "(EDB)", "rhsp": [{"type": "AND", "name": "", "addr": "(KDB)", "loc": "f,24:20,24:21", "dtypep": "(EDB)", "lhsp": [{"type": "CONST", "name": "32'h3", "addr": "(LDB)", "loc": "f,24:20,24:21", "dtypep": "(JD)"}], "rhsp": [{"type": "SHIFTR", "name": "", "addr": "(MDB)", "loc": "f,24:20,24:21", "dtypep": "(EDB)", "lhsp": [{"type": "VARREF", "name": "val", "addr": "(NDB)", "loc": "f,24:17,24:20", "dtypep": "(AC)", "access": "RD", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "CONST", "name": "5'h2", "addr": "(ODB)", "loc": "f,24:23,24:24", "dtypep": "(PDB)"}]}]}], "lhsp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v1", "addr": "(QDB)", "loc": "f,24:14,24:16", "dtypep": "(EDB)", "access": "WR", "varp": "(JX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNPOST", "name": "", "addr": "(RDB)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "rhsp": [{"type": "VARREF", "name": "__Vdly__val", "addr": "(SDB)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "access": "RD", "varp": "(CX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "val", "addr": "(TDB)", "loc": "f,21:17,21:20", "dtypep": "(AC)", "access": "WR", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(UDB)", "loc": "f,23:14,23:16", "dtypep": "(EDB)", "rhsp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v0", "addr": "(VDB)", "loc": "f,23:14,23:16", "dtypep": "(EDB)", "access": "RD", "varp": "(FX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(WDB)", "loc": "f,23:14,23:16", "dtypep": "(EDB)", "fromp": [{"type": "VARREF", "name": "vec", "addr": "(XDB)", "loc": "f,23:7,23:10", "dtypep": "(XB)", "access": "WR", "varp": "(XQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "3'h0", "addr": "(YDB)", "loc": "f,23:11,23:12", "dtypep": "(ZDB)"}]}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(AEB)", "loc": "f,24:14,24:16", "dtypep": "(EDB)", "rhsp": [{"type": "VARREF", "name": "__Vdlyvval__vec__v1", "addr": "(BEB)", "loc": "f,24:14,24:16", "dtypep": "(EDB)", "access": "RD", "varp": "(JX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "ARRAYSEL", "name": "", "addr": "(CEB)", "loc": "f,24:14,24:16", "dtypep": "(EDB)", "fromp": [{"type": "VARREF", "name": "vec", "addr": "(DEB)", "loc": "f,24:7,24:10", "dtypep": "(XB)", "access": "WR", "varp": "(XQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "bitp": [{"type": "CONST", "name": "3'h1", "addr": "(EEB)", "loc": "f,24:11,24:12", "dtypep": "(ZDB)"}]}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(FEB)", "loc": "f,29:12,29:14", "dtypep": "(ED)", "rhsp": [{"type": "VARREF", "name": "GSR", "addr": "(GEB)", "loc": "f,29:20,29:23", "dtypep": "(ED)", "access": "RD", "varp": "(CS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "RESET", "addr": "(HEB)", "loc": "f,29:6,29:11", "dtypep": "(ED)", "access": "WR", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(IEB)", "loc": "f,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "CRESET", "name": "", "addr": "(JEB)", "loc": "f,8:17,8:20", "varrefp": [{"type": "VARREF", "name": "CLK", "addr": "(KEB)", "loc": "f,8:17,8:20", "dtypep": "(J)", "access": "WR", "varp": "(QU)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(LEB)", "loc": "f,9:17,9:22", "varrefp": [{"type": "VARREF", "name": "RESET", "addr": "(MEB)", "loc": "f,9:17,9:22", "dtypep": "(J)", "access": "WR", "varp": "(FK)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(NEB)", "loc": "f,17:16,17:19", "varrefp": [{"type": "VARREF", "name": "vec", "addr": "(OEB)", "loc": "f,17:16,17:19", "dtypep": "(XB)", "access": "WR", "varp": "(XQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(PEB)", "loc": "f,19:16,19:19", "varrefp": [{"type": "VARREF", "name": "val", "addr": "(QEB)", "loc": "f,19:16,19:19", "dtypep": "(AC)", "access": "WR", "varp": "(AR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(REB)", "loc": "f,56:15,56:17", "varrefp": [{"type": "VARREF", "name": "neg.i8", "addr": "(SEB)", "loc": "f,56:15,56:17", "dtypep": "(NC)", "access": "WR", "varp": "(OR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(TEB)", "loc": "f,57:17,57:20", "varrefp": [{"type": "VARREF", "name": "neg.i48", "addr": "(UEB)", "loc": "f,57:17,57:20", "dtypep": "(PC)", "access": "WR", "varp": "(SR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(VEB)", "loc": "f,58:17,58:21", "varrefp": [{"type": "VARREF", "name": "neg.i128", "addr": "(WEB)", "loc": "f,58:17,58:21", "dtypep": "(RC)", "access": "WR", "varp": "(VR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(XEB)", "loc": "f,72:14,72:16", "varrefp": [{"type": "VARREF", "name": "little.i8", "addr": "(YEB)", "loc": "f,72:14,72:16", "dtypep": "(EC)", "access": "WR", "varp": "(ER)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(ZEB)", "loc": "f,73:15,73:18", "varrefp": [{"type": "VARREF", "name": "little.i48", "addr": "(AFB)", "loc": "f,73:15,73:18", "dtypep": "(GC)", "access": "WR", "varp": "(IR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(BFB)", "loc": "f,74:17,74:21", "varrefp": [{"type": "VARREF", "name": "little.i128", "addr": "(CFB)", "loc": "f,74:17,74:21", "dtypep": "(IC)", "access": "WR", "varp": "(LR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CUSE", "name": "glbl", "addr": "(DFB)", "loc": "f,14:9,14:13", "useType": "INT_FWD"}], "activesp": []}, {"type": "MODULE", "name": "glbl", "addr": "(SU)", "loc": "f,33:8,33:12", "origName": "glbl", "level": 3, "modPublic": true, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "GSR", "addr": "(CS)", "loc": "f,47:8,47:11", "dtypep": "(J)", "origName": "GSR", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "SCOPE", "name": "t.glbl", "addr": "(EFB)", "loc": "f,14:9,14:13", "aboveScopep": "(TU)", "aboveCellp": "(RU)", "modp": "(SU)", "varsp": [], "blocksp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(FFB)", "loc": "f,33:8,33:12", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "CRESET", "name": "", "addr": "(GFB)", "loc": "f,47:8,47:11", "varrefp": [{"type": "VARREF", "name": "GSR", "addr": "(HFB)", "loc": "f,47:8,47:11", "dtypep": "(J)", "access": "WR", "varp": "(CS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}], "activesp": []}], "filesp": [{"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Syms.cpp", "addr": "(IFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Syms.h", "addr": "(JFB)", "loc": "a,0:0,0:0", "source": false, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Dpi.h", "addr": "(KFB)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Dpi.cpp", "addr": "(LFB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt.h", "addr": "(MFB)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt.cpp", "addr": "(NFB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root.h", "addr": "(OFB)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_t.h", "addr": "(PFB)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_glbl.h", "addr": "(QFB)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root__Slow.cpp", "addr": "(RFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root__DepSet_h57c3739d__0__Slow.cpp", "addr": "(SFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root__DepSet_h1fa4e909__0__Slow.cpp", "addr": "(TFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root__DepSet_h57c3739d__0.cpp", "addr": "(UFB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_$root__DepSet_h1fa4e909__0.cpp", "addr": "(VFB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_t__Slow.cpp", "addr": "(WFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_t__DepSet_h4ca1d5da__0__Slow.cpp", "addr": "(XFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_t__DepSet_h3ac446c2__0.cpp", "addr": "(YFB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_glbl__Slow.cpp", "addr": "(ZFB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt_glbl__DepSet_h149371c9__0__Slow.cpp", "addr": "(AGB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Trace__0__Slow.cpp", "addr": "(BGB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__TraceDecls__0__Slow.cpp", "addr": "(CGB)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_trace_public_sig_vlt/Vt_trace_public_sig_vlt__Trace__0.cpp", "addr": "(DGB)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}], "miscsp": [{"type": "TYPETABLE", "name": "", "addr": "(C)", "loc": "a,0:0,0:0", "constraintRefp": "UNLINKED", "emptyQueuep": "UNLINKED", "queueIndexp": "UNLINKED", "streamp": "UNLINKED", "voidp": "(HB)", "typesp": [{"type": "BASICDTYPE", "name": "logic", "addr": "(J)", "loc": "d,50:22,50:24", "dtypep": "(J)", "keyword": "logic", "generic": true, "rangep": []}, {"type": "VOIDDTYPE", "name": "", "addr": "(HB)", "loc": "d,51:21,51:30", "dtypep": "(HB)", "generic": false}, {"type": "BASICDTYPE", "name": "logic", "addr": "(EC)", "loc": "f,72:4,72:7", "dtypep": "(EC)", "keyword": "logic", "range": "0:7", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(GC)", "loc": "f,73:4,73:7", "dtypep": "(GC)", "keyword": "logic", "range": "1:49", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(IC)", "loc": "f,74:4,74:7", "dtypep": "(IC)", "keyword": "logic", "range": "63:190", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(NC)", "loc": "f,56:4,56:7", "dtypep": "(NC)", "keyword": "logic", "range": "0:-7", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(PC)", "loc": "f,57:4,57:7", "dtypep": "(PC)", "keyword": "logic", "range": "-1:-48", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(RC)", "loc": "f,58:4,58:7", "dtypep": "(RC)", "keyword": "logic", "range": "63:-64", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(EGB)", "loc": "f,17:4,17:9", "dtypep": "(EGB)", "keyword": "logic", "range": "2:1", "generic": true, "rangep": []}, {"type": "UNPACKARRAYDTYPE", "name": "", "addr": "(XB)", "loc": "f,17:20,17:21", "dtypep": "(XB)", "isCompound": false, "declRange": "[4:3]", "generic": false, "refDTypep": "(EGB)", "childDTypep": [], "rangep": [{"type": "RANGE", "name": "", "addr": "(FGB)", "loc": "f,17:20,17:21", "ascending": false, "leftp": [{"type": "CONST", "name": "32'sh4", "addr": "(GGB)", "loc": "f,17:21,17:22", "dtypep": "(WU)"}], "rightp": [{"type": "CONST", "name": "32'sh3", "addr": "(HGB)", "loc": "f,17:23,17:24", "dtypep": "(WU)"}]}]}, {"type": "BASICDTYPE", "name": "integer", "addr": "(AC)", "loc": "f,19:4,19:11", "dtypep": "(AC)", "keyword": "integer", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(GX)", "loc": "f,23:20,23:21", "dtypep": "(GX)", "keyword": "logic", "range": "1:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(WU)", "loc": "f,17:21,17:22", "dtypep": "(WU)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(JD)", "loc": "f,23:21,23:22", "dtypep": "(JD)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(M)", "loc": "f,17:16,17:19", "dtypep": "(M)", "keyword": "bit", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VlTriggerVec", "addr": "(U)", "loc": "f,7:8,7:9", "dtypep": "(U)", "keyword": "VlTriggerVec", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "QData", "addr": "(AH)", "loc": "f,7:8,7:9", "dtypep": "(AH)", "keyword": "QData", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(XG)", "loc": "f,7:8,7:9", "dtypep": "(XG)", "keyword": "logic", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(Q)", "loc": "f,7:8,7:9", "dtypep": "(Q)", "keyword": "bit", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VlTriggerVec", "addr": "(W)", "loc": "f,7:8,7:9", "dtypep": "(W)", "keyword": "VlTriggerVec", "generic": false, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(IGB)", "loc": "f,7:8,7:9", "dtypep": "(IGB)", "keyword": "bit", "generic": false, "rangep": []}, {"type": "UNPACKARRAYDTYPE", "name": "", "addr": "(S)", "loc": "f,7:8,7:9", "dtypep": "(S)", "isCompound": false, "declRange": "[1:0]", "generic": false, "refDTypep": "(IGB)", "childDTypep": [], "rangep": [{"type": "RANGE", "name": "", "addr": "(JGB)", "loc": "f,7:8,7:9", "ascending": false, "leftp": [{"type": "CONST", "name": "32'h1", "addr": "(KGB)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}], "rightp": [{"type": "CONST", "name": "32'h0", "addr": "(LGB)", "loc": "f,7:8,7:9", "dtypep": "(JD)"}]}]}, {"type": "BASICDTYPE", "name": "chandle", "addr": "(DP)", "loc": "f,7:8,7:9", "dtypep": "(DP)", "keyword": "chandle", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(ED)", "loc": "f,7:8,7:9", "dtypep": "(ED)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(DR)", "loc": "f,72:14,72:16", "dtypep": "(DR)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(HR)", "loc": "f,73:15,73:18", "dtypep": "(HR)", "keyword": "logic", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(RR)", "loc": "f,57:17,57:20", "dtypep": "(RR)", "keyword": "logic", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(PDB)", "loc": "f,23:23,23:24", "dtypep": "(PDB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(EDB)", "loc": "f,23:20,23:21", "dtypep": "(EDB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(ZDB)", "loc": "f,23:11,23:12", "dtypep": "(ZDB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "IData", "addr": "(IV)", "loc": "f,58:36,58:37", "dtypep": "(IV)", "keyword": "IData", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(DU)", "loc": "f,8:17,8:20", "dtypep": "(DU)", "keyword": "logic", "range": "7:0", "generic": true, "rangep": []}]}, {"type": "CONSTPOOL", "name": "", "addr": "(D)", "loc": "a,0:0,0:0", "modulep": [{"type": "MODULE", "name": "@CONST-POOL@", "addr": "(MGB)", "loc": "a,0:0,0:0", "origName": "@CONST-POOL@", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "NONE", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "TOP", "addr": "(NGB)", "loc": "a,0:0,0:0", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(MGB)", "varsp": [], "blocksp": []}], "activesp": []}]}]}