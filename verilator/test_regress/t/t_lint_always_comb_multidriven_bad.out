%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:26:16: Variable written to in always_comb also written by other process (IEEE 1800-2023 *******): 'out1'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:26:16: 
   26 |    always_comb out1 = d;   
      |                ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:25:11: ... Location of other write
   25 |    assign out1 = 1'b0;
      |           ^~~~
                      ... For warning description see https://verilator.org/warn/MULTIDRIVEN?v=latest
                      ... Use "/* verilator lint_off MULTIDRIVEN */" and lint_on around source to disable this message.
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:29:16: Variable written to in always_comb also written by other process (IEEE 1800-2023 *******): 'out2'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:29:16: 
   29 |    always_comb out2 = 1'b0;   
      |                ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:28:11: ... Location of other write
   28 |    assign out2 = d;
      |           ^~~~
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:32:11: Variable also written to in always_comb (IEEE 1800-2023 *******): 'out3'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:32:11: 
   32 |    assign out3 = 1'b0;   
      |           ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:31:16: ... Location of always_comb write
   31 |    always_comb out3 = d;
      |                ^~~~
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:35:11: Variable also written to in always_comb (IEEE 1800-2023 *******): 'out4'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:35:11: 
   35 |    assign out4 = d;   
      |           ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:34:16: ... Location of always_comb write
   34 |    always_comb out4 = 1'b0;
      |                ^~~~
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:38:16: Variable written to in always_comb also written by other process (IEEE 1800-2023 *******): 'out5'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:38:16: 
   38 |    always_comb out5 = d;   
      |                ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:37:16: ... Location of other write
   37 |    always_comb out5 = 1'b0;
      |                ^~~~
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:41:16: Variable written to in always_comb also written by other process (IEEE 1800-2023 *******): 'out6'
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:41:16: 
   41 |    always_comb out6 = 1'b0;   
      |                ^~~~
                      t/t_lint_always_comb_multidriven_bad.v:40:16: ... Location of other write
   40 |    always_comb out6 = d;
      |                ^~~~
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:17:15: Bits [0:0] of signal 'out2' have multiple combinational drivers
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:28:16: ... Location of first driver
   28 |    assign out2 = d;
      |                ^
                      t/t_lint_always_comb_multidriven_bad.v:29:21: ... Location of other driver
   29 |    always_comb out2 = 1'b0;   
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:17:15: ... Only the first driver will be respected
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:19:15: Bits [0:0] of signal 'out4' have multiple combinational drivers
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:34:21: ... Location of first driver
   34 |    always_comb out4 = 1'b0;
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:35:16: ... Location of other driver
   35 |    assign out4 = d;   
      |                ^
                      t/t_lint_always_comb_multidriven_bad.v:19:15: ... Only the first driver will be respected
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:20:15: Bits [0:0] of signal 'out5' have multiple combinational drivers
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:37:21: ... Location of first driver
   37 |    always_comb out5 = 1'b0;
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:38:21: ... Location of other driver
   38 |    always_comb out5 = d;   
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:20:15: ... Only the first driver will be respected
%Warning-MULTIDRIVEN: t/t_lint_always_comb_multidriven_bad.v:21:15: Bits [0:0] of signal 'out6' have multiple combinational drivers
                                                                  : ... note: In instance 't'
                      t/t_lint_always_comb_multidriven_bad.v:40:21: ... Location of first driver
   40 |    always_comb out6 = d;
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:41:21: ... Location of other driver
   41 |    always_comb out6 = 1'b0;   
      |                     ^
                      t/t_lint_always_comb_multidriven_bad.v:21:15: ... Only the first driver will be respected
%Error: Exiting due to
