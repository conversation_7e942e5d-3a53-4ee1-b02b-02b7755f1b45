%Error-UNSUPPORTED: t/t_randomize_method_complex_bad.v:16:11: Unsupported: 'randomize() with' on complex expressions
   16 |       x.f.randomize() with { r < 5; },
      |           ^~~~~~~~~
                    ... For error description see https://verilator.org/warn/UNSUPPORTED?v=latest
%Error: t/t_randomize_method_complex_bad.v:16:30: Can't find definition of variable: 'r'
   16 |       x.f.randomize() with { r < 5; },
      |                              ^
%Error: t/t_randomize_method_complex_bad.v:17:9: 'randomize() with' on a non-class-instance 'int'
   17 |       i.randomize() with { v < 5; });
      |         ^~~~~~~~~
%Error: t/t_randomize_method_complex_bad.v:17:28: Can't find definition of variable: 'v'
   17 |       i.randomize() with { v < 5; });
      |                            ^
%Error: Exiting due to
