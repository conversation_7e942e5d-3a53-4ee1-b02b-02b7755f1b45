0 | posedge
0 | cb.y=0
0 | b=0
0 | x<=0
0 | y=0
0 | c<=0
0 | c<=1
0 | cb.a=1
0 | cb.b=1
0 | posedge
0 | x<=1
0 | y=1
0 | c<=0
0 | cb.a=0
0 | cb.b=1
0 | cb.y=1
0 | b=1
0 | x<=0
0 | y=0
0 | 0 1 0 0 0 0
20 | posedge
20 | c<=1
20 | cb.a=1
20 | cb.b=1
20 | cb.y=0
20 | b=0
20 | posedge
20 | x<=1
20 | y=1
20 | c<=0
20 | cb.a=0
20 | cb.b=1
20 | cb.y=1
20 | b=1
20 | x<=0
20 | y=0
20 | 0 1 0 0 0 0
30 | posedge
30 | c<=1
30 | cb.a=1
30 | cb.b=1
30 | cb.y=0
30 | b=0
30 | posedge
30 | x<=1
30 | y=1
30 | c<=0
30 | cb.a=0
30 | cb.b=1
30 | cb.y=1
30 | b=1
30 | x<=0
30 | y=0
30 | 0 1 0 0 0 0
*-* All Finished *-*
