Verilator Gantt report

Argument settings:
  +verilator+prof+exec+start+2
  +verilator+prof+exec+window+2

Summary:
  Total elapsed time = 23415 rdtsc ticks
  Parallelized code  = 82.51% of elapsed time
  Total threads      = 2
  Total CPUs used    = 2
  Total mtasks       = 7
  Total yields       = 0

Parallelized code, measured:
  Thread utilization =  14.22%
  Speedup            =  0.284x

Parallelized code, predicted during static scheduling:
  Thread utilization =  63.22%
  Speedup            =   1.26x

All code, measured:
  Thread utilization =  20.48%
  Speedup            =   0.41x

All code, measured, scaled by predicted speedup:
  Thread utilization =  56.80%
  Speedup            =   1.14x

MTask statistics:
  Longest mtask id = 5
  Longest mtask time = 6.16% of time elapsed in parallelized code
  min log(p2e) = -3.681  from mtask 5 (predict 30, elapsed 1190)
  max log(p2e) = -2.409  from mtask 8 (predict 107, elapsed 1190)
  mean = -2.992
  stddev = 0.459
  e ^ stddev = 1.583

CPU info:
   Id | Time spent executing MTask | Socket | Core | Model
      | % of elapsed ticks / ticks |        |      |
  ====|============================|========|======|======
   10 |  20.18% /             4725 |      0 |   10 | Test Ryzen 9 3950X 16-Core Processor
   19 |   3.29% /              770 |      0 |    3 | Test Ryzen 9 3950X 16-Core Processor

Writing profile_exec.vcd
