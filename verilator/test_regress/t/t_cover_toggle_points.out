//      // verilator_coverage annotation
        // DESCRIPTION: Verilator: Verilog Test module
        //
        // This file ONLY is placed under the Creative Commons Public Domain, for
        // any use, without warranty, 2008 by <PERSON>.
        // SPDX-License-Identifier: CC0-1.0
        
        module t (/*AUTOARG*/
           // Inputs
           clk, check_real, check_array_real, check_string
           );
        
 000019    input clk;
+000019  point: comment=clk
           input real check_real; // Check issue #2741
 000021    input real check_array_real [1:0];
+000021  point: comment=check_array_real[0]
+000021  point: comment=check_array_real[1]
           input string check_string; // Check issue #2766
        
           typedef struct packed {
              union packed {
                 logic    ua;
                 logic    ub;
              } u;
              logic b;
           } str_t;
        
%000002    reg   toggle; initial toggle='0;
-000002  point: comment=toggle
        
%000002    str_t stoggle; initial stoggle='0;
-000002  point: comment=stoggle.b
-000002  point: comment=stoggle.u.ua
        
           union {
              real val1;  // TODO use bit [7:0] here
              real val2;  // TODO use bit [3:0] here
           } utoggle;
        
           const reg aconst = '0;
        
%000000    reg [1:0][1:0] ptoggle; initial ptoggle=0;
-000002  point: comment=ptoggle[0][0]
-000000  point: comment=ptoggle[0][1]
-000000  point: comment=ptoggle[1][0]
-000000  point: comment=ptoggle[1][1]
        
           integer cyc; initial cyc=1;
%000000    wire [7:0] cyc_copy = cyc[7:0];
+000011  point: comment=cyc_copy[0]
-000005  point: comment=cyc_copy[1]
-000002  point: comment=cyc_copy[2]
-000001  point: comment=cyc_copy[3]
-000000  point: comment=cyc_copy[4]
-000000  point: comment=cyc_copy[5]
-000000  point: comment=cyc_copy[6]
-000000  point: comment=cyc_copy[7]
%000002    wire       toggle_up;
-000002  point: comment=toggle_up
        
           typedef struct {
              int q[$];
           } str_queue_t;
           str_queue_t str_queue;
        
           alpha a1 (/*AUTOINST*/
                     // Outputs
                     .toggle_up                 (toggle_up),
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle),
                     .cyc_copy                  (cyc_copy[7:0]));
           alpha a2 (/*AUTOINST*/
                     // Outputs
                     .toggle_up                 (toggle_up),
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle),
                     .cyc_copy                  (cyc_copy[7:0]));
        
           beta  b1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle_up                 (toggle_up));
        
           off   o1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
        
%000000    reg [1:0]  memory[121:110];
-000001  point: comment=memory[110][0]
-000000  point: comment=memory[110][1]
-000000  point: comment=memory[111][0]
-000000  point: comment=memory[111][1]
-000000  point: comment=memory[112][0]
-000000  point: comment=memory[112][1]
-000000  point: comment=memory[113][0]
-000000  point: comment=memory[113][1]
-000000  point: comment=memory[114][0]
-000000  point: comment=memory[114][1]
-000000  point: comment=memory[115][0]
-000000  point: comment=memory[115][1]
-000000  point: comment=memory[116][0]
-000000  point: comment=memory[116][1]
-000000  point: comment=memory[117][0]
-000000  point: comment=memory[117][1]
-000000  point: comment=memory[118][0]
-000000  point: comment=memory[118][1]
-000000  point: comment=memory[119][0]
-000000  point: comment=memory[119][1]
-000000  point: comment=memory[120][0]
-000000  point: comment=memory[120][1]
-000000  point: comment=memory[121][0]
-000000  point: comment=memory[121][1]
        
           wire [1023:0] largeish = {992'h0, cyc};
           // CHECK_COVER_MISSING(-1)
        
           always @ (posedge clk) begin
              if (cyc != 0) begin
                 cyc <= cyc + 1;
                 memory[cyc + 'd100] <= memory[cyc + 'd100] + 2'b1;
                 toggle <= '0;
                 stoggle.u <= toggle;
                 stoggle.b <= toggle;
                 utoggle.val1 <= real'(cyc[7:0]);
                 ptoggle[0][0] <= toggle;
                 if (cyc == 3) begin
                    str_queue.q.push_back(1);
                    toggle <= '1;
                 end
                 if (cyc == 4) begin
                    if (str_queue.q.size() != 1) $stop;
                    toggle <= '0;
                 end
                 else if (cyc == 10) begin
                    $write("*-* All Finished *-*\n");
                    $finish;
                 end
              end
           end
        
        endmodule
        
        module alpha (/*AUTOARG*/
           // Outputs
           toggle_up,
           // Inputs
           clk, toggle, cyc_copy
           );
        
           // t.a1 and t.a2 collapse to a count of 2
        
 000038    input clk;
+000038  point: comment=clk
        
%000004    input toggle;
-000004  point: comment=toggle
           // CHECK_COVER(-1,"top.t.a*",4)
           // 2 edges * (t.a1 and t.a2)
        
%000000    input [7:0] cyc_copy;
+000022  point: comment=cyc_copy[0]
+000010  point: comment=cyc_copy[1]
-000004  point: comment=cyc_copy[2]
-000002  point: comment=cyc_copy[3]
-000000  point: comment=cyc_copy[4]
-000000  point: comment=cyc_copy[5]
-000000  point: comment=cyc_copy[6]
-000000  point: comment=cyc_copy[7]
           // CHECK_COVER(-1,"top.t.a*","cyc_copy[0]",22)
           // CHECK_COVER(-2,"top.t.a*","cyc_copy[1]",10)
           // CHECK_COVER(-3,"top.t.a*","cyc_copy[2]",4)
           // CHECK_COVER(-4,"top.t.a*","cyc_copy[3]",2)
           // CHECK_COVER(-5,"top.t.a*","cyc_copy[4]",0)
           // CHECK_COVER(-6,"top.t.a*","cyc_copy[5]",0)
           // CHECK_COVER(-7,"top.t.a*","cyc_copy[6]",0)
           // CHECK_COVER(-8,"top.t.a*","cyc_copy[7]",0)
        
%000004    reg         toggle_internal;
-000004  point: comment=toggle_internal
           // CHECK_COVER(-1,"top.t.a*",4)
           // 2 edges * (t.a1 and t.a2)
        
%000004    output reg  toggle_up;
-000004  point: comment=toggle_up
           // CHECK_COVER(-1,"top.t.a*",4)
           // 2 edges * (t.a1 and t.a2)
        
           always @ (posedge clk) begin
              toggle_internal <= toggle;
              toggle_up       <= toggle;
           end
        endmodule
        
        module beta (/*AUTOARG*/
           // Inputs
           clk, toggle_up
           );
        
 000019    input clk;
+000019  point: comment=clk
        
%000002    input toggle_up;
-000002  point: comment=toggle_up
           // CHECK_COVER(-1,"top.t.b1","toggle_up",2)
        
           /* verilator public_module */
        
           always @ (posedge clk) begin
              if (0 && toggle_up) begin end
           end
        endmodule
        
        module off (/*AUTOARG*/
           // Inputs
           clk, toggle
           );
        
           // verilator coverage_off
           input clk;
           // CHECK_COVER_MISSING(-1)
        
           // verilator coverage_on
%000002    input toggle;
-000002  point: comment=toggle
           // CHECK_COVER(-1,"top.t.o1","toggle",2)
        
        endmodule
        
