$date
	Wed Feb 23 00:02:43 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var int 32 " cnt [31:0] $end
$var parameter 96 # v[0] [95:0] $end
$var parameter 96 $ v[1] [95:0] $end
$var parameter 96 % v[2] [95:0] $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b000100000000000000000000000000100001000000000000000000000000000100010000000000000000000000000000 %
b001000000000000000000000000000100010000000000000000000000000000100100000000000000000000000000000 $
b001100000000000000000000000000100011000000000000000000000000000100110000000000000000000000000000 #
b00000000000000000000000000000000 "
0!
$end
#10
1!
b00000000000000000000000000000001 "
#15
0!
#20
1!
b00000000000000000000000000000010 "
#25
0!
#30
1!
b00000000000000000000000000000011 "
#35
0!
#40
1!
