%Error: t/t_var_bad_sv.v:8:8: Unexpected 'do': 'do' is a SystemVerilog keyword misused as an identifier.
                            : ... Suggest modify the Verilog-2001 code to avoid SV keywords, or use `begin_keywords or --language.
    8 |    reg do;
      |        ^~
%Error: t/t_var_bad_sv.v:9:14: Unexpected 'do': 'do' is a SystemVerilog keyword misused as an identifier.
    9 |    mod mod (.do(bar));
      |              ^~
%Error: t/t_var_bad_sv.v:9:16: syntax error, unexpected '(', expecting ')'
    9 |    mod mod (.do(bar));
      |                ^
%Error: Exiting due to
