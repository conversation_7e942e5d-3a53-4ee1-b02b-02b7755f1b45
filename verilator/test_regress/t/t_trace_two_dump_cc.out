$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module topa $end
  $var wire 1 # clk $end
  $scope module t $end
   $var wire 1 # clk $end
   $var wire 32 $ cyc [31:0] $end
   $var wire 32 % c_trace_on [31:0] $end
   $scope module sub $end
    $var wire 32 & inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module topb $end
  $var wire 1 ( clk $end
  $scope module t $end
   $var wire 1 ( clk $end
   $var wire 32 + cyc [31:0] $end
   $var wire 32 , c_trace_on [31:0] $end
   $var real 64 ) r $end
   $scope module sub $end
    $var wire 32 - inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#10
1#
b00000000000000000000000000000001 $
b00000000000000000000000000000000 %
1(
r0 )
b00000000000000000000000000000001 &
b00000000000000000000000000000001 +
b00000000000000000000000000000000 ,
b00000000000000000000000000000010 -
#15
0#
0(
#20
1#
b00000000000000000000000000000010 $
b00000000000000000000000000000011 %
1(
r0.1 )
#25
0#
0(
#30
1#
b00000000000000000000000000000011 $
b00000000000000000000000000000100 %
1(
r0.2 )
#35
0#
0(
#40
1#
b00000000000000000000000000000100 $
b00000000000000000000000000000101 %
1(
r0.3 )
#45
0#
0(
#50
1#
b00000000000000000000000000000101 $
b00000000000000000000000000000110 %
1(
r0.4 )
#55
0#
0(
#60
1#
b00000000000000000000000000000110 $
b00000000000000000000000000000111 %
1(
r0.5 )
#65
0#
0(
#70
1#
b00000000000000000000000000000111 $
b00000000000000000000000000001000 %
1(
r0.6 )
#75
0#
0(
#80
1#
b00000000000000000000000000001000 $
b00000000000000000000000000001001 %
1(
r0.7 )
#85
0#
0(
#90
1#
b00000000000000000000000000001001 $
b00000000000000000000000000001010 %
1(
r0.7999999999999999 )
#95
0#
0(
#100
1#
b00000000000000000000000000001010 $
b00000000000000000000000000001011 %
1(
r0.8999999999999999 )
#105
0#
0(
#110
1#
b00000000000000000000000000001011 $
b00000000000000000000000000001100 %
1(
r0.9999999999999999 )
