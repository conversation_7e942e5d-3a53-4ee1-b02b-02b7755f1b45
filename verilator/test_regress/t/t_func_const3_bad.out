%Warning-WIDTHCONCAT: t/t_func_const3_bad.v:12:28: More than a 8k bit replication is probably wrong: 9000
                                                 : ... note: In instance 't.b9k.c9'
   12 |    localparam SOMEP = {BITS{1'b0}};
      |                            ^
                      ... For warning description see https://verilator.org/warn/WIDTHCONCAT?v=latest
                      ... Use "/* verilator lint_off WIDTHCONCAT */" and lint_on around source to disable this message.
%Error: Exiting due to
