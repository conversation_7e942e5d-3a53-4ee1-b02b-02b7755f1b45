%Error-UNSUPPORTED: t/t_interface_virtual_unsup.v:22:22: Unsupported: write to virtual interface in if condition
   22 |       if (write_data(vif.data)) $write("dummy op");
      |                      ^~~
                    ... For error description see https://verilator.org/warn/UNSUPPORTED?v=latest
%Error-UNSUPPORTED: t/t_interface_virtual_unsup.v:23:25: Unsupported: write to virtual interface in loop condition
   23 |       while (write_data(vif.data));
      |                         ^~~
%Error-UNSUPPORTED: t/t_interface_virtual_unsup.v:24:34: Unsupported: write to virtual interface in loop condition
   24 |       for (int i = 0; write_data(vif.data); i += int'(write_data(vif.data)));
      |                                  ^~~
%Error-UNSUPPORTED: t/t_interface_virtual_unsup.v:24:66: Unsupported: write to virtual interface in loop increment statement
   24 |       for (int i = 0; write_data(vif.data); i += int'(write_data(vif.data)));
      |                                                                  ^~~
%Error-UNSUPPORTED: t/t_interface_virtual_unsup.v:25:34: Unsupported: write to virtual interface in loop condition
   25 |       for (int i = 0; write_data(vif.data++); i++);
      |                                  ^~~
%Error: Exiting due to
