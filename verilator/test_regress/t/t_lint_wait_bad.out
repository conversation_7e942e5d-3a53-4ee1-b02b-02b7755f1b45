%Warning-WAITCONST: t/t_timing_wait1.v:52:12: Wait statement condition is constant
   52 |       wait(1);
      |            ^
                    ... For warning description see https://verilator.org/warn/WAITCONST?v=latest
                    ... Use "/* verilator lint_off WAITCONST */" and lint_on around source to disable this message.
%Warning-WAITCONST: t/t_timing_wait1.v:54:14: Wait statement condition is constant
   54 |       wait(0 < 1) $write("*-* All Finished *-*\n");
      |              ^
%Warning-WAITCONST: t/t_timing_wait1.v:59:19: Wait statement condition is constant
   59 |    initial wait(1 == 0) $stop;
      |                   ^~
%Error: Exiting due to
