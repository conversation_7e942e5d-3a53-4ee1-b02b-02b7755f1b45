$version Generated by VerilatedVcd $end
$timescale 1ps $end

 $scope module top1 $end
  $var wire 1 # clk $end
  $var wire 1 $ rst $end
  $var wire 32 % trace_number [31:0] $end
  $var wire 1 & stop $end
  $var wire 32 ' counter [31:0] $end
  $var wire 1 ( done_o $end
  $scope module top $end
   $var wire 1 # clk $end
   $var wire 1 $ rst $end
   $var wire 32 % trace_number [31:0] $end
   $var wire 1 & stop $end
   $var wire 32 ' counter [31:0] $end
   $var wire 1 ( done_o $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
1$
b00000000000000000000000000000000 %
1&
b00000000000000000000000000000000 '
0(
#1
1#
#2
0#
0$
#3
1#
b00000000000000000000000000000001 '
#4
0#
#5
1#
b00000000000000000000000000000010 '
#6
0#
#7
1#
b00000000000000000000000000000011 '
#8
0#
#9
1#
b00000000000000000000000000000100 '
#10
0#
#11
1#
b00000000000000000000000000000101 '
1(
