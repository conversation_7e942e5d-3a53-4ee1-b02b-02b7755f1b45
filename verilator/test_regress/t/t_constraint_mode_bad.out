%Error: t/t_constraint_mode_bad.v:21:15: Cannot call 'constraint_mode()' on a non-class variable
                                       : ... note: In instance 't'
   21 |       p.m_one.constraint_mode(0);
      |               ^~~~~~~~~~~~~~~
%Error: t/t_constraint_mode_bad.v:22:45: Cannot call 'constraint_mode()' as a function on a variable
                                       : ... note: In instance 't'
   22 |       $display("p.constraint_mode()=%0d", p.constraint_mode());
      |                                             ^~~~~~~~~~~~~~~
%Error: t/t_constraint_mode_bad.v:23:18: 'constraint_mode()' with arguments cannot be called as a function
                                       : ... note: In instance 't'
   23 |       $display(p.constraint_mode(0));
      |                  ^~~~~~~~~~~~~~~
%Warning-IGNOREDRETURN: t/t_constraint_mode_bad.v:24:14: Ignoring return value of non-void function (IEEE 1800-2023 13.4.1)
                                                       : ... note: In instance 't'
   24 |       p.cons.constraint_mode();
      |              ^~~~~~~~~~~~~~~
                        ... For warning description see https://verilator.org/warn/IGNOREDRETURN?v=latest
                        ... Use "/* verilator lint_off IGNOREDRETURN */" and lint_on around source to disable this message.
%Error: t/t_constraint_mode_bad.v:12:14: Cannot call 'constraint_mode()' as a function on a variable
                                       : ... note: In instance 't'
   12 |       return constraint_mode();
      |              ^~~~~~~~~~~~~~~
%Error: Exiting due to
