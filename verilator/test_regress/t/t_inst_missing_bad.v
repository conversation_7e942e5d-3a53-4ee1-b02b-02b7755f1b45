// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2012 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/);
   wire ok = 1'b0;
   // verilator lint_off UNDRIVEN
   wire nc;
   // verilator lint_on UNDRIVEN

   sub sub (ok, , nc);
endmodule

module sub (input ok, input none, input nc, input missing);
   initial if (ok && none && nc && missing) begin end  // No unused warning
endmodule
