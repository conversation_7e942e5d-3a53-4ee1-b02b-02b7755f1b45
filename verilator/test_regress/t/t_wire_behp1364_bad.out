%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:24:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2023 6.5): 'w'
                                                : ... note: In instance 't'
   24 |       w = 0;
      |       ^
                    ... For error description see https://verilator.org/warn/PROCASSWIRE?v=latest
%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:25:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2023 6.5): 'o'
                                                : ... note: In instance 't'
   25 |       o = 0;
      |       ^
%Error-PROCASSWIRE: t/t_wire_behp1364_bad.v:26:7: Procedural assignment to wire, perhaps intended var (IEEE 1800-2023 6.5): 'oa'
                                                : ... note: In instance 't'
   26 |       oa = 0;
      |       ^~
%Error: Exiting due to
