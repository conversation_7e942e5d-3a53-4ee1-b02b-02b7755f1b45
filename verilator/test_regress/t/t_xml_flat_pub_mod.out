<?xml version="1.0" ?>
<!-- DESCRIPTION: Verilator output: XML representation of netlist -->
<verilator_xml>
  <files>
    <file id="a" filename="&lt;built-in&gt;" language="1800-2023"/>
    <file id="b" filename="&lt;command-line&gt;" language="1800-2023"/>
    <file id="c" filename="input.vc" language="1800-2023"/>
    <file id="d" filename="t/t_xml_flat_pub_mod.v" language="1800-2023"/>
  </files>
  <module_files>
    <file id="d" filename="t/t_xml_flat_pub_mod.v" language="1800-2023"/>
  </module_files>
  <cells>
    <cell loc="d,11,8,11,11" name="$root" submodname="$root" hier="$root"/>
  </cells>
  <netlist>
    <module loc="d,11,8,11,11" name="$root" origName="$root" topModule="1" public="true">
      <var loc="d,11,24,11,29" name="i_clk" dtype_id="1" dir="input" pinIndex="1" vartype="logic" origName="i_clk" public="true"/>
      <var loc="d,11,24,11,29" name="top.i_clk" dtype_id="1" vartype="logic" origName="i_clk"/>
      <var loc="d,7,24,7,29" name="top.f.i_clk" dtype_id="1" vartype="logic" origName="i_clk"/>
      <topscope loc="d,11,8,11,11">
        <scope loc="d,11,8,11,11" name="TOP">
          <varscope loc="d,11,24,11,29" name="i_clk" dtype_id="1"/>
          <varscope loc="d,11,24,11,29" name="top.i_clk" dtype_id="1"/>
          <varscope loc="d,7,24,7,29" name="top.f.i_clk" dtype_id="1"/>
          <assignalias loc="d,11,24,11,29" dtype_id="1">
            <varref loc="d,11,24,11,29" name="i_clk" dtype_id="1"/>
            <varref loc="d,11,24,11,29" name="top.i_clk" dtype_id="1"/>
          </assignalias>
          <assignalias loc="d,7,24,7,29" dtype_id="1">
            <varref loc="d,12,7,12,8" name="top.i_clk" dtype_id="1"/>
            <varref loc="d,7,24,7,29" name="top.f.i_clk" dtype_id="1"/>
          </assignalias>
        </scope>
      </topscope>
    </module>
    <typetable loc="a,0,0,0,0">
      <basicdtype loc="d,11,18,11,23" id="1" name="logic"/>
    </typetable>
  </netlist>
</verilator_xml>
