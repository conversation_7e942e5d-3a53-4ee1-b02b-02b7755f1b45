// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Prototypes for DPI import and export functions.
//
// Verilator includes this file in all generated .cpp files that use DPI functions.
// Manually include this file where DPI .c import functions are declared to ensure
// the C functions match the expectations of the DPI imports.

#ifndef VERILATED_VT_DPI_ARG_OUTPUT_TYPE__DPI_H_
#define VERILATED_VT_DPI_ARG_OUTPUT_TYPE__DPI_H_  // guard

#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif


// DPI EXPORTS
extern void e_array_2_state_1(svBitVecVal* o);
extern void e_array_2_state_128(svBitVecVal* o);
extern void e_array_2_state_32(svBitVecVal* o);
extern void e_array_2_state_33(svBitVecVal* o);
extern void e_array_2_state_64(svBitVecVal* o);
extern void e_array_2_state_65(svBitVecVal* o);
extern void e_array_4_state_1(svLogicVecVal* o);
extern void e_array_4_state_128(svLogicVecVal* o);
extern void e_array_4_state_32(svLogicVecVal* o);
extern void e_array_4_state_33(svLogicVecVal* o);
extern void e_array_4_state_64(svLogicVecVal* o);
extern void e_array_4_state_65(svLogicVecVal* o);
extern void e_bit(svBit* o);
extern void e_bit_t(svBit* o);
extern void e_byte(char* o);
extern void e_byte_t(char* o);
extern void e_byte_unsigned(unsigned char* o);
extern void e_byte_unsigned_t(unsigned char* o);
extern void e_chandle(void** o);
extern void e_chandle_t(void** o);
extern void e_int(int* o);
extern void e_int_t(int* o);
extern void e_int_unsigned(unsigned int* o);
extern void e_int_unsigned_t(unsigned int* o);
extern void e_integer(svLogicVecVal* o);
extern void e_integer_t(svLogicVecVal* o);
extern void e_logic(svLogic* o);
extern void e_logic_t(svLogic* o);
extern void e_longint(long long* o);
extern void e_longint_t(long long* o);
extern void e_longint_unsigned(unsigned long long* o);
extern void e_longint_unsigned_t(unsigned long long* o);
extern void e_real(double* o);
extern void e_real_t(double* o);
extern void e_shortint(short* o);
extern void e_shortint_t(short* o);
extern void e_shortint_unsigned(unsigned short* o);
extern void e_shortint_unsigned_t(unsigned short* o);
extern void e_string(const char** o);
extern void e_string_t(const char** o);
extern void e_struct_2_state_1(svBitVecVal* o);
extern void e_struct_2_state_128(svBitVecVal* o);
extern void e_struct_2_state_32(svBitVecVal* o);
extern void e_struct_2_state_33(svBitVecVal* o);
extern void e_struct_2_state_64(svBitVecVal* o);
extern void e_struct_2_state_65(svBitVecVal* o);
extern void e_struct_4_state_1(svLogicVecVal* o);
extern void e_struct_4_state_128(svLogicVecVal* o);
extern void e_struct_4_state_32(svLogicVecVal* o);
extern void e_struct_4_state_33(svLogicVecVal* o);
extern void e_struct_4_state_64(svLogicVecVal* o);
extern void e_struct_4_state_65(svLogicVecVal* o);
extern void e_time(svLogicVecVal* o);
extern void e_time_t(svLogicVecVal* o);
extern void e_union_2_state_1(svBitVecVal* o);
extern void e_union_2_state_128(svBitVecVal* o);
extern void e_union_2_state_32(svBitVecVal* o);
extern void e_union_2_state_33(svBitVecVal* o);
extern void e_union_2_state_64(svBitVecVal* o);
extern void e_union_2_state_65(svBitVecVal* o);
extern void e_union_4_state_1(svLogicVecVal* o);
extern void e_union_4_state_128(svLogicVecVal* o);
extern void e_union_4_state_32(svLogicVecVal* o);
extern void e_union_4_state_33(svLogicVecVal* o);
extern void e_union_4_state_64(svLogicVecVal* o);
extern void e_union_4_state_65(svLogicVecVal* o);

// DPI IMPORTS
extern void check_exports();
extern void i_array_2_state_1(svBitVecVal* o);
extern void i_array_2_state_128(svBitVecVal* o);
extern void i_array_2_state_32(svBitVecVal* o);
extern void i_array_2_state_33(svBitVecVal* o);
extern void i_array_2_state_64(svBitVecVal* o);
extern void i_array_2_state_65(svBitVecVal* o);
extern void i_array_4_state_1(svLogicVecVal* o);
extern void i_array_4_state_128(svLogicVecVal* o);
extern void i_array_4_state_32(svLogicVecVal* o);
extern void i_array_4_state_33(svLogicVecVal* o);
extern void i_array_4_state_64(svLogicVecVal* o);
extern void i_array_4_state_65(svLogicVecVal* o);
extern void i_bit(svBit* o);
extern void i_bit_t(svBit* o);
extern void i_byte(char* o);
extern void i_byte_t(char* o);
extern void i_byte_unsigned(unsigned char* o);
extern void i_byte_unsigned_t(unsigned char* o);
extern void i_chandle(void** o);
extern void i_chandle_t(void** o);
extern void i_int(int* o);
extern void i_int_t(int* o);
extern void i_int_unsigned(unsigned int* o);
extern void i_int_unsigned_t(unsigned int* o);
extern void i_integer(svLogicVecVal* o);
extern void i_integer_t(svLogicVecVal* o);
extern void i_logic(svLogic* o);
extern void i_logic_t(svLogic* o);
extern void i_longint(long long* o);
extern void i_longint_t(long long* o);
extern void i_longint_unsigned(unsigned long long* o);
extern void i_longint_unsigned_t(unsigned long long* o);
extern void i_real(double* o);
extern void i_real_t(double* o);
extern void i_shortint(short* o);
extern void i_shortint_t(short* o);
extern void i_shortint_unsigned(unsigned short* o);
extern void i_shortint_unsigned_t(unsigned short* o);
extern void i_string(const char** o);
extern void i_string_t(const char** o);
extern void i_struct_2_state_1(svBitVecVal* o);
extern void i_struct_2_state_128(svBitVecVal* o);
extern void i_struct_2_state_32(svBitVecVal* o);
extern void i_struct_2_state_33(svBitVecVal* o);
extern void i_struct_2_state_64(svBitVecVal* o);
extern void i_struct_2_state_65(svBitVecVal* o);
extern void i_struct_4_state_1(svLogicVecVal* o);
extern void i_struct_4_state_128(svLogicVecVal* o);
extern void i_struct_4_state_32(svLogicVecVal* o);
extern void i_struct_4_state_33(svLogicVecVal* o);
extern void i_struct_4_state_64(svLogicVecVal* o);
extern void i_struct_4_state_65(svLogicVecVal* o);
extern void i_time(svLogicVecVal* o);
extern void i_time_t(svLogicVecVal* o);
extern void i_union_2_state_1(svBitVecVal* o);
extern void i_union_2_state_128(svBitVecVal* o);
extern void i_union_2_state_32(svBitVecVal* o);
extern void i_union_2_state_33(svBitVecVal* o);
extern void i_union_2_state_64(svBitVecVal* o);
extern void i_union_2_state_65(svBitVecVal* o);
extern void i_union_4_state_1(svLogicVecVal* o);
extern void i_union_4_state_128(svLogicVecVal* o);
extern void i_union_4_state_32(svLogicVecVal* o);
extern void i_union_4_state_33(svLogicVecVal* o);
extern void i_union_4_state_64(svLogicVecVal* o);
extern void i_union_4_state_65(svLogicVecVal* o);

#ifdef __cplusplus
}
#endif

#endif  // guard
