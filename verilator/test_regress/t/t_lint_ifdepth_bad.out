%Warning-IFDEPTH: t/t_lint_ifdepth_bad.v:22:12: Deep 'if' statement; suggest unique/priority to avoid slow logic
                                              : ... note: In instance 't'
   22 |       else if (value==11) begin end   
      |            ^~
                  ... For warning description see https://verilator.org/warn/IFDEPTH?v=latest
                  ... Use "/* verilator lint_off IFDEPTH */" and lint_on around source to disable this message.
%Error: Exiting due to
