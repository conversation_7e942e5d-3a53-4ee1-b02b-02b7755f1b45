%Warning-ASCRANGE: t/t_metacmt_onoff.v:8:8: Ascending bit range vector: left < right of bit range: [0:1]
                                          : ... note: In instance 't'
    8 |    reg [0:1] show1; /*verilator lint_off ASCRANGE*/  reg [0:2] ign2; /*verilator lint_on ASCRANGE*/   reg [0:3] show3;
      |        ^
                   ... For warning description see https://verilator.org/warn/ASCRANGE?v=latest
                   ... Use "/* verilator lint_off ASCRANGE */" and lint_on around source to disable this message.
%Warning-ASCRANGE: t/t_metacmt_onoff.v:8:107: Ascending bit range vector: left < right of bit range: [0:3]
                                            : ... note: In instance 't'
    8 |    reg [0:1] show1; /*verilator lint_off ASCRANGE*/  reg [0:2] ign2; /*verilator lint_on ASCRANGE*/   reg [0:3] show3;
      |                                                                                                           ^
%Error: Exiting due to
