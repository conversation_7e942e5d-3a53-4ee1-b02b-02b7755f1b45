#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
use IO::File;

scenarios(simulator => 1);

# Look for O(n^2) problems in process handling

sub gen {
    my $filename = shift;

    my $fh = IO::File->new(">$filename");
    $fh->print("// Generated by t_timing_long.pl\n");

    $fh->print("\n");
    $fh->print("`ifdef TEST_VERBOSE\n");
    $fh->print(" `define MSG(m) \$display m\n");
    $fh->print("`else\n");
    $fh->print(" `define MSG(m)\n");
    $fh->print("`endif\n");
    $fh->print("\n");

    $fh->print("module t;\n");
    $fh->print("\n");
    $fh->print("  int cnt;\n");
    $fh->print("\n");
    $fh->print("  initial begin\n");

    my $n = 100;
    for (my $i = 1; $i < $n; ++$i) {
        # If statement around the timing is important to make the code scheduling
        # mostly unpredictable
        $fh->printf("    if (cnt == %d) begin\n", $i - 1);
        $fh->printf("      #1; ++cnt; `MSG((\"[%0t] cnt?=${i}\", \$time));"
                    . " if (cnt != %d) \$stop;\n", $i);
        $fh->printf("    end\n");
    }

    $fh->print("\n");
    $fh->print('    $write("*-* All Finished *-*\n");', "\n");
    $fh->print('    $finish;', "\n");
    $fh->print("  end\n");
    $fh->print("endmodule\n");
}

top_filename("$Self->{obj_dir}/t_timing_long.v");

gen($Self->{top_filename});

if ($Self->have_coroutines) {
    compile(
        verilator_flags2 => ["--exe --build --main --tim" . "ing"],
        make_top => 1,
        );

    execute(
        check_finished => 1,
        );
}

compile(
    verilator_flags2 => ["--exe --build --main --no-timing -Wno-STMTDLY"],
    make_top => 1,
    );

execute(
    check_finished => 1,
    );

ok(1);
1;
