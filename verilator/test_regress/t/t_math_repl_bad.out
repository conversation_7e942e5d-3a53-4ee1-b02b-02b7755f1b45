%Error-ZEROREPL: t/t_math_repl_bad.v:12:14: Replication value of 0 is only legal under a concatenation (IEEE 1800-2023 *********)
                                          : ... note: In instance 't'
   12 |       o = {0 {1'b1}};   
      |              ^
                 ... For error description see https://verilator.org/warn/ZEROREPL?v=latest
%Warning-WIDTHEXPAND: t/t_math_repl_bad.v:12:9: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's REPLICATE generates 1 bits.
                                              : ... note: In instance 't'
   12 |       o = {0 {1'b1}};   
      |         ^
                      ... Use "/* verilator lint_off WIDTHEXPAND */" and lint_on around source to disable this message.
%Error: t/t_math_repl_bad.v:13:43: Replication value isn't a constant.
                                 : ... note: In instance 't'
   13 |       o = {$test$plusargs("NON-CONSTANT") {1'b1}};   
      |                                           ^
%Warning-WIDTHEXPAND: t/t_math_repl_bad.v:13:9: Operator ASSIGN expects 32 bits on the Assign RHS, but Assign RHS's REPLICATE generates 1 bits.
                                              : ... note: In instance 't'
   13 |       o = {$test$plusargs("NON-CONSTANT") {1'b1}};   
      |         ^
%Error: Exiting due to
