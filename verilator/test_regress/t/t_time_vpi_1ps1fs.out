:: In top.t
Time scale of t is 1ps / 1fs
[60000] time%0d=60  123%0t=123000
  dig%0t=5432109876543000 dig%0d=5432109876543
  rdig%0t=5432109876543210 rdig%0f=5432109876543.209961
  acc%0t=12345678901234567890000 acc%0d=12345678901234567890
[0.060000ns] time%0d=60  123%0t=0.123000ns
  dig%0t=5432109876.543000ns dig%0d=5432109876543
  rdig%0t=5432109876.543210ns rdig%0f=5432109876543.209961
  acc%0t=12345678901234567.890000ns acc%0d=12345678901234567890
[0.060000ns] stime%0t=0.060000ns  stime%0d=60  stime%0f=60.000000
[0.060000ns] rtime%0t=0.060000ns  rtime%0d=60  rtime%0f=60.000000
global svGetTime = 0 0,60000
global svGetTimeUnit = 0 -12  svGetTmePrecision = 0 -15
global vpiSimTime = 0,60000  vpiScaledRealTime = 60000
global vpiTimeUnit = -12  vpiTimePrecision = -15
top.t svGetTime = 0 0,60000
top.t svGetTimeUnit = 0 -12  svGetTmePrecision = 0 -15
top.t vpiSimTime = 0,60000  vpiScaledRealTime = 60
top.t vpiTimeUnit = -12  vpiTimePrecision = -15
*-* All Finished *-*
