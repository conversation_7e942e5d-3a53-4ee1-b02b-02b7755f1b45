%Warning-UNOPTFLAT: t/t_unopt_combo.v:23:25: Signal unoptimizable: Circular combinational logic: 't.b'
   23 |    wire [31:0]          b;                       
      |                         ^
                    ... For warning description see https://verilator.org/warn/UNOPTFLAT?v=latest
                    ... Use "/* verilator lint_off UNOPTFLAT */" and lint_on around source to disable this message.
                    t/t_unopt_combo.v:23:25:      Example path: t.b
                    t/t_unopt_combo.v:124:4:      Example path: ALWAYS
                    t/t_unopt_combo.v:24:25:      Example path: t.c
                    t/t_unopt_combo.v:81:4:      Example path: ALWAYS
                    t/t_unopt_combo.v:23:25:      Example path: t.b
%Error: Exiting due to
