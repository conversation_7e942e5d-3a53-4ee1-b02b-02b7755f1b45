{"type": "NETLIST", "name": "$root", "addr": "(B)", "loc": "a,0:0,0:0", "timeunit": "1ps", "timeprecision": "1ps", "typeTablep": "(C)", "constPoolp": "(D)", "dollarUnitPkgp": "(E)", "stdPackagep": "UNLINKED", "evalp": "(F)", "evalNbap": "(G)", "dpiExportTriggerp": "UNLINKED", "delaySchedulerp": "UNLINKED", "nbaEventp": "UNLINKED", "nbaEventTriggerp": "UNLINKED", "topScopep": "(H)", "modulesp": [{"type": "MODULE", "name": "$root", "addr": "(I)", "loc": "f,9:8,9:9", "origName": "$root", "level": 1, "modPublic": true, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "clk", "addr": "(J)", "loc": "f,13:10,13:13", "dtypep": "(K)", "origName": "clk", "isSc": false, "isPrimaryIO": true, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": true, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "clker", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlFirstIteration", "addr": "(L)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VstlFirstIteration", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(N)", "loc": "f,9:8,9:9", "dtypep": "(K)", "origName": "__Vtrigprevexpr___TOP__clk__0", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactContinue", "addr": "(O)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VactContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.cyc", "addr": "(P)", "loc": "f,15:12,15:15", "dtypep": "(Q)", "origName": "cyc", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.in", "addr": "(R)", "loc": "f,19:12,19:14", "dtypep": "(S)", "origName": "in", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.fr_chk", "addr": "(T)", "loc": "f,22:12,22:18", "dtypep": "(S)", "origName": "fr_chk", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.sub.fr_a", "addr": "(U)", "loc": "f,82:15,82:19", "dtypep": "(S)", "origName": "fr_a", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.sub.fr_b", "addr": "(V)", "loc": "f,83:15,83:19", "dtypep": "(S)", "origName": "fr_b", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.sub.in", "addr": "(W)", "loc": "f,81:14,81:16", "dtypep": "(S)", "origName": "in", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactIterCount", "addr": "(X)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "origName": "__VactIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlTriggered", "addr": "(Z)", "loc": "f,9:8,9:9", "dtypep": "(AB)", "origName": "__VstlTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactTriggered", "addr": "(BB)", "loc": "f,9:8,9:9", "dtypep": "(CB)", "origName": "__VactTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaTriggered", "addr": "(DB)", "loc": "f,9:8,9:9", "dtypep": "(CB)", "origName": "__VnbaTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CELL", "name": "$unit", "addr": "(EB)", "loc": "a,0:0,0:0", "origName": "__024unit", "recursive": false, "modp": "(E)", "pinsp": [], "paramsp": [], "rangep": [], "intfRefsp": []}, {"type": "SCIMPHDR", "name": "", "addr": "(FB)", "loc": "f,71:20,73:1", "shortText": "..."}, {"type": "SCIMPHDR", "name": "", "addr": "(GB)", "loc": "f,73:1,74:1", "shortText": "  void mon_register_a(const char* namep, void* sigp, bool isOut);..."}, {"type": "TOPSCOPE", "name": "", "addr": "(H)", "loc": "f,9:8,9:9", "senTreesp": [], "scopep": [{"type": "SCOPE", "name": "TOP", "addr": "(HB)", "loc": "f,9:8,9:9", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(I)", "varsp": [], "blocksp": []}]}, {"type": "CFUNC", "name": "mon_eval", "addr": "(IB)", "loc": "f,62:38,62:46", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": true, "dpiImportWrapper": false, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "mon_scope_name", "addr": "(KB)", "loc": "f,58:38,58:52", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": true, "dpiImportWrapper": false, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "formatted", "addr": "(LB)", "loc": "f,58:67,58:76", "dtypep": "(MB)", "origName": "formatted", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(NB)", "loc": "f,58:67,58:76", "varrefp": [{"type": "VARREF", "name": "formatted", "addr": "(OB)", "loc": "f,58:67,58:76", "dtypep": "(MB)", "access": "WR", "varp": "(LB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "mon_register_b", "addr": "(PB)", "loc": "f,60:38,60:52", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": true, "dpiImportWrapper": false, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "name", "addr": "(QB)", "loc": "f,60:60,60:64", "dtypep": "(MB)", "origName": "name", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(RB)", "loc": "f,60:60,60:64", "varrefp": [{"type": "VARREF", "name": "name", "addr": "(SB)", "loc": "f,60:60,60:64", "dtypep": "(MB)", "access": "WR", "varp": "(QB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "isOut", "addr": "(TB)", "loc": "f,60:70,60:75", "dtypep": "(S)", "origName": "isOut", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(UB)", "loc": "f,60:70,60:75", "varrefp": [{"type": "VARREF", "name": "isOut", "addr": "(VB)", "loc": "f,60:70,60:75", "dtypep": "(S)", "access": "WR", "varp": "(TB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "mon_register_done", "addr": "(WB)", "loc": "f,61:38,61:55", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": true, "dpiImportWrapper": false, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_eval_static", "addr": "(XB)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(YB)", "loc": "f,9:8,9:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(ZB)", "loc": "f,9:8,9:9", "dtypep": "(AC)", "funcName": "_eval_static__TOP", "funcp": "(BC)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_static__TOP", "addr": "(BC)", "loc": "f,9:8,9:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(CC)", "loc": "f,15:18,15:19", "dtypep": "(Q)", "rhsp": [{"type": "CONST", "name": "32'sh0", "addr": "(DC)", "loc": "f,15:18,15:19", "dtypep": "(EC)"}], "lhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(FC)", "loc": "f,15:18,15:19", "dtypep": "(Q)", "access": "WR", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial", "addr": "(GC)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(HC)", "loc": "f,9:8,9:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(IC)", "loc": "f,9:8,9:9", "dtypep": "(AC)", "funcName": "_eval_initial__TOP", "funcp": "(JC)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(KC)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "rhsp": [{"type": "VARREF", "name": "clk", "addr": "(MC)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(NC)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial__TOP", "addr": "(JC)", "loc": "f,9:8,9:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "UCSTMT", "name": "", "addr": "(OC)", "loc": "f,91:7,91:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(PC)", "loc": "f,91:10,91:41", "shortText": "mon_class_name(this->name());"}]}, {"type": "COMMENT", "name": "Function: mon_scope_name", "addr": "(QC)", "loc": "f,92:7,92:21"}, {"type": "STMTEXPR", "name": "", "addr": "(RC)", "loc": "f,92:7,92:21", "exprp": [{"type": "CCALL", "name": "", "addr": "(SC)", "loc": "f,92:7,92:21", "dtypep": "(AC)", "funcName": "__Vdpiimwrap_mon_scope_name_TOP____024unit", "funcp": "(TC)", "argsp": [{"type": "SCOPENAME", "name": "", "addr": "(UC)", "loc": "f,92:7,92:21", "dtypep": "(VC)", "dpiExport": false, "forFormat": false, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(WC)", "loc": "f,92:7,92:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(XC)", "loc": "f,92:7,92:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(YC)", "loc": "f,92:7,92:21", "shortText": "__DOT__sub"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(ZC)", "loc": "f,92:7,92:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(AD)", "loc": "f,92:7,92:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(BD)", "loc": "f,92:7,92:21", "shortText": "__DOT__sub"}]}, {"type": "CEXPR", "name": "", "addr": "(CD)", "loc": "f,92:7,92:21", "dtypep": "(DD)", "exprsp": [{"type": "TEXT", "name": "", "addr": "(ED)", "loc": "f,92:7,92:21", "shortText": "\"t/t_dpi_var.v\""}]}, {"type": "CONST", "name": "32'h5c", "addr": "(FD)", "loc": "f,92:7,92:21", "dtypep": "(GD)"}, {"type": "SFORMATF", "name": "%m", "addr": "(HD)", "loc": "f,92:7,92:21", "dtypep": "(MB)", "exprsp": [], "scopeNamep": [{"type": "SCOPENAME", "name": "", "addr": "(ID)", "loc": "f,92:7,92:21", "dtypep": "(VC)", "dpiExport": false, "forFormat": true, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(JD)", "loc": "f,92:7,92:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(KD)", "loc": "f,92:7,92:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(LD)", "loc": "f,92:7,92:21", "shortText": "__DOT__sub"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(MD)", "loc": "f,92:7,92:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(ND)", "loc": "f,92:7,92:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(OD)", "loc": "f,92:7,92:21", "shortText": "__DOT__sub"}]}]}]}]}, {"type": "UCSTMT", "name": "", "addr": "(PD)", "loc": "f,94:7,94:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(QD)", "loc": "f,94:10,94:36", "shortText": "mon_register_a(\"in\", &"}, {"type": "VARREF", "name": "t.in", "addr": "(RD)", "loc": "f,94:38,94:40", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}, {"type": "TEXT", "name": "", "addr": "(SD)", "loc": "f,94:42,94:53", "shortText": ", false);"}]}, {"type": "UCSTMT", "name": "", "addr": "(TD)", "loc": "f,95:7,95:9", "exprsp": [{"type": "TEXT", "name": "", "addr": "(UD)", "loc": "f,95:10,95:38", "shortText": "mon_register_a(\"fr_a\", &"}, {"type": "VARREF", "name": "t.sub.fr_a", "addr": "(VD)", "loc": "f,95:40,95:44", "dtypep": "(S)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}, {"type": "TEXT", "name": "", "addr": "(WD)", "loc": "f,95:46,95:56", "shortText": ", true);"}]}, {"type": "COMMENT", "name": "Function: mon_register_b", "addr": "(XD)", "loc": "f,97:7,97:21"}, {"type": "STMTEXPR", "name": "", "addr": "(YD)", "loc": "f,97:7,97:21", "exprp": [{"type": "CCALL", "name": "", "addr": "(ZD)", "loc": "f,97:7,97:21", "dtypep": "(AC)", "funcName": "__Vdpiimwrap_mon_register_b_TOP____024unit", "funcp": "(AE)", "argsp": [{"type": "SCOPENAME", "name": "", "addr": "(BE)", "loc": "f,97:7,97:21", "dtypep": "(VC)", "dpiExport": false, "forFormat": false, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(CE)", "loc": "f,97:7,97:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(DE)", "loc": "f,97:7,97:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(EE)", "loc": "f,97:7,97:21", "shortText": "__DOT__sub"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(FE)", "loc": "f,97:7,97:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(GE)", "loc": "f,97:7,97:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(HE)", "loc": "f,97:7,97:21", "shortText": "__DOT__sub"}]}, {"type": "CEXPR", "name": "", "addr": "(IE)", "loc": "f,97:7,97:21", "dtypep": "(DD)", "exprsp": [{"type": "TEXT", "name": "", "addr": "(JE)", "loc": "f,97:7,97:21", "shortText": "\"t/t_dpi_var.v\""}]}, {"type": "CONST", "name": "32'h61", "addr": "(KE)", "loc": "f,97:7,97:21", "dtypep": "(GD)"}, {"type": "CONST", "name": "\\\"in\\\"", "addr": "(LE)", "loc": "f,97:22,97:26", "dtypep": "(MB)"}, {"type": "CONST", "name": "32'sh0", "addr": "(ME)", "loc": "f,97:28,97:29", "dtypep": "(EC)"}]}]}, {"type": "COMMENT", "name": "Function: mon_register_b", "addr": "(NE)", "loc": "f,98:7,98:21"}, {"type": "STMTEXPR", "name": "", "addr": "(OE)", "loc": "f,98:7,98:21", "exprp": [{"type": "CCALL", "name": "", "addr": "(PE)", "loc": "f,98:7,98:21", "dtypep": "(AC)", "funcName": "__Vdpiimwrap_mon_register_b_TOP____024unit", "funcp": "(AE)", "argsp": [{"type": "SCOPENAME", "name": "", "addr": "(QE)", "loc": "f,98:7,98:21", "dtypep": "(VC)", "dpiExport": false, "forFormat": false, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(RE)", "loc": "f,98:7,98:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(SE)", "loc": "f,98:7,98:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(TE)", "loc": "f,98:7,98:21", "shortText": "__DOT__sub"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(UE)", "loc": "f,98:7,98:21", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(VE)", "loc": "f,98:7,98:21", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(WE)", "loc": "f,98:7,98:21", "shortText": "__DOT__sub"}]}, {"type": "CEXPR", "name": "", "addr": "(XE)", "loc": "f,98:7,98:21", "dtypep": "(DD)", "exprsp": [{"type": "TEXT", "name": "", "addr": "(YE)", "loc": "f,98:7,98:21", "shortText": "\"t/t_dpi_var.v\""}]}, {"type": "CONST", "name": "32'h62", "addr": "(ZE)", "loc": "f,98:7,98:21", "dtypep": "(GD)"}, {"type": "CONST", "name": "\\\"fr_b\\\"", "addr": "(AF)", "loc": "f,98:22,98:28", "dtypep": "(MB)"}, {"type": "CONST", "name": "32'sh1", "addr": "(BF)", "loc": "f,98:30,98:31", "dtypep": "(EC)"}]}]}, {"type": "COMMENT", "name": "Function: mon_register_done", "addr": "(CF)", "loc": "f,99:7,99:24"}, {"type": "STMTEXPR", "name": "", "addr": "(DF)", "loc": "f,99:7,99:24", "exprp": [{"type": "CCALL", "name": "", "addr": "(EF)", "loc": "f,99:7,99:24", "dtypep": "(AC)", "funcName": "__Vdpiimwrap_mon_register_done_TOP____024unit", "funcp": "(FF)", "argsp": [{"type": "SCOPENAME", "name": "", "addr": "(GF)", "loc": "f,99:7,99:24", "dtypep": "(VC)", "dpiExport": false, "forFormat": false, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(HF)", "loc": "f,99:7,99:24", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(IF)", "loc": "f,99:7,99:24", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(JF)", "loc": "f,99:7,99:24", "shortText": "__DOT__sub"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(KF)", "loc": "f,99:7,99:24", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(LF)", "loc": "f,99:7,99:24", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(MF)", "loc": "f,99:7,99:24", "shortText": "__DOT__sub"}]}, {"type": "CEXPR", "name": "", "addr": "(NF)", "loc": "f,99:7,99:24", "dtypep": "(DD)", "exprsp": [{"type": "TEXT", "name": "", "addr": "(OF)", "loc": "f,99:7,99:24", "shortText": "\"t/t_dpi_var.v\""}]}, {"type": "CONST", "name": "32'h63", "addr": "(PF)", "loc": "f,99:7,99:24", "dtypep": "(GD)"}]}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_final", "addr": "(QF)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_eval_settle", "addr": "(RF)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VstlIterCount", "addr": "(SF)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "origName": "__VstlIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VstlContinue", "addr": "(TF)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VstlContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(UF)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(VF)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "lhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(WF)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(SF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XF)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(YF)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(ZF)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(AG)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(BG)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(CG)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(TF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(DG)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(EG)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(TF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(FG)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(GG)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(HG)", "loc": "a,0:0,0:0", "dtypep": "(GD)"}], "rhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(IG)", "loc": "a,0:0,0:0", "dtypep": "(Y)", "access": "RD", "varp": "(SF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(JG)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(KG)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(LG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(MG)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_dump_triggers__stl", "funcp": "(NG)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(OG)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(PG)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_dpi_var.v\", 9, \"\", "}, {"type": "TEXT", "name": "", "addr": "(QG)", "loc": "a,0:0,0:0", "shortText": "\"Settle region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(RG)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "ADD", "name": "", "addr": "(SG)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(TG)", "loc": "f,9:8,9:9", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(UG)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(VG)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "RD", "varp": "(SF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VstlIterCount", "addr": "(WG)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(SF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XG)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(YG)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(ZG)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(TF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(AH)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(BH)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "funcName": "_eval_phase__stl", "funcp": "(CH)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(DH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(EH)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VstlContinue", "addr": "(FH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(TF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(GH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(HH)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(IH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "incsp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_triggers__stl", "addr": "(JH)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(KH)", "loc": "f,9:8,9:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(LH)", "loc": "f,9:8,9:9", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(MH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(NH)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}, {"type": "CCAST", "name": "", "addr": "(OH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__VstlFirstIteration", "addr": "(PH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "RD", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}, {"type": "TEXTBLOCK", "name": "", "addr": "(QH)", "loc": "f,9:8,9:9", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(RH)", "loc": "f,9:8,9:9", "shortText": "#ifdef VL_DEBUG..."}, {"type": "TEXT", "name": "", "addr": "(SH)", "loc": "f,9:8,9:9", "shortText": "if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {..."}, {"type": "STMTEXPR", "name": "", "addr": "(TH)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(UH)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_dump_triggers__stl", "funcp": "(NG)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(VH)", "loc": "f,9:8,9:9", "shortText": "}..."}, {"type": "TEXT", "name": "", "addr": "(WH)", "loc": "f,9:8,9:9", "shortText": "#endif..."}]}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__stl", "addr": "(NG)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(XH)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(YH)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(ZH)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(AI)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(BI)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(CI)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(DI)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "RD", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(EI)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(FI)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(GI)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(HI)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(II)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(JI)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "RD", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(KI)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(LI)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         'stl' region trigger index 0 is active: Internal 'stl' trigger - first iteration\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_stl_sequent__TOP__0", "addr": "(MI)", "loc": "f,81:14,81:16", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGNALIAS", "name": "", "addr": "(NI)", "loc": "f,81:14,81:16", "dtypep": "(S)", "rhsp": [{"type": "VARREF", "name": "t.in", "addr": "(OI)", "loc": "f,23:8,23:11", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "t.sub.in", "addr": "(PI)", "loc": "f,81:14,81:16", "dtypep": "(S)", "access": "WR", "varp": "(W)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNW", "name": "", "addr": "(QI)", "loc": "f,87:21,87:22", "dtypep": "(S)", "rhsp": [{"type": "ADD", "name": "", "addr": "(RI)", "loc": "f,87:26,87:27", "dtypep": "(EC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(SI)", "loc": "f,87:28,87:29", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(TI)", "loc": "f,87:28,87:29", "dtypep": "(EC)"}]}], "rhsp": [{"type": "VARREF", "name": "t.in", "addr": "(UI)", "loc": "f,19:12,19:14", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "t.fr_chk", "addr": "(VI)", "loc": "f,22:12,22:18", "dtypep": "(S)", "access": "WR", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": [], "strengthSpecp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_stl", "addr": "(WI)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(XI)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(YI)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(ZI)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(AJ)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(BJ)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "RD", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(CJ)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(DJ)", "loc": "f,81:14,81:16", "exprp": [{"type": "CCALL", "name": "", "addr": "(EJ)", "loc": "f,81:14,81:16", "dtypep": "(AC)", "funcName": "_stl_sequent__TOP__0", "funcp": "(MI)", "argsp": []}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__stl", "addr": "(CH)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VstlExecute", "addr": "(FJ)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VstlExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(GJ)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(HJ)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_eval_triggers__stl", "funcp": "(JH)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(IJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(JJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VstlTriggered", "addr": "(KJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(LJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "WR", "varp": "(FJ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(MJ)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(NJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(FJ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(OJ)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(PJ)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_eval_stl", "funcp": "(WI)", "argsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(QJ)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VstlExecute", "addr": "(RJ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(FJ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_triggers__act", "addr": "(SJ)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(TJ)", "loc": "f,9:8,9:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(UJ)", "loc": "f,9:8,9:9", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(VJ)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "WR", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(XJ)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}, {"type": "AND", "name": "", "addr": "(YJ)", "loc": "f,26:14,26:21", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(ZJ)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "VARREF", "name": "clk", "addr": "(AK)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "rhsp": [{"type": "NOT", "name": "", "addr": "(BK)", "loc": "f,26:14,26:21", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(CK)", "loc": "f,26:14,26:21", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(DK)", "loc": "f,26:14,26:21", "dtypep": "(LC)", "access": "RD", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(EK)", "loc": "f,9:8,9:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(FK)", "loc": "f,9:8,9:9", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(GK)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "WR", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h1", "addr": "(HK)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}, {"type": "AND", "name": "", "addr": "(IK)", "loc": "f,49:13,49:20", "dtypep": "(LC)", "lhsp": [{"type": "NOT", "name": "", "addr": "(JK)", "loc": "f,49:13,49:20", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(KK)", "loc": "f,13:10,13:13", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "VARREF", "name": "clk", "addr": "(LK)", "loc": "f,13:10,13:13", "dtypep": "(LC)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}], "rhsp": [{"type": "CCAST", "name": "", "addr": "(MK)", "loc": "f,49:13,49:20", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(NK)", "loc": "f,49:13,49:20", "dtypep": "(LC)", "access": "RD", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}]}, {"type": "ASSIGN", "name": "", "addr": "(OK)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "rhsp": [{"type": "VARREF", "name": "clk", "addr": "(PK)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(QK)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "TEXTBLOCK", "name": "", "addr": "(RK)", "loc": "f,9:8,9:9", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(SK)", "loc": "f,9:8,9:9", "shortText": "#ifdef VL_DEBUG..."}, {"type": "TEXT", "name": "", "addr": "(TK)", "loc": "f,9:8,9:9", "shortText": "if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {..."}, {"type": "STMTEXPR", "name": "", "addr": "(UK)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(VK)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_dump_triggers__act", "funcp": "(WK)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(XK)", "loc": "f,9:8,9:9", "shortText": "}..."}, {"type": "TEXT", "name": "", "addr": "(YK)", "loc": "f,9:8,9:9", "shortText": "#endif..."}]}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__act", "addr": "(WK)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(ZK)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(AL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(BL)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(CL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(DL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(EL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(FL)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(GL)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(HL)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(IL)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(JL)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(KL)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(LL)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(ML)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(NL)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         'act' region trigger index 0 is active: @(posedge clk)\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(OL)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(PL)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h2", "addr": "(QL)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(RL)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(SL)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(TL)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(UL)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         'act' region trigger index 1 is active: @(negedge clk)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__nba", "addr": "(VL)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(WL)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(XL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(YL)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(ZL)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(AM)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(BM)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(CM)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(DM)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(EM)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(FM)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(GM)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(HM)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(IM)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(JM)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(KM)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         'nba' region trigger index 0 is active: @(posedge clk)\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(LM)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(MM)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h2", "addr": "(NM)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(OM)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(PM)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(QM)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(RM)", "loc": "f,9:8,9:9", "shortText": "VL_DBG_MSGF(\"         'nba' region trigger index 1 is active: @(negedge clk)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_act", "addr": "(SM)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_nba_sequent__TOP__0", "addr": "(TM)", "loc": "f,50:7,50:15", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(UM)", "loc": "f,50:7,50:15", "exprp": [{"type": "CCALL", "name": "", "addr": "(VM)", "loc": "f,50:7,50:15", "dtypep": "(AC)", "funcName": "__Vdpiimwrap_mon_eval_TOP____024unit", "funcp": "(WM)", "argsp": [{"type": "SCOPENAME", "name": "", "addr": "(XM)", "loc": "f,50:7,50:15", "dtypep": "(VC)", "dpiExport": false, "forFormat": false, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(YM)", "loc": "f,50:7,50:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(ZM)", "loc": "f,50:7,50:15", "shortText": "__DOT__t"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(AN)", "loc": "f,50:7,50:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(BN)", "loc": "f,50:7,50:15", "shortText": "__DOT__t"}]}, {"type": "CEXPR", "name": "", "addr": "(CN)", "loc": "f,50:7,50:15", "dtypep": "(DD)", "exprsp": [{"type": "TEXT", "name": "", "addr": "(DN)", "loc": "f,50:7,50:15", "shortText": "\"t/t_dpi_var.v\""}]}, {"type": "CONST", "name": "32'h32", "addr": "(EN)", "loc": "f,50:7,50:15", "dtypep": "(GD)"}]}]}, {"type": "ALWAYSPUBLIC", "name": "", "addr": "(FN)", "loc": "f,82:15,82:19", "sensesp": [], "stmtsp": [{"type": "VARREF", "name": "t.sub.fr_a", "addr": "(GN)", "loc": "f,82:15,82:19", "dtypep": "(S)", "access": "WR", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "ALWAYSPUBLIC", "name": "", "addr": "(HN)", "loc": "f,83:15,83:19", "sensesp": [], "stmtsp": [{"type": "VARREF", "name": "t.sub.fr_b", "addr": "(IN)", "loc": "f,83:15,83:19", "dtypep": "(S)", "access": "WR", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_nba_sequent__TOP__1", "addr": "(JN)", "loc": "f,30:7,30:10", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__Vdly__t.cyc", "addr": "(KN)", "loc": "f,15:12,15:15", "dtypep": "(Q)", "origName": "__Vdly__t__DOT__cyc", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(LN)", "loc": "f,15:12,15:15", "varrefp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(MN)", "loc": "f,15:12,15:15", "dtypep": "(Q)", "access": "WR", "varp": "(KN)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "stmtsp": [{"type": "ASSIGNPRE", "name": "", "addr": "(NN)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(ON)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(PN)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "access": "WR", "varp": "(KN)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(QN)", "loc": "f,30:11,30:13", "dtypep": "(Q)", "rhsp": [{"type": "ADD", "name": "", "addr": "(RN)", "loc": "f,30:18,30:19", "dtypep": "(Q)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(SN)", "loc": "f,30:20,30:21", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(TN)", "loc": "f,30:20,30:21", "dtypep": "(EC)"}]}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(UN)", "loc": "f,30:14,30:17", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(VN)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "access": "WR", "varp": "(KN)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(WN)", "loc": "f,31:10,31:12", "dtypep": "(S)", "rhsp": [{"type": "OR", "name": "", "addr": "(XN)", "loc": "f,31:22,31:23", "dtypep": "(GD)", "lhsp": [{"type": "SHIFTL", "name": "", "addr": "(YN)", "loc": "f,31:22,31:23", "dtypep": "(GD)", "lhsp": [{"type": "VARREF", "name": "t.in", "addr": "(ZN)", "loc": "f,31:14,31:16", "dtypep": "(AO)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "CONST", "name": "32'h1", "addr": "(BO)", "loc": "f,31:22,31:23", "dtypep": "(GD)"}]}], "rhsp": [{"type": "AND", "name": "", "addr": "(CO)", "loc": "f,31:36,31:37", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(DO)", "loc": "f,31:36,31:37", "dtypep": "(LC)"}], "rhsp": [{"type": "REDXOR", "name": "", "addr": "(EO)", "loc": "f,31:24,31:26", "dtypep": "(LC)", "lhsp": [{"type": "AND", "name": "", "addr": "(FO)", "loc": "f,31:24,31:26", "dtypep": "(GD)", "lhsp": [{"type": "CONST", "name": "32'h80000005", "addr": "(GO)", "loc": "f,31:24,31:26", "dtypep": "(GD)"}], "rhsp": [{"type": "VARREF", "name": "t.in", "addr": "(HO)", "loc": "f,31:24,31:26", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}]}], "lhsp": [{"type": "VARREF", "name": "t.in", "addr": "(IO)", "loc": "f,31:7,31:9", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(JO)", "loc": "f,32:7,32:9", "condp": [{"type": "EQ", "name": "", "addr": "(KO)", "loc": "f,32:14,32:16", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'sh0", "addr": "(LO)", "loc": "f,32:16,32:17", "dtypep": "(EC)"}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(MO)", "loc": "f,32:11,32:14", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "ASSIGNDLY", "name": "", "addr": "(NO)", "loc": "f,34:13,34:15", "dtypep": "(S)", "rhsp": [{"type": "CONST", "name": "32'hd70a4497", "addr": "(OO)", "loc": "f,34:16,34:28", "dtypep": "(GD)"}], "lhsp": [{"type": "VARREF", "name": "t.in", "addr": "(PO)", "loc": "f,34:10,34:12", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": [{"type": "IF", "name": "", "addr": "(QO)", "loc": "f,36:12,36:14", "condp": [{"type": "LTES", "name": "", "addr": "(RO)", "loc": "f,36:19,36:20", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'sh3", "addr": "(SO)", "loc": "f,36:20,36:21", "dtypep": "(EC)"}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(TO)", "loc": "f,36:16,36:19", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "IF", "name": "", "addr": "(UO)", "loc": "f,38:12,38:14", "condp": [{"type": "GTS", "name": "", "addr": "(VO)", "loc": "f,38:19,38:20", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'sha", "addr": "(WO)", "loc": "f,38:20,38:22", "dtypep": "(EC)"}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(XO)", "loc": "f,38:16,38:19", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "IF", "name": "", "addr": "(YO)", "loc": "f,39:10,39:12", "condp": [{"type": "NEQ", "name": "", "addr": "(ZO)", "loc": "f,39:21,39:23", "dtypep": "(LC)", "lhsp": [{"type": "VARREF", "name": "t.fr_chk", "addr": "(AP)", "loc": "f,39:14,39:20", "dtypep": "(S)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "VARREF", "name": "t.sub.fr_a", "addr": "(BP)", "loc": "f,39:24,39:28", "dtypep": "(S)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "STOP", "name": "", "addr": "(CP)", "loc": "f,39:30,39:35"}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(DP)", "loc": "f,40:10,40:12", "condp": [{"type": "NEQ", "name": "", "addr": "(EP)", "loc": "f,40:21,40:23", "dtypep": "(LC)", "lhsp": [{"type": "VARREF", "name": "t.fr_chk", "addr": "(FP)", "loc": "f,40:14,40:20", "dtypep": "(S)", "access": "RD", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "VARREF", "name": "t.sub.fr_b", "addr": "(GP)", "loc": "f,40:24,40:28", "dtypep": "(S)", "access": "RD", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "STOP", "name": "", "addr": "(HP)", "loc": "f,40:30,40:35"}], "elsesp": []}], "elsesp": [{"type": "IF", "name": "", "addr": "(IP)", "loc": "f,42:12,42:14", "condp": [{"type": "EQ", "name": "", "addr": "(JP)", "loc": "f,42:19,42:21", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'sha", "addr": "(KP)", "loc": "f,42:21,42:23", "dtypep": "(EC)"}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(LP)", "loc": "f,42:16,42:19", "dtypep": "(Q)", "access": "RD", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "DISPLAY", "name": "", "addr": "(MP)", "loc": "f,43:10,43:16", "fmtp": [{"type": "SFORMATF", "name": "*-* All Finished *-*\\n", "addr": "(NP)", "loc": "f,43:10,43:16", "dtypep": "(MB)", "exprsp": [], "scopeNamep": []}], "filep": []}, {"type": "FINISH", "name": "", "addr": "(OP)", "loc": "f,44:10,44:17"}], "elsesp": []}]}], "elsesp": []}]}, {"type": "ASSIGNPOST", "name": "", "addr": "(PP)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "rhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(QP)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "access": "RD", "varp": "(KN)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(RP)", "loc": "f,30:7,30:10", "dtypep": "(Q)", "access": "WR", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNALIAS", "name": "", "addr": "(SP)", "loc": "f,81:14,81:16", "dtypep": "(S)", "rhsp": [{"type": "VARREF", "name": "t.in", "addr": "(TP)", "loc": "f,23:8,23:11", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "t.sub.in", "addr": "(UP)", "loc": "f,81:14,81:16", "dtypep": "(S)", "access": "WR", "varp": "(W)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNW", "name": "", "addr": "(VP)", "loc": "f,87:21,87:22", "dtypep": "(S)", "rhsp": [{"type": "ADD", "name": "", "addr": "(WP)", "loc": "f,87:26,87:27", "dtypep": "(EC)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(XP)", "loc": "f,87:28,87:29", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(YP)", "loc": "f,87:28,87:29", "dtypep": "(EC)"}]}], "rhsp": [{"type": "VARREF", "name": "t.in", "addr": "(ZP)", "loc": "f,19:12,19:14", "dtypep": "(S)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "t.fr_chk", "addr": "(AQ)", "loc": "f,22:12,22:18", "dtypep": "(S)", "access": "WR", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": [], "strengthSpecp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_nba", "addr": "(G)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(BQ)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(CQ)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h2", "addr": "(DQ)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(EQ)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(FQ)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(GQ)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(HQ)", "loc": "f,50:7,50:15", "exprp": [{"type": "CCALL", "name": "", "addr": "(IQ)", "loc": "f,50:7,50:15", "dtypep": "(AC)", "funcName": "_nba_sequent__TOP__0", "funcp": "(TM)", "argsp": []}]}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(JQ)", "loc": "f,9:8,9:9", "condp": [{"type": "AND", "name": "", "addr": "(KQ)", "loc": "f,9:8,9:9", "dtypep": "(DD)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(LQ)", "loc": "f,9:8,9:9", "dtypep": "(DD)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(MQ)", "loc": "f,9:8,9:9", "dtypep": "(VC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(NQ)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(OQ)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(PQ)", "loc": "f,30:7,30:10", "exprp": [{"type": "CCALL", "name": "", "addr": "(QQ)", "loc": "f,30:7,30:10", "dtypep": "(AC)", "funcName": "_nba_sequent__TOP__1", "funcp": "(JN)", "argsp": []}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__act", "addr": "(RQ)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VpreTriggered", "addr": "(SQ)", "loc": "f,9:8,9:9", "dtypep": "(CB)", "origName": "__VpreTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactExecute", "addr": "(TQ)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VactExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(UQ)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(VQ)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_eval_triggers__act", "funcp": "(SJ)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(WQ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(XQ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(YQ)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(ZQ)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "WR", "varp": "(TQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(AR)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(BR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(TQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(CR)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "andNot", "addr": "(DR)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VpreTriggered", "addr": "(ER)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "WR", "varp": "(SQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(FR)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}, {"type": "VARREF", "name": "__VnbaTriggered", "addr": "(GR)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(HR)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "thisOr", "addr": "(IR)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(JR)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "WR", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(KR)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "RD", "varp": "(BB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(LR)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(MR)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_eval_act", "funcp": "(SM)", "argsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(NR)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(OR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(TQ)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__nba", "addr": "(PR)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaExecute", "addr": "(QR)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VnbaExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(RR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(SR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(TR)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "RD", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(UR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "WR", "varp": "(QR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(VR)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(WR)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(QR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(XR)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(YR)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_eval_nba", "funcp": "(G)", "argsp": []}]}, {"type": "STMTEXPR", "name": "", "addr": "(ZR)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "clear", "addr": "(AS)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(BS)", "loc": "a,0:0,0:0", "dtypep": "(WJ)", "access": "WR", "varp": "(DB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(CS)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(DS)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(QR)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval", "addr": "(F)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(HB)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaIterCount", "addr": "(ES)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "origName": "__VnbaIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaContinue", "addr": "(FS)", "loc": "f,9:8,9:9", "dtypep": "(M)", "origName": "__VnbaContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(GS)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(HS)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(IS)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(ES)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(JS)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(KS)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(LS)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(FS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(MS)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(NS)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(FS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(OS)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(PS)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(QS)", "loc": "a,0:0,0:0", "dtypep": "(GD)"}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(RS)", "loc": "a,0:0,0:0", "dtypep": "(Y)", "access": "RD", "varp": "(ES)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(SS)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(TS)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(US)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(VS)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_dump_triggers__nba", "funcp": "(VL)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(WS)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(XS)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_dpi_var.v\", 9, \"\", "}, {"type": "TEXT", "name": "", "addr": "(YS)", "loc": "a,0:0,0:0", "shortText": "\"NBA region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(ZS)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "ADD", "name": "", "addr": "(AT)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(BT)", "loc": "f,9:8,9:9", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(CT)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(DT)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "RD", "varp": "(ES)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(ET)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(ES)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(FT)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(GT)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(HT)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(FS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(IT)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(JT)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(KT)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(LT)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(MT)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(NT)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(OT)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(PT)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "access": "RD", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(QT)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(RT)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(ST)", "loc": "a,0:0,0:0", "dtypep": "(GD)"}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(TT)", "loc": "a,0:0,0:0", "dtypep": "(Y)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(UT)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(VT)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(WT)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(XT)", "loc": "a,0:0,0:0", "dtypep": "(AC)", "funcName": "_dump_triggers__act", "funcp": "(WK)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(YT)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(ZT)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_dpi_var.v\", 9, \"\", "}, {"type": "TEXT", "name": "", "addr": "(AU)", "loc": "a,0:0,0:0", "shortText": "\"Active region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(BU)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "rhsp": [{"type": "ADD", "name": "", "addr": "(CU)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(DU)", "loc": "f,9:8,9:9", "dtypep": "(GD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(EU)", "loc": "f,9:8,9:9", "dtypep": "(GD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(FU)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "RD", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(GU)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "access": "WR", "varp": "(X)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(HU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(IU)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(JU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(KU)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(LU)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "funcName": "_eval_phase__act", "funcp": "(RQ)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(MU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(NU)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(OU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}, {"type": "IF", "name": "", "addr": "(PU)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(QU)", "loc": "a,0:0,0:0", "dtypep": "(LC)", "funcName": "_eval_phase__nba", "funcp": "(PR)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(RU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(SU)", "loc": "f,9:8,9:9", "dtypep": "(LC)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(TU)", "loc": "f,9:8,9:9", "dtypep": "(LC)", "access": "WR", "varp": "(FS)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_debug_assertions", "addr": "(UU)", "loc": "f,9:8,9:9", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(VU)", "loc": "f,13:10,13:13", "condp": [{"type": "AND", "name": "", "addr": "(WU)", "loc": "f,13:10,13:13", "dtypep": "(K)", "lhsp": [{"type": "VARREF", "name": "clk", "addr": "(XU)", "loc": "f,13:10,13:13", "dtypep": "(K)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "CONST", "name": "8'hfe", "addr": "(YU)", "loc": "f,13:10,13:13", "dtypep": "(ZU)"}]}], "thensp": [{"type": "CSTMT", "name": "", "addr": "(AV)", "loc": "f,13:10,13:13", "exprsp": [{"type": "TEXT", "name": "", "addr": "(BV)", "loc": "f,13:10,13:13", "shortText": "Verilated::overWidthError(\"clk\");"}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(CV)", "loc": "f,9:8,9:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "CRESET", "name": "", "addr": "(DV)", "loc": "f,13:10,13:13", "varrefp": [{"type": "VARREF", "name": "clk", "addr": "(EV)", "loc": "f,13:10,13:13", "dtypep": "(K)", "access": "WR", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(FV)", "loc": "f,15:12,15:15", "varrefp": [{"type": "VARREF", "name": "t.cyc", "addr": "(GV)", "loc": "f,15:12,15:15", "dtypep": "(Q)", "access": "WR", "varp": "(P)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(HV)", "loc": "f,19:12,19:14", "varrefp": [{"type": "VARREF", "name": "t.in", "addr": "(IV)", "loc": "f,19:12,19:14", "dtypep": "(S)", "access": "WR", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(JV)", "loc": "f,22:12,22:18", "varrefp": [{"type": "VARREF", "name": "t.fr_chk", "addr": "(KV)", "loc": "f,22:12,22:18", "dtypep": "(S)", "access": "WR", "varp": "(T)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(LV)", "loc": "f,82:15,82:19", "varrefp": [{"type": "VARREF", "name": "t.sub.fr_a", "addr": "(MV)", "loc": "f,82:15,82:19", "dtypep": "(S)", "access": "WR", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(NV)", "loc": "f,83:15,83:19", "varrefp": [{"type": "VARREF", "name": "t.sub.fr_b", "addr": "(OV)", "loc": "f,83:15,83:19", "dtypep": "(S)", "access": "WR", "varp": "(V)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(PV)", "loc": "f,81:14,81:16", "varrefp": [{"type": "VARREF", "name": "t.sub.in", "addr": "(QV)", "loc": "f,81:14,81:16", "dtypep": "(S)", "access": "WR", "varp": "(W)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(RV)", "loc": "f,9:8,9:9", "varrefp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(SV)", "loc": "f,9:8,9:9", "dtypep": "(K)", "access": "WR", "varp": "(N)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CUSE", "name": "$unit", "addr": "(TV)", "loc": "a,0:0,0:0", "useType": "INT_FWD"}], "activesp": []}, {"type": "PACKAGE", "name": "$unit", "addr": "(E)", "loc": "a,0:0,0:0", "origName": "__024unit", "level": 3, "modPublic": false, "inLibrary": true, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "$unit", "addr": "(JB)", "loc": "a,0:0,0:0", "aboveScopep": "(HB)", "aboveCellp": "(EB)", "modp": "(E)", "varsp": [], "blocksp": []}, {"type": "CFUNC", "name": "__Vdpiimwrap_mon_scope_name_TOP__$unit", "addr": "(TC)", "loc": "f,58:38,58:52", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": true, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "__Vscopep", "addr": "(UV)", "loc": "f,58:38,58:52", "dtypep": "(VV)", "origName": "__Vscopep", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(WV)", "loc": "f,58:38,58:52", "varrefp": [{"type": "VARREF", "name": "__Vscopep", "addr": "(XV)", "loc": "f,58:38,58:52", "dtypep": "(VV)", "access": "WR", "varp": "(UV)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(YV)", "loc": "f,58:38,58:52", "dtypep": "(ZV)", "origName": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(AW)", "loc": "f,58:38,58:52", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(BW)", "loc": "f,58:38,58:52", "dtypep": "(ZV)", "access": "WR", "varp": "(YV)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON>", "addr": "(CW)", "loc": "f,58:38,58:52", "dtypep": "(S)", "origName": "__<PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(DW)", "loc": "f,58:38,58:52", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON>", "addr": "(EW)", "loc": "f,58:38,58:52", "dtypep": "(S)", "access": "WR", "varp": "(CW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "formatted", "addr": "(FW)", "loc": "f,58:67,58:76", "dtypep": "(MB)", "origName": "formatted", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(GW)", "loc": "f,58:67,58:76", "varrefp": [{"type": "VARREF", "name": "formatted", "addr": "(HW)", "loc": "f,58:67,58:76", "dtypep": "(MB)", "access": "WR", "varp": "(FW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [{"type": "CSTMT", "name": "", "addr": "(IW)", "loc": "f,58:67,58:76", "exprsp": [{"type": "TEXT", "name": "", "addr": "(JW)", "loc": "f,58:67,58:76", "shortText": "const char* formatted__Vcvt;..."}]}, {"type": "CSTMT", "name": "", "addr": "(KW)", "loc": "f,58:67,58:76", "exprsp": [{"type": "TEXT", "name": "", "addr": "(LW)", "loc": "f,58:67,58:76", "shortText": "for (size_t formatted__Vidx = 0; formatted__Vidx < 1; ++formatted__Vidx) formatted__Vcvt = formatted.c_str();..."}]}, {"type": "CSTMT", "name": "", "addr": "(MW)", "loc": "f,58:38,58:52", "exprsp": [{"type": "TEXT", "name": "", "addr": "(NW)", "loc": "f,58:38,58:52", "shortText": "Verilated::dpiContext(__<PERSON>scope<PERSON>, __<PERSON><PERSON>lenamep, __<PERSON><PERSON><PERSON>);..."}]}, {"type": "STMTEXPR", "name": "", "addr": "(OW)", "loc": "f,58:38,58:52", "exprp": [{"type": "CCALL", "name": "", "addr": "(PW)", "loc": "f,58:38,58:52", "dtypep": "(AC)", "funcName": "mon_scope_name", "funcp": "(KB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "__Vdpiimwrap_mon_register_b_TOP__$unit", "addr": "(AE)", "loc": "f,60:38,60:52", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": true, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "__Vscopep", "addr": "(QW)", "loc": "f,60:38,60:52", "dtypep": "(VV)", "origName": "__Vscopep", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(RW)", "loc": "f,60:38,60:52", "varrefp": [{"type": "VARREF", "name": "__Vscopep", "addr": "(SW)", "loc": "f,60:38,60:52", "dtypep": "(VV)", "access": "WR", "varp": "(QW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(TW)", "loc": "f,60:38,60:52", "dtypep": "(ZV)", "origName": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(UW)", "loc": "f,60:38,60:52", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(VW)", "loc": "f,60:38,60:52", "dtypep": "(ZV)", "access": "WR", "varp": "(TW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON>", "addr": "(WW)", "loc": "f,60:38,60:52", "dtypep": "(S)", "origName": "__<PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(XW)", "loc": "f,60:38,60:52", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON>", "addr": "(YW)", "loc": "f,60:38,60:52", "dtypep": "(S)", "access": "WR", "varp": "(WW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "name", "addr": "(ZW)", "loc": "f,60:60,60:64", "dtypep": "(MB)", "origName": "name", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(AX)", "loc": "f,60:60,60:64", "varrefp": [{"type": "VARREF", "name": "name", "addr": "(BX)", "loc": "f,60:60,60:64", "dtypep": "(MB)", "access": "WR", "varp": "(ZW)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "isOut", "addr": "(CX)", "loc": "f,60:70,60:75", "dtypep": "(S)", "origName": "isOut", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "VAUTOM", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(DX)", "loc": "f,60:70,60:75", "varrefp": [{"type": "VARREF", "name": "isOut", "addr": "(EX)", "loc": "f,60:70,60:75", "dtypep": "(S)", "access": "WR", "varp": "(CX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [{"type": "CSTMT", "name": "", "addr": "(FX)", "loc": "f,60:60,60:64", "exprsp": [{"type": "TEXT", "name": "", "addr": "(GX)", "loc": "f,60:60,60:64", "shortText": "const char* name__Vcvt;..."}]}, {"type": "CSTMT", "name": "", "addr": "(HX)", "loc": "f,60:60,60:64", "exprsp": [{"type": "TEXT", "name": "", "addr": "(IX)", "loc": "f,60:60,60:64", "shortText": "for (size_t name__Vidx = 0; name__Vidx < 1; ++name__Vidx) name__Vcvt = name.c_str();..."}]}, {"type": "CSTMT", "name": "", "addr": "(JX)", "loc": "f,60:70,60:75", "exprsp": [{"type": "TEXT", "name": "", "addr": "(KX)", "loc": "f,60:70,60:75", "shortText": "int isOut__Vcvt;..."}]}, {"type": "CSTMT", "name": "", "addr": "(LX)", "loc": "f,60:70,60:75", "exprsp": [{"type": "TEXT", "name": "", "addr": "(MX)", "loc": "f,60:70,60:75", "shortText": "for (size_t isOut__Vidx = 0; isOut__Vidx < 1; ++isOut__Vidx) isOut__Vcvt = isOut;..."}]}, {"type": "CSTMT", "name": "", "addr": "(NX)", "loc": "f,60:38,60:52", "exprsp": [{"type": "TEXT", "name": "", "addr": "(OX)", "loc": "f,60:38,60:52", "shortText": "Verilated::dpiContext(__<PERSON>scope<PERSON>, __<PERSON><PERSON>lenamep, __<PERSON><PERSON><PERSON>);..."}]}, {"type": "STMTEXPR", "name": "", "addr": "(PX)", "loc": "f,60:38,60:52", "exprp": [{"type": "CCALL", "name": "", "addr": "(QX)", "loc": "f,60:38,60:52", "dtypep": "(AC)", "funcName": "mon_register_b", "funcp": "(PB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "__Vdpiimwrap_mon_register_done_TOP__$unit", "addr": "(FF)", "loc": "f,61:38,61:55", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": true, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "__Vscopep", "addr": "(RX)", "loc": "f,61:38,61:55", "dtypep": "(VV)", "origName": "__Vscopep", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(SX)", "loc": "f,61:38,61:55", "varrefp": [{"type": "VARREF", "name": "__Vscopep", "addr": "(TX)", "loc": "f,61:38,61:55", "dtypep": "(VV)", "access": "WR", "varp": "(RX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(UX)", "loc": "f,61:38,61:55", "dtypep": "(ZV)", "origName": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(VX)", "loc": "f,61:38,61:55", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(WX)", "loc": "f,61:38,61:55", "dtypep": "(ZV)", "access": "WR", "varp": "(UX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON>", "addr": "(XX)", "loc": "f,61:38,61:55", "dtypep": "(S)", "origName": "__<PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(YX)", "loc": "f,61:38,61:55", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON>", "addr": "(ZX)", "loc": "f,61:38,61:55", "dtypep": "(S)", "access": "WR", "varp": "(XX)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [{"type": "CSTMT", "name": "", "addr": "(AY)", "loc": "f,61:38,61:55", "exprsp": [{"type": "TEXT", "name": "", "addr": "(BY)", "loc": "f,61:38,61:55", "shortText": "Verilated::dpiContext(__<PERSON>scope<PERSON>, __<PERSON><PERSON>lenamep, __<PERSON><PERSON><PERSON>);..."}]}, {"type": "STMTEXPR", "name": "", "addr": "(CY)", "loc": "f,61:38,61:55", "exprp": [{"type": "CCALL", "name": "", "addr": "(DY)", "loc": "f,61:38,61:55", "dtypep": "(AC)", "funcName": "mon_register_done", "funcp": "(WB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "__Vdpiimwrap_mon_eval_TOP__$unit", "addr": "(WM)", "loc": "f,62:38,62:46", "slow": false, "isStatic": true, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": true, "dpiContext": true, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(JB)", "argsp": [{"type": "VAR", "name": "__Vscopep", "addr": "(EY)", "loc": "f,62:38,62:46", "dtypep": "(VV)", "origName": "__Vscopep", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(FY)", "loc": "f,62:38,62:46", "varrefp": [{"type": "VARREF", "name": "__Vscopep", "addr": "(GY)", "loc": "f,62:38,62:46", "dtypep": "(VV)", "access": "WR", "varp": "(EY)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(HY)", "loc": "f,62:38,62:46", "dtypep": "(ZV)", "origName": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(IY)", "loc": "f,62:38,62:46", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON><PERSON><PERSON>", "addr": "(JY)", "loc": "f,62:38,62:46", "dtypep": "(ZV)", "access": "WR", "varp": "(HY)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "VAR", "name": "__<PERSON><PERSON><PERSON>", "addr": "(KY)", "loc": "f,62:38,62:46", "dtypep": "(S)", "origName": "__<PERSON><PERSON><PERSON>", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(LY)", "loc": "f,62:38,62:46", "varrefp": [{"type": "VARREF", "name": "__<PERSON><PERSON><PERSON>", "addr": "(MY)", "loc": "f,62:38,62:46", "dtypep": "(S)", "access": "WR", "varp": "(KY)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "initsp": [], "stmtsp": [{"type": "CSTMT", "name": "", "addr": "(NY)", "loc": "f,62:38,62:46", "exprsp": [{"type": "TEXT", "name": "", "addr": "(OY)", "loc": "f,62:38,62:46", "shortText": "Verilated::dpiContext(__<PERSON>scope<PERSON>, __<PERSON><PERSON>lenamep, __<PERSON><PERSON><PERSON>);..."}]}, {"type": "STMTEXPR", "name": "", "addr": "(PY)", "loc": "f,62:38,62:46", "exprp": [{"type": "CCALL", "name": "", "addr": "(QY)", "loc": "f,62:38,62:46", "dtypep": "(AC)", "funcName": "mon_eval", "funcp": "(IB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(RY)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}], "activesp": []}], "filesp": [{"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt__Syms.cpp", "addr": "(SY)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt__Syms.h", "addr": "(TY)", "loc": "a,0:0,0:0", "source": false, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt__Dpi.h", "addr": "(UY)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt__Dpi.cpp", "addr": "(VY)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt.h", "addr": "(WY)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt.cpp", "addr": "(XY)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root.h", "addr": "(YY)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$unit.h", "addr": "(ZY)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root__Slow.cpp", "addr": "(AZ)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root__DepSet_h0a8a96be__0__Slow.cpp", "addr": "(BZ)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root__DepSet_h81e6ecb0__0__Slow.cpp", "addr": "(CZ)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root__DepSet_h0a8a96be__0.cpp", "addr": "(DZ)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$root__DepSet_h81e6ecb0__0.cpp", "addr": "(EZ)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$unit__Slow.cpp", "addr": "(FZ)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$unit__DepSet_h81468a10__0__Slow.cpp", "addr": "(GZ)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_dpi_var_vlt/Vt_dpi_var_vlt_$unit__DepSet_h0a6aebde__0.cpp", "addr": "(HZ)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}], "miscsp": [{"type": "TYPETABLE", "name": "", "addr": "(C)", "loc": "a,0:0,0:0", "constraintRefp": "UNLINKED", "emptyQueuep": "UNLINKED", "queueIndexp": "UNLINKED", "streamp": "UNLINKED", "voidp": "(AC)", "typesp": [{"type": "BASICDTYPE", "name": "logic", "addr": "(K)", "loc": "d,50:22,50:24", "dtypep": "(K)", "keyword": "logic", "generic": true, "rangep": []}, {"type": "VOIDDTYPE", "name": "", "addr": "(AC)", "loc": "d,51:21,51:30", "dtypep": "(AC)", "generic": false}, {"type": "BASICDTYPE", "name": "string", "addr": "(MB)", "loc": "d,156:10,156:16", "dtypep": "(MB)", "keyword": "string", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "QData", "addr": "(VC)", "loc": "f,50:7,50:15", "dtypep": "(VC)", "keyword": "QData", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "integer", "addr": "(Q)", "loc": "f,15:4,15:11", "dtypep": "(Q)", "keyword": "integer", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "int", "addr": "(S)", "loc": "f,19:4,19:7", "dtypep": "(S)", "keyword": "int", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(EC)", "loc": "f,15:18,15:19", "dtypep": "(EC)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(GD)", "loc": "f,31:17,31:19", "dtypep": "(GD)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VerilatedScope*", "addr": "(VV)", "loc": "f,62:38,62:46", "dtypep": "(VV)", "keyword": "VerilatedScope*", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "char*", "addr": "(ZV)", "loc": "f,62:38,62:46", "dtypep": "(ZV)", "keyword": "char*", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(DD)", "loc": "f,50:7,50:15", "dtypep": "(DD)", "keyword": "logic", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VlTriggerVec", "addr": "(AB)", "loc": "f,9:8,9:9", "dtypep": "(AB)", "keyword": "VlTriggerVec", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(M)", "loc": "f,9:8,9:9", "dtypep": "(M)", "keyword": "bit", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(Y)", "loc": "f,9:8,9:9", "dtypep": "(Y)", "keyword": "bit", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VlTriggerVec", "addr": "(CB)", "loc": "f,9:8,9:9", "dtypep": "(CB)", "keyword": "VlTriggerVec", "range": "1:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(LC)", "loc": "f,26:22,26:25", "dtypep": "(LC)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(WJ)", "loc": "f,9:8,9:9", "dtypep": "(WJ)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(AO)", "loc": "f,31:16,31:17", "dtypep": "(AO)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(ZU)", "loc": "f,13:10,13:13", "dtypep": "(ZU)", "keyword": "logic", "range": "7:0", "generic": true, "rangep": []}]}, {"type": "CONSTPOOL", "name": "", "addr": "(D)", "loc": "a,0:0,0:0", "modulep": [{"type": "MODULE", "name": "@CONST-POOL@", "addr": "(IZ)", "loc": "a,0:0,0:0", "origName": "@CONST-POOL@", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "NONE", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "TOP", "addr": "(JZ)", "loc": "a,0:0,0:0", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(IZ)", "varsp": [], "blocksp": []}], "activesp": []}]}]}