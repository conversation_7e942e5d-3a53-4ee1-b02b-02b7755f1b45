$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module top $end
  $var wire 1 ( clk $end
  $scope module t $end
   $var wire 1 ( clk $end
   $var wire 32 # cyc [31:0] $end
   $scope module c5_data $end
    $var wire 1 $ valid $end
    $var wire 4 % value [3:0] $end
    $var wire 1 ) reset $end
   $upscope $end
   $scope module c6_data $end
    $var wire 1 & valid $end
    $var wire 4 ' value [3:0] $end
    $var wire 1 * reset $end
   $upscope $end
   $scope module cif2 $end
   $upscope $end
   $scope module cif3 $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000001 #
0$
b0000 %
0&
b0000 '
0(
0)
0*
#10
b00000000000000000000000000000010 #
1(
#15
0(
#20
b00000000000000000000000000000011 #
b1111 %
1&
b1010 '
1(
#25
0(
#30
b00000000000000000000000000000100 #
1(
#35
0(
#40
b00000000000000000000000000000101 #
1(
#45
0(
#50
b00000000000000000000000000000110 #
1(
#55
0(
#60
b00000000000000000000000000000111 #
1(
#65
0(
#70
b00000000000000000000000000001000 #
1(
#75
0(
#80
b00000000000000000000000000001001 #
1(
#85
0(
#90
b00000000000000000000000000001010 #
1(
#95
0(
#100
b00000000000000000000000000001011 #
1(
#105
0(
#110
b00000000000000000000000000001100 #
1(
#115
0(
#120
b00000000000000000000000000001101 #
1(
#125
0(
#130
b00000000000000000000000000001110 #
1(
#135
0(
#140
b00000000000000000000000000001111 #
1(
#145
0(
#150
b00000000000000000000000000010000 #
1(
#155
0(
#160
b00000000000000000000000000010001 #
1(
#165
0(
#170
b00000000000000000000000000010010 #
1(
#175
0(
#180
b00000000000000000000000000010011 #
1(
#185
0(
#190
b00000000000000000000000000010100 #
1(
#195
0(
#200
b00000000000000000000000000010101 #
1(
