$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module top $end
  $var wire 1 # clk $end
  $var wire 1 $ reset_l $end
  $scope module t $end
   $var wire 1 # clk $end
   $var wire 1 $ reset_l $end
   $scope module u0_sub_top $end
    $var wire 1 # clk $end
    $var wire 1 $ reset_l $end
   $upscope $end
   $scope module u1_sub_top $end
    $var wire 1 # clk $end
    $var wire 1 $ reset_l $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top $end
  $var wire 1 & clk $end
  $var wire 1 ' reset_l $end
  $scope module sub_top $end
   $var wire 1 & clk $end
   $var wire 1 ' reset_l $end
   $scope module u0 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u1 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u2 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u3 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u4 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u5 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u6 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
   $scope module u7 $end
    $var wire 1 & clk $end
    $var wire 1 ' reset_l $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top $end
  $var wire 1 ) clk $end
  $var wire 1 * reset_l $end
  $scope module sub_top $end
   $var wire 1 ) clk $end
   $var wire 1 * reset_l $end
   $scope module u0 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u1 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u2 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u3 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u4 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u5 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u6 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
   $scope module u7 $end
    $var wire 1 ) clk $end
    $var wire 1 * reset_l $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u0 $end
  $var wire 1 , clk $end
  $var wire 1 - reset_l $end
  $scope module detail_code $end
   $var wire 1 , clk $end
   $var wire 1 - reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u1 $end
  $var wire 1 / clk $end
  $var wire 1 0 reset_l $end
  $scope module detail_code $end
   $var wire 1 / clk $end
   $var wire 1 0 reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u2 $end
  $var wire 1 2 clk $end
  $var wire 1 3 reset_l $end
  $scope module detail_code $end
   $var wire 1 2 clk $end
   $var wire 1 3 reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u3 $end
  $var wire 1 5 clk $end
  $var wire 1 6 reset_l $end
  $scope module detail_code $end
   $var wire 1 5 clk $end
   $var wire 1 6 reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u4 $end
  $var wire 1 8 clk $end
  $var wire 1 9 reset_l $end
  $scope module detail_code $end
   $var wire 1 8 clk $end
   $var wire 1 9 reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u5 $end
  $var wire 1 ; clk $end
  $var wire 1 < reset_l $end
  $scope module detail_code $end
   $var wire 1 ; clk $end
   $var wire 1 < reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u6 $end
  $var wire 1 > clk $end
  $var wire 1 ? reset_l $end
  $scope module detail_code $end
   $var wire 1 > clk $end
   $var wire 1 ? reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u0_sub_top.sub_top.u7 $end
  $var wire 1 A clk $end
  $var wire 1 B reset_l $end
  $scope module detail_code $end
   $var wire 1 A clk $end
   $var wire 1 B reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u0 $end
  $var wire 1 D clk $end
  $var wire 1 E reset_l $end
  $scope module detail_code $end
   $var wire 1 D clk $end
   $var wire 1 E reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u1 $end
  $var wire 1 G clk $end
  $var wire 1 H reset_l $end
  $scope module detail_code $end
   $var wire 1 G clk $end
   $var wire 1 H reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u2 $end
  $var wire 1 J clk $end
  $var wire 1 K reset_l $end
  $scope module detail_code $end
   $var wire 1 J clk $end
   $var wire 1 K reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u3 $end
  $var wire 1 M clk $end
  $var wire 1 N reset_l $end
  $scope module detail_code $end
   $var wire 1 M clk $end
   $var wire 1 N reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u4 $end
  $var wire 1 P clk $end
  $var wire 1 Q reset_l $end
  $scope module detail_code $end
   $var wire 1 P clk $end
   $var wire 1 Q reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u5 $end
  $var wire 1 S clk $end
  $var wire 1 T reset_l $end
  $scope module detail_code $end
   $var wire 1 S clk $end
   $var wire 1 T reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u6 $end
  $var wire 1 V clk $end
  $var wire 1 W reset_l $end
  $scope module detail_code $end
   $var wire 1 V clk $end
   $var wire 1 W reset_l $end
  $upscope $end
 $upscope $end
 $scope module top.t.u1_sub_top.sub_top.u7 $end
  $var wire 1 Y clk $end
  $var wire 1 Z reset_l $end
  $scope module detail_code $end
   $var wire 1 Y clk $end
   $var wire 1 Z reset_l $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
0$
0&
0'
0)
0*
0,
0-
0/
00
02
03
05
06
08
09
0;
0<
0>
0?
0A
0B
0D
0E
0G
0H
0J
0K
0M
0N
0P
0Q
0S
0T
0V
0W
0Y
0Z
