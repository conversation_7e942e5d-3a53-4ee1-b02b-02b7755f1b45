%Error: t/t_interface_array_bad.v:23:16: Expecting expression to be constant, but variable isn't const: 'bar'
                                       : ... note: In instance 't'
   23 |    assign foos[bar].a = 1'b1;
      |                ^~~
%Error: t/t_interface_array_bad.v:23:15: Could not expand constant selection inside dotted reference: 'bar'
                                       : ... note: In instance 't'
   23 |    assign foos[bar].a = 1'b1;
      |               ^
%Error: Exiting due to
