%Warning-PINMISSING: t/t_inst_pin_place_bad.v:21:7: Cell has missing pin: 'pin_1'
   21 |     ) i_sub (
      |       ^~~~~
                     t/t_inst_pin_place_bad.v:11:11: ... Location of port declaration
   11 |     input pin_1
      |           ^~~~~
                     ... For warning description see https://verilator.org/warn/PINMISSING?v=latest
                     ... Use "/* verilator lint_off PINMISSING */" and lint_on around source to disable this message.
%Error: t/t_inst_pin_place_bad.v:22:10: Instance attempts to connect to 'PARAM_A', but it is a parameter
   22 |         .PARAM_A(1)
      |          ^~~~~~~
%Error: t/t_inst_pin_place_bad.v:20:10: Instance attempts to override 'pin_1' as a parameter, but it is a port
   20 |         .pin_1(1)
      |          ^~~~~
%Error: Exiting due to
