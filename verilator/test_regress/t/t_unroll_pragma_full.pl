#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(vlt => 1);

top_filename("t/t_unroll_pragma.v");

compile(
    verilator_flags2 => ['--unroll-count 4 --unroll-stmts 9999 --stats -DTEST_FULL'],
    verilator_make_gmake => 0,
    make_top_shell => 0,
    make_main => 0,
    );

file_grep($Self->{stats}, qr/Optimizations, Unrolled Loops\s+(\d+)/i, 5);

ok(1);
1;
