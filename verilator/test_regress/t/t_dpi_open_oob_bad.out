dpii_nullptr:
dpii_int_u3:
%Warning: DPI svOpenArrayHandle function index 3 out of bounds; 1 outside [4:-4].
%Warning: DPI svOpenArrayHandle function index 2 out of bounds; 20 outside [-3:3].
%Warning: DPI svOpenArrayHandle function index 1 out of bounds; 10 outside [2:-2].
%Warning: DPI svOpenArrayHandle function called on 3 dimensional array using 1 dimensional function.
dpii_real_u1:
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
%Warning: DPI svOpenArrayHandle function unsupported datatype (5).
dpii_bit_u6:
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
dpii_real_u6:
%Warning: DPI svOpenArrayHandle function called on 6 dimensional array using -1 dimensional function.
*-* All Finished *-*
