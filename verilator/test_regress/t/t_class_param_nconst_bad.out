%Error: t/t_class_param_nconst_bad.v:12:17: Expecting expression to be constant, but can't convert a RAND to constant.
                                          : ... note: In instance 't'
   12 |    Cls #(.PARAM($random)) c;   
      |                 ^~~~~~~
%Error: t/t_class_param_nconst_bad.v:12:11: Can't convert defparam value to constant: Param 'PARAM' of 'Cls'
                                          : ... note: In instance 't'
   12 |    Cls #(.PARAM($random)) c;   
      |           ^~~~~
%Error: Exiting due to
