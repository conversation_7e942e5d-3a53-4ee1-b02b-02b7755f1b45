#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
use IO::File;
#use Data::Dumper;
use strict;
use vars qw($Self);

scenarios(simulator => 1);

# Greek Hi
my $hi = "Greek: " . c(0xce) . c(0xb3) . c(0xce) . c(0xb5) . c(0xce) . c(0xb9) . c(0xce) . c(0xb1);
sub c {
    # Appease https://www.virustotal.com NANO-Antivirus gives Trojan.Script.Vbs-heuristic flag
    my $code = shift;
    return eval("c" . "h" . "r(" . $code . ")");
}

sub gen {
    my $filename = shift;

    my $fh = IO::File->new(">$filename");
    $fh->print(c(0xEF));
    $fh->print(c(0xBB));
    $fh->print(c(0xBF));  # BOM
    $fh->print("// Bom\n");
    $fh->print("// Generated by t_unicode.pl\n");
    $fh->print("module t;\n");
    $fh->print("   // " . $hi . "\n");
    $fh->print("   initial begin\n");
    $fh->print("      \$write(\"" . $hi . "\\n\");\n");
    $fh->print("      \$write(\"*-* All Finished *-*\\n\");\n");
    $fh->print("      \$finish;\n");
    $fh->print("   end\n");
    $fh->print("endmodule\n");
}

top_filename("$Self->{obj_dir}/t_unicode.v");

gen($Self->{top_filename});

compile(
    );

execute(
    check_finished => 1,
    expect => $hi,
    );

ok(1);
1;
