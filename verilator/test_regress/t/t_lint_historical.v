// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2022 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t;
   // Test all warnings, including those that are historically removed still parse
   // verilator lint_off ALWCOMBORDER
   // verilator lint_off ASCRANGE
   // verilator lint_off ASSIGNDLY
   // verilator lint_off ASSIGNIN
   // verilator lint_off BADSTDPRAGMA
   // verilator lint_off BLKANDNBLK
   // verilator lint_off BLKLOOPINIT
   // verilator lint_off BLKSEQ
   // verilator lint_off BSSPACE
   // verilator lint_off CASEINCOMPLETE
   // verilator lint_off CASEOVERLAP
   // verilator lint_off CASEWITHX
   // verilator lint_off CASEX
   // verilator lint_off CASTCONST
   // verilator lint_off CDCRSTLOGIC
   // verilator lint_off CLKDATA
   // verilator lint_off CMPCONST
   // verilator lint_off COLONPLUS
   // verilator lint_off COMBDLY
   // verilator lint_off CONSTRAINTIGN
   // verilator lint_off CONTASSREG
   // verilator lint_off DECLFILENAME
   // verilator lint_off DEFPARAM
   // verilator lint_off DEPRECATED
   // verilator lint_off ENCAPSULATED
   // verilator lint_off ENDLABEL
   // verilator lint_off ENUMVALUE
   // verilator lint_off EOFNEWLINE
   // verilator lint_off GENCLK
   // verilator lint_off HIERBLOCK
   // verilator lint_off IFDEPTH
   // verilator lint_off IGNOREDRETURN
   // verilator lint_off IMPERFECTSCH
   // verilator lint_off IMPLICIT
   // verilator lint_off IMPLICITSTATIC
   // verilator lint_off IMPORTSTAR
   // verilator lint_off IMPURE
   // verilator lint_off INCABSPATH
   // verilator lint_off INFINITELOOP
   // verilator lint_off INITIALDLY
   // verilator lint_off INSECURE
   // verilator lint_off LATCH
   // verilator lint_off LITENDIAN
   // verilator lint_off MINTYPMAXDLY
   // verilator lint_off MODDUP
   // verilator lint_off MULTIDRIVEN
   // verilator lint_off MULTITOP
   // verilator lint_off NEWERSTD
   // verilator lint_off NOLATCH
   // verilator lint_off NULLPORT
   // verilator lint_off PINCONNECTEMPTY
   // verilator lint_off PINMISSING
   // verilator lint_off PINNOCONNECT
   // verilator lint_off PINNOTFOUND
   // verilator lint_off PKGNODECL
   // verilator lint_off PROCASSWIRE
   // verilator lint_off PROFOUTOFDATE
   // verilator lint_off PROTECTED
   // verilator lint_off RANDC
   // verilator lint_off REALCVT
   // verilator lint_off REDEFMACRO
   // verilator lint_off RISEFALLDLY
   // verilator lint_off SELRANGE
   // verilator lint_off SHORTREAL
   // verilator lint_off SPLITVAR
   // verilator lint_off STATICVAR
   // verilator lint_off STMTDLY
   // verilator lint_off SYMRSVDWORD
   // verilator lint_off SYNCASYNCNET
   // verilator lint_off TICKCOUNT
   // verilator lint_off TIMESCALEMOD
   // verilator lint_off UNDRIVEN
   // verilator lint_off UNOPT
   // verilator lint_off UNOPTFLAT
   // verilator lint_off UNOPTTHREADS
   // verilator lint_off UNPACKED
   // verilator lint_off UNSIGNED
   // verilator lint_off UNUSEDGENVAR
   // verilator lint_off UNUSEDPARAM
   // verilator lint_off UNUSEDSIGNAL
   // verilator lint_off USERERROR
   // verilator lint_off USERFATAL
   // verilator lint_off USERINFO
   // verilator lint_off USERWARN
   // verilator lint_off VARHIDDEN
   // verilator lint_off WAITCONST
   // verilator lint_off WIDTH
   // verilator lint_off WIDTHCONCAT
   // verilator lint_off WIDTHEXPAND
   // verilator lint_off WIDTHTRUNC
   // verilator lint_off WIDTHXZEXPAND
   // verilator lint_off ZERODLY

endmodule
