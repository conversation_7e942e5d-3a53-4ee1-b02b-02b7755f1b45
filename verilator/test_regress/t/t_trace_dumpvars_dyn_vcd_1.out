$version Generated by VerilatedVcd $end
$date Sat Mar  5 13:47:52 2022 $end
$timescale 1ps $end

 $scope module top $end
  $scope module t $end
   $var wire 32 # cyc [31:0] $end
   $scope module sub1a $end
    $var wire 32 - ADD [31:0] $end
    $var wire 32 # cyc [31:0] $end
    $var wire 32 $ value [31:0] $end
   $upscope $end
   $scope module sub1b $end
    $var wire 32 1 ADD [31:0] $end
    $var wire 32 # cyc [31:0] $end
    $var wire 32 ( value [31:0] $end
    $scope module sub2a $end
     $var wire 32 2 ADD [31:0] $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 ) value [31:0] $end
    $upscope $end
    $scope module sub2b $end
     $var wire 32 3 ADD [31:0] $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 * value [31:0] $end
    $upscope $end
    $scope module sub2c $end
     $var wire 32 4 ADD [31:0] $end
     $var wire 32 # cyc [31:0] $end
     $var wire 32 + value [31:0] $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000000 #
b00000000000000000000000000001010 $
b00000000000000000000000000010100 (
b00000000000000000000000000010101 )
b00000000000000000000000000010110 *
b00000000000000000000000000010111 +
b00000000000000000000000000001010 -
b00000000000000000000000000010100 1
b00000000000000000000000000010101 2
b00000000000000000000000000010110 3
b00000000000000000000000000010111 4
#1
b00000000000000000000000000000001 #
b00000000000000000000000000001011 $
b00000000000000000000000000010101 (
b00000000000000000000000000010110 )
b00000000000000000000000000010111 *
b00000000000000000000000000011000 +
#2
#3
b00000000000000000000000000000010 #
b00000000000000000000000000001100 $
b00000000000000000000000000010110 (
b00000000000000000000000000010111 )
b00000000000000000000000000011000 *
b00000000000000000000000000011001 +
#4
#5
b00000000000000000000000000000011 #
b00000000000000000000000000001101 $
b00000000000000000000000000010111 (
b00000000000000000000000000011000 )
b00000000000000000000000000011001 *
b00000000000000000000000000011010 +
#6
#7
b00000000000000000000000000000100 #
b00000000000000000000000000001110 $
b00000000000000000000000000011000 (
b00000000000000000000000000011001 )
b00000000000000000000000000011010 *
b00000000000000000000000000011011 +
#8
#9
b00000000000000000000000000000101 #
b00000000000000000000000000001111 $
b00000000000000000000000000011001 (
b00000000000000000000000000011010 )
b00000000000000000000000000011011 *
b00000000000000000000000000011100 +
#10
#11
b00000000000000000000000000000110 #
b00000000000000000000000000010000 $
b00000000000000000000000000011010 (
b00000000000000000000000000011011 )
b00000000000000000000000000011100 *
b00000000000000000000000000011101 +
#12
#13
b00000000000000000000000000000111 #
b00000000000000000000000000010001 $
b00000000000000000000000000011011 (
b00000000000000000000000000011100 )
b00000000000000000000000000011101 *
b00000000000000000000000000011110 +
#14
#15
b00000000000000000000000000001000 #
b00000000000000000000000000010010 $
b00000000000000000000000000011100 (
b00000000000000000000000000011101 )
b00000000000000000000000000011110 *
b00000000000000000000000000011111 +
#16
#17
b00000000000000000000000000001001 #
b00000000000000000000000000010011 $
b00000000000000000000000000011101 (
b00000000000000000000000000011110 )
b00000000000000000000000000011111 *
b00000000000000000000000000100000 +
#18
#19
b00000000000000000000000000001010 #
b00000000000000000000000000010100 $
b00000000000000000000000000011110 (
b00000000000000000000000000011111 )
b00000000000000000000000000100000 *
b00000000000000000000000000100001 +
#20
