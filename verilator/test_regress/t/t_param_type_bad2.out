%Error: t/t_param_type_bad2.v:8:19: Operator VAR 't' expected non-datatype Initial value but 'logic' is a datatype.
                                  : ... note: In instance 't'
    8 |    localparam t = logic;   
      |                   ^~~~~
%Error: t/t_param_type_bad2.v:9:20: Operator VAR 't2' expected non-datatype Initial value but 'real' is a datatype.
                                  : ... note: In instance 't'
    9 |    localparam t2 = realtime;   
      |                    ^~~~~~~~
%Error: Exiting due to
