#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
use IO::File;

scenarios(simulator => 1);

sub gen {
    my $filename = shift;

    my $fh = IO::File->new(">$filename");
    $fh->print("// Generated by t_gate_chained.pl\n");
    $fh->print("module t (clk,i,sel,o);\n");
    $fh->print("  input clk;\n");
    $fh->print("  input [63:0] i;\n");
    $fh->print("  input [15:0] sel;\n");
    $fh->print("  output [63:0] o;\n");
    $fh->print("\n");
    my $prev = "i";
    my $n = 9000;
    for (my $i = 1; $i < $n; ++$i) {
        $fh->printf("  wire [63:0] ass%04x = (sel == 16'h%04x) ? 64'h0 : $prev;\n", $i, $i);
        $prev = sprintf("ass%04x", $i);
    }

    $fh->print("\n");
    $fh->print("  wire [63:0] o = $prev;\n");

    $fh->print("\n");
    $fh->print("  always @ (posedge clk) begin\n");
    $fh->print('    $write("*-* All Finished *-*\n");', "\n");
    $fh->print('    $finish;', "\n");
    $fh->print("  end\n");
    $fh->print("endmodule\n");
}

top_filename("$Self->{obj_dir}/t_gate_chained.v");

gen($Self->{top_filename});

compile(
    verilator_flags2 => ["--stats --x-assign fast --x-initial fast",
                         "-Wno-UNOPTTHREADS -fno-dfg"],
    );

execute(
    check_finished => 1,
    );

# Must be <<9000 above to prove this worked
file_grep($Self->{stats}, qr/Optimizations, Gate sigs deleted\s+(\d+)/i, 8575);

ok(1);
1;
