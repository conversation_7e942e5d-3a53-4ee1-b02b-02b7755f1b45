$version Generated by VerilatedVcd $end
$timescale 1ps $end

 $scope module top $end
  $var wire 1 E" clk $end
  $scope module t $end
   $var wire 1 E" clk $end
   $var wire 32 F" DEPTH [31:0] $end
   $var wire 32 G" WIDTH [31:0] $end
   $var wire 32 H" NUMSUB [31:0] $end
   $var wire 8 I" in [7:0] $end
   $var wire 8 - out[0] [7:0] $end
   $var wire 8 . out[1] [7:0] $end
   $var wire 8 / out[2] [7:0] $end
   $var wire 8 0 out[3] [7:0] $end
   $var wire 8 1 out[4] [7:0] $end
   $var wire 8 2 out[5] [7:0] $end
   $var wire 8 3 out[6] [7:0] $end
   $var wire 8 4 out[7] [7:0] $end
   $var wire 8 5 out[8] [7:0] $end
   $var wire 8 N through_tmp [7:0] $end
   $var wire 3 O shift [2:0] $end
   $var wire 64 J" expc [63:0] $end
   $scope module always_block $end
    $var wire 1 P failed $end
    $scope module unnamedblk1 $end
     $var wire 32 Q i [31:0] $end
    $upscope $end
   $upscope $end
   $scope module delay0 $end
    $var wire 1 E" clk $end
    $var wire 1 L" unpack_sig0(10) $end
    $var wire 1 M" unpack_sig0(11) $end
    $var wire 1 N" unpack_sig0(12) $end
    $var wire 1 R unpack_sig0(13) $end
    $var wire 1 S unpack_sig0(14) $end
    $var wire 1 T unpack_sig0(15) $end
    $var wire 1 U unpack_sig0(16) $end
    $var wire 1 V unpack_sig1(13) $end
    $var wire 1 W unpack_sig1(14) $end
    $var wire 1 X unpack_sig1(15) $end
    $var wire 1 Y unpack_sig1(16) $end
    $var wire 1 O" unpack_sig2(10) $end
    $var wire 1 P" unpack_sig2(11) $end
    $var wire 1 Q" unpack_sig2(12) $end
    $var wire 1 Z unpack_sig2(13) $end
    $var wire 1 [ unpack_sig2(14) $end
    $var wire 1 \ unpack_sig2(15) $end
    $var wire 1 ] unpack_sig2(16) $end
    $var wire 1 ^ unpack_sig3(13) $end
    $var wire 1 _ unpack_sig3(14) $end
    $var wire 1 ` unpack_sig3(15) $end
    $var wire 1 a unpack_sig3(16) $end
    $var wire 32 b c [31:0] $end
   $upscope $end
   $scope module i_t_array_rev $end
    $var wire 1 E" clk $end
    $var wire 32 R" cyc [31:0] $end
    $var wire 1 # arrd(0) $end
    $var wire 1 $ arrd(1) $end
    $var wire 1 % y0 $end
    $var wire 1 & y1 $end
    $var wire 1 c localbkw(0) $end
    $var wire 1 d localbkw(1) $end
    $scope module arr_rev_u $end
     $var wire 1 ' arrbkw[0] $end
     $var wire 1 ( arrbkw[1] $end
     $var wire 1 % y0 $end
     $var wire 1 & y1 $end
    $upscope $end
   $upscope $end
   $scope module i_var_decl_with_init $end
    $var wire 32 ) var0 [-1:30] $end
    $var wire 32 * var2 [-1:30] $end
    $var wire 32 + var1 [30:-1] $end
    $var wire 32 , var3 [30:-1] $end
   $upscope $end
   $scope module shifter0 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 e out [7:0] $end
    $var wire 32 S" OFFSET [31:0] $end
    $var wire 8 f tmp(-1) [7:0] $end
    $var wire 8 g tmp(-2) [7:0] $end
    $var wire 8 I" tmp(-3) [7:0] $end
    $var wire 8 e tmp(0) [7:0] $end
   $upscope $end
   $scope module shifter1 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 e out [7:0] $end
    $var wire 32 S" OFFSET [31:0] $end
    $var wire 8 f tmp(-1) [7:0] $end
    $var wire 8 g tmp(-2) [7:0] $end
    $var wire 8 I" tmp(-3) [7:0] $end
    $var wire 8 e tmp(0) [7:0] $end
   $upscope $end
   $scope module shifter2 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 e out [7:0] $end
    $var wire 32 T" OFFSET [31:0] $end
    $var wire 8 I" tmp(1) [7:0] $end
    $var wire 8 g tmp(2) [7:0] $end
    $var wire 8 f tmp(3) [7:0] $end
    $var wire 8 e tmp(4) [7:0] $end
   $upscope $end
   $scope module shifter3 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 6 out [7:0] $end
    $var wire 32 T" OFFSET [31:0] $end
    $var wire 32 F" N [31:0] $end
    $var wire 8 I" tmp0(1)(1) [7:0] $end
    $var wire 8 I" tmp0(1)(2) [7:0] $end
    $var wire 8 I" tmp0(1)(3) [7:0] $end
    $var wire 8 g tmp0(2)(1) [7:0] $end
    $var wire 8 g tmp0(2)(2) [7:0] $end
    $var wire 8 g tmp0(2)(3) [7:0] $end
    $var wire 8 f tmp0(3)(1) [7:0] $end
    $var wire 8 f tmp0(3)(2) [7:0] $end
    $var wire 8 f tmp0(3)(3) [7:0] $end
    $var wire 8 e tmp0(4)(1) [7:0] $end
    $var wire 8 e tmp0(4)(2) [7:0] $end
    $var wire 8 e tmp0(4)(3) [7:0] $end
    $var wire 8 h tmp1(1)(1) [7:0] $end
    $var wire 8 i tmp1(1)(2) [7:0] $end
    $var wire 8 j tmp1(1)(3) [7:0] $end
    $var wire 8 k tmp1(2)(1) [7:0] $end
    $var wire 8 l tmp1(2)(2) [7:0] $end
    $var wire 8 m tmp1(2)(3) [7:0] $end
    $var wire 8 n tmp1(3)(1) [7:0] $end
    $var wire 8 o tmp1(3)(2) [7:0] $end
    $var wire 8 p tmp1(3)(3) [7:0] $end
    $var wire 8 q tmp1(4)(1) [7:0] $end
    $var wire 8 r tmp1(4)(2) [7:0] $end
    $var wire 8 s tmp1(4)(3) [7:0] $end
    $var wire 8 t tmp2[1][1] [7:0] $end
    $var wire 8 u tmp2[1][2] [7:0] $end
    $var wire 8 v tmp2[1][3] [7:0] $end
    $var wire 8 w tmp2[2][1] [7:0] $end
    $var wire 8 x tmp2[2][2] [7:0] $end
    $var wire 8 y tmp2[2][3] [7:0] $end
    $var wire 8 z tmp2[3][1] [7:0] $end
    $var wire 8 { tmp2[3][2] [7:0] $end
    $var wire 8 | tmp2[3][3] [7:0] $end
    $var wire 8 } tmp2[4][1] [7:0] $end
    $var wire 8 ~ tmp2[4][2] [7:0] $end
    $var wire 8 !! tmp2[4][3] [7:0] $end
    $var wire 8 "! tmp3(1)(1) [7:0] $end
    $var wire 8 #! tmp3(1)(2) [7:0] $end
    $var wire 8 $! tmp3(1)(3) [7:0] $end
    $var wire 8 %! tmp3(2)(1) [7:0] $end
    $var wire 8 &! tmp3(2)(2) [7:0] $end
    $var wire 8 '! tmp3(2)(3) [7:0] $end
    $var wire 8 (! tmp3(3)(1) [7:0] $end
    $var wire 8 )! tmp3(3)(2) [7:0] $end
    $var wire 8 *! tmp3(3)(3) [7:0] $end
    $var wire 8 +! tmp3(4)(1) [7:0] $end
    $var wire 8 ,! tmp3(4)(2) [7:0] $end
    $var wire 8 -! tmp3(4)(3) [7:0] $end
    $var wire 8 .! tmp4(1)(1) [7:0] $end
    $var wire 8 /! tmp4(1)(2) [7:0] $end
    $var wire 8 0! tmp4(1)(3) [7:0] $end
    $var wire 8 1! tmp4(2)(1) [7:0] $end
    $var wire 8 2! tmp4(2)(2) [7:0] $end
    $var wire 8 3! tmp4(2)(3) [7:0] $end
    $var wire 8 4! tmp4(3)(1) [7:0] $end
    $var wire 8 5! tmp4(3)(2) [7:0] $end
    $var wire 8 6! tmp4(3)(3) [7:0] $end
    $var wire 8 7! tmp4(4)(1) [7:0] $end
    $var wire 8 8! tmp4(4)(2) [7:0] $end
    $var wire 8 9! tmp4(4)(3) [7:0] $end
    $var wire 8 :! tmp5[1][1] [7:0] $end
    $var wire 8 ;! tmp5[1][2] [7:0] $end
    $var wire 8 <! tmp5[1][3] [7:0] $end
    $var wire 8 =! tmp5[2][1] [7:0] $end
    $var wire 8 >! tmp5[2][2] [7:0] $end
    $var wire 8 ?! tmp5[2][3] [7:0] $end
    $var wire 8 @! tmp5[3][1] [7:0] $end
    $var wire 8 A! tmp5[3][2] [7:0] $end
    $var wire 8 B! tmp5[3][3] [7:0] $end
    $var wire 8 C! tmp5[4][1] [7:0] $end
    $var wire 8 D! tmp5[4][2] [7:0] $end
    $var wire 8 E! tmp5[4][3] [7:0] $end
    $var wire 8 F! tmp6(1)(1) [7:0] $end
    $var wire 8 G! tmp6(1)(2) [7:0] $end
    $var wire 8 H! tmp6(1)(3) [7:0] $end
    $var wire 8 I! tmp6(2)(1) [7:0] $end
    $var wire 8 J! tmp6(2)(2) [7:0] $end
    $var wire 8 K! tmp6(2)(3) [7:0] $end
    $var wire 8 L! tmp6(3)(1) [7:0] $end
    $var wire 8 M! tmp6(3)(2) [7:0] $end
    $var wire 8 N! tmp6(3)(3) [7:0] $end
    $var wire 8 O! tmp6(4)(1) [7:0] $end
    $var wire 8 P! tmp6(4)(2) [7:0] $end
    $var wire 8 Q! tmp6(4)(3) [7:0] $end
    $var wire 8 R! tmp7(2)(1) [7:0] $end
    $var wire 8 S! tmp7(2)(2) [7:0] $end
    $var wire 8 T! tmp7(2)(3) [7:0] $end
    $var wire 8 U! tmp7(3)(1) [7:0] $end
    $var wire 8 V! tmp7(3)(2) [7:0] $end
    $var wire 8 W! tmp7(3)(3) [7:0] $end
    $var wire 8 X! tmp7(4)(1) [7:0] $end
    $var wire 8 Y! tmp7(4)(2) [7:0] $end
    $var wire 8 Z! tmp7(4)(3) [7:0] $end
    $var wire 8 [! tmp7(5)(1) [7:0] $end
    $var wire 8 \! tmp7(5)(2) [7:0] $end
    $var wire 8 ]! tmp7(5)(3) [7:0] $end
    $var wire 8 U" tmp8(0)(1) [7:0] $end
    $var wire 8 V" tmp8(0)(2) [7:0] $end
    $var wire 8 W" tmp8(0)(3) [7:0] $end
    $var wire 8 X" tmp8(1)(1) [7:0] $end
    $var wire 8 Y" tmp8(1)(2) [7:0] $end
    $var wire 8 Z" tmp8(1)(3) [7:0] $end
    $var wire 8 ^! tmp8(2)(1) [7:0] $end
    $var wire 8 _! tmp8(2)(2) [7:0] $end
    $var wire 8 `! tmp8(2)(3) [7:0] $end
    $var wire 8 a! tmp8(3)(1) [7:0] $end
    $var wire 8 b! tmp8(3)(2) [7:0] $end
    $var wire 8 c! tmp8(3)(3) [7:0] $end
    $var wire 8 d! tmp8(4)(1) [7:0] $end
    $var wire 8 e! tmp8(4)(2) [7:0] $end
    $var wire 8 f! tmp8(4)(3) [7:0] $end
    $var wire 8 g! tmp8(5)(1) [7:0] $end
    $var wire 8 h! tmp8(5)(2) [7:0] $end
    $var wire 8 i! tmp8(5)(3) [7:0] $end
    $var wire 8 [" tmp8(6)(1) [7:0] $end
    $var wire 8 \" tmp8(6)(2) [7:0] $end
    $var wire 8 ]" tmp8(6)(3) [7:0] $end
    $var wire 8 ^" tmp8(7)(1) [7:0] $end
    $var wire 8 _" tmp8(7)(2) [7:0] $end
    $var wire 8 `" tmp8(7)(3) [7:0] $end
    $var wire 8 j! tmp9(4)(1) [7:0] $end
    $var wire 8 k! tmp9(4)(2) [7:0] $end
    $var wire 8 l! tmp9(4)(3) [7:0] $end
    $var wire 8 m! tmp9(5)(1) [7:0] $end
    $var wire 8 n! tmp9(5)(2) [7:0] $end
    $var wire 8 o! tmp9(5)(3) [7:0] $end
    $var wire 8 p! tmp9(6)(1) [7:0] $end
    $var wire 8 q! tmp9(6)(2) [7:0] $end
    $var wire 8 r! tmp9(6)(3) [7:0] $end
    $var wire 8 s! tmp9(7)(1) [7:0] $end
    $var wire 8 t! tmp9(7)(2) [7:0] $end
    $var wire 8 u! tmp9(7)(3) [7:0] $end
    $var wire 8 v! tmp10(1)(1) [7:0] $end
    $var wire 8 w! tmp10(1)(2) [7:0] $end
    $var wire 8 x! tmp10(1)(3) [7:0] $end
    $var wire 8 y! tmp10(2)(1) [7:0] $end
    $var wire 8 z! tmp10(2)(2) [7:0] $end
    $var wire 8 {! tmp10(2)(3) [7:0] $end
    $var wire 8 |! tmp10(3)(1) [7:0] $end
    $var wire 8 }! tmp10(3)(2) [7:0] $end
    $var wire 8 ~! tmp10(3)(3) [7:0] $end
    $var wire 8 !" tmp10(4)(1) [7:0] $end
    $var wire 8 "" tmp10(4)(2) [7:0] $end
    $var wire 8 #" tmp10(4)(3) [7:0] $end
    $var wire 8 7 tmp12(-1)(1)(1) [7:0] $end
    $var wire 8 8 tmp12(-1)(1)(2) [7:0] $end
    $var wire 8 9 tmp12(-1)(1)(3) [7:0] $end
    $var wire 8 : tmp12(-1)(2)(1) [7:0] $end
    $var wire 8 ; tmp12(-1)(2)(2) [7:0] $end
    $var wire 8 < tmp12(-1)(2)(3) [7:0] $end
    $var wire 8 = tmp12(-1)(3)(1) [7:0] $end
    $var wire 8 > tmp12(-1)(3)(2) [7:0] $end
    $var wire 8 ? tmp12(-1)(3)(3) [7:0] $end
    $var wire 8 6 tmp12(-1)(4)(1) [7:0] $end
    $var wire 8 @ tmp12(-1)(4)(2) [7:0] $end
    $var wire 8 A tmp12(-1)(4)(3) [7:0] $end
    $var wire 8 B tmp12(0)(1)(1) [7:0] $end
    $var wire 8 C tmp12(0)(1)(2) [7:0] $end
    $var wire 8 D tmp12(0)(1)(3) [7:0] $end
    $var wire 8 E tmp12(0)(2)(1) [7:0] $end
    $var wire 8 F tmp12(0)(2)(2) [7:0] $end
    $var wire 8 G tmp12(0)(2)(3) [7:0] $end
    $var wire 8 H tmp12(0)(3)(1) [7:0] $end
    $var wire 8 I tmp12(0)(3)(2) [7:0] $end
    $var wire 8 J tmp12(0)(3)(3) [7:0] $end
    $var wire 8 K tmp12(0)(4)(1) [7:0] $end
    $var wire 8 L tmp12(0)(4)(2) [7:0] $end
    $var wire 8 M tmp12(0)(4)(3) [7:0] $end
    $var wire 8 a" tmp13(1)(1) [7:0] $end
    $var wire 8 b" tmp13(1)(2) [7:0] $end
    $var wire 8 c" tmp13(1)(3) [7:0] $end
    $var wire 8 d" tmp13(2)(1) [7:0] $end
    $var wire 8 e" tmp13(2)(2) [7:0] $end
    $var wire 8 f" tmp13(2)(3) [7:0] $end
    $var wire 8 g" tmp13(3)(1) [7:0] $end
    $var wire 8 h" tmp13(3)(2) [7:0] $end
    $var wire 8 i" tmp13(3)(3) [7:0] $end
    $var wire 8 j" tmp13(4)(1) [7:0] $end
    $var wire 8 k" tmp13(4)(2) [7:0] $end
    $var wire 8 l" tmp13(4)(3) [7:0] $end
   $upscope $end
   $scope module shifter4 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 $" out [7:0] $end
    $var wire 32 m" OFFSET [31:0] $end
    $var wire 32 n" tmp(2) [31:0] $end
    $var wire 32 %" tmp(3) [31:0] $end
    $var wire 32 &" tmp(4) [31:0] $end
    $var wire 32 '" tmp(5) [31:0] $end
    $var wire 24 o" pad [23:0] $end
   $upscope $end
   $scope module shifter5 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 (" out [7:0] $end
    $var wire 32 p" OFFSET [31:0] $end
    $var wire 32 )" tmp [31:0] $end
   $upscope $end
   $scope module shifter6 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 *" out [7:0] $end
    $var wire 32 p" OFFSET [31:0] $end
    $var wire 32 +" tmp [31:0] $end
   $upscope $end
   $scope module shifter7 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 e out [7:0] $end
    $var wire 32 ," tmp [31:0] $end
   $upscope $end
   $scope module shifter8 $end
    $var wire 32 F" DEPTH [31:0] $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 I" in [7:0] $end
    $var wire 3 O shift [2:0] $end
    $var wire 8 e out [7:0] $end
    $var wire 32 ," tmp [0:31] $end
   $upscope $end
   $scope module though0 $end
    $var wire 32 G" WIDTH [31:0] $end
    $var wire 8 e in [7:0] $end
    $var wire 8 N out [7:0] $end
    $var wire 1 -" unpack_tmp(0) $end
    $var wire 1 ." unpack_tmp(1) $end
    $var wire 1 /" unpack_tmp(2) $end
    $var wire 1 0" unpack_tmp(3) $end
    $var wire 1 1" unpack_tmp(4) $end
    $var wire 1 2" unpack_tmp(5) $end
    $var wire 1 3" unpack_tmp(6) $end
    $var wire 1 4" unpack_tmp(7) $end
    $scope module i_pack2unpack $end
     $var wire 32 G" WIDTH [31:0] $end
     $var wire 8 e in [7:0] $end
     $var wire 1 5" out[0] $end
     $var wire 1 6" out[1] $end
     $var wire 1 7" out[2] $end
     $var wire 1 8" out[3] $end
     $var wire 1 9" out[4] $end
     $var wire 1 :" out[5] $end
     $var wire 1 ;" out[6] $end
     $var wire 1 <" out[7] $end
    $upscope $end
    $scope module i_unpack2pack $end
     $var wire 32 G" WIDTH [31:0] $end
     $var wire 1 =" in[0] $end
     $var wire 1 >" in[1] $end
     $var wire 1 ?" in[2] $end
     $var wire 1 @" in[3] $end
     $var wire 1 A" in[4] $end
     $var wire 1 B" in[5] $end
     $var wire 1 C" in[6] $end
     $var wire 1 D" in[7] $end
     $var wire 8 N out [7:0] $end
    $upscope $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
0$
0%
1&
0'
1(
b00000001001000110100010101100111 )
b00100000000000000000000000000000 *
b00000001001000110100000000100111 +
b00000000000000000000000000000011 ,
b10001110 -
b10001110 .
b10001110 /
b10001110 0
b10001110 1
b10001110 2
b10001110 3
b10001110 4
b10001110 5
b10001110 6
b10001110 7
b10001110 8
b10001110 9
b10001110 :
b10001110 ;
b10001110 <
b10001110 =
b10001110 >
b10001110 ?
b10001110 @
b10001110 A
b10001110 B
b10001110 C
b10001110 D
b10001110 E
b10001110 F
b10001110 G
b10001110 H
b10001110 I
b10001110 J
b10001110 K
b10001110 L
b10001110 M
b10001110 N
b000 O
0P
b00000000000000000000000000000000 Q
0R
0S
0T
0U
0V
0W
0X
0Y
0Z
0[
0\
0]
0^
0_
0`
0a
b00000000000000000000000000000000 b
0c
0d
b10001110 e
b10001110 f
b10001110 g
b10001110 h
b10001110 i
b10001110 j
b10001110 k
b10001110 l
b10001110 m
b10001110 n
b10001110 o
b10001110 p
b10001110 q
b10001110 r
b10001110 s
b10001110 t
b10001110 u
b10001110 v
b10001110 w
b10001110 x
b10001110 y
b10001110 z
b10001110 {
b10001110 |
b10001110 }
b10001110 ~
b10001110 !!
b10001110 "!
b10001110 #!
b10001110 $!
b10001110 %!
b10001110 &!
b10001110 '!
b10001110 (!
b10001110 )!
b10001110 *!
b10001110 +!
b10001110 ,!
b10001110 -!
b10001110 .!
b10001110 /!
b10001110 0!
b10001110 1!
b10001110 2!
b10001110 3!
b10001110 4!
b10001110 5!
b10001110 6!
b10001110 7!
b10001110 8!
b10001110 9!
b10001110 :!
b10001110 ;!
b10001110 <!
b10001110 =!
b10001110 >!
b10001110 ?!
b10001110 @!
b10001110 A!
b10001110 B!
b10001110 C!
b10001110 D!
b10001110 E!
b10001110 F!
b10001110 G!
b10001110 H!
b10001110 I!
b10001110 J!
b10001110 K!
b10001110 L!
b10001110 M!
b10001110 N!
b10001110 O!
b10001110 P!
b10001110 Q!
b10001110 R!
b10001110 S!
b10001110 T!
b10001110 U!
b10001110 V!
b10001110 W!
b10001110 X!
b10001110 Y!
b10001110 Z!
b10001110 [!
b10001110 \!
b10001110 ]!
b10001110 ^!
b10001110 _!
b10001110 `!
b10001110 a!
b10001110 b!
b10001110 c!
b10001110 d!
b10001110 e!
b10001110 f!
b10001110 g!
b10001110 h!
b10001110 i!
b10001110 j!
b10001110 k!
b10001110 l!
b10001110 m!
b10001110 n!
b10001110 o!
b10001110 p!
b10001110 q!
b10001110 r!
b10001110 s!
b10001110 t!
b10001110 u!
b10001110 v!
b10001110 w!
b10001110 x!
b10001110 y!
b10001110 z!
b10001110 {!
b10001110 |!
b10001110 }!
b10001110 ~!
b10001110 !"
b10001110 ""
b10001110 #"
b10001110 $"
b00000000000000000000000010001110 %"
b00000000000000000000000010001110 &"
b00000000000000000000000010001110 '"
b10001110 ("
b10001110100011101000111010001110 )"
b10001110 *"
b10001110100011101000111010001110 +"
b10001110100011101000111010001110 ,"
1-"
0."
0/"
00"
11"
12"
13"
04"
05"
16"
17"
18"
09"
0:"
0;"
1<"
0="
1>"
1?"
1@"
0A"
0B"
0C"
1D"
0E"
b00000000000000000000000000000011 F"
b00000000000000000000000000001000 G"
b00000000000000000000000000001001 H"
b10001110 I"
b1000111001000111101000111101000111101000011101000011101000011101 J"
0L"
0M"
0N"
0O"
0P"
0Q"
b00000000000000000000000000000000 R"
b11111111111111111111111111111101 S"
b00000000000000000000000000000001 T"
b00000000 U"
b00000000 V"
b00000000 W"
b00000000 X"
b00000000 Y"
b00000000 Z"
b00000000 ["
b00000000 \"
b00000000 ]"
b00000000 ^"
b00000000 _"
b00000000 `"
b00000000 a"
b00000000 b"
b00000000 c"
b00000000 d"
b00000000 e"
b00000000 f"
b00000000 g"
b00000000 h"
b00000000 i"
b00000000 j"
b00000000 k"
b00000000 l"
b00000000000000000000000000000010 m"
b00000000000000000000000010001110 n"
b000000000000000000000000 o"
b11111111111111111111111111111110 p"
#10
b01000111 -
b01000111 .
b01000111 /
b01000111 0
b01000111 1
b01000111 2
b01000111 3
b01000111 4
b01000111 5
b01000111 6
b01000111 :
b01000111 ;
b01000111 <
b01000111 =
b01000111 >
b01000111 ?
b01000111 @
b01000111 A
b01000111 E
b01000111 F
b01000111 G
b01000111 H
b01000111 I
b01000111 J
b01000111 K
b01000111 L
b01000111 M
b01000111 N
b001 O
b00000000000000000000000000001001 Q
1R
1V
1Z
1^
b00000000000000000000000000000001 b
1d
b01000111 e
b01000111 f
b01000111 g
b01000111 k
b01000111 l
b01000111 m
b01000111 n
b01000111 o
b01000111 p
b01000111 q
b01000111 r
b01000111 s
b01000111 w
b01000111 x
b01000111 y
b01000111 z
b01000111 {
b01000111 |
b01000111 }
b01000111 ~
b01000111 !!
b01000111 %!
b01000111 &!
b01000111 '!
b01000111 (!
b01000111 )!
b01000111 *!
b01000111 +!
b01000111 ,!
b01000111 -!
b01000111 1!
b01000111 2!
b01000111 3!
b01000111 4!
b01000111 5!
b01000111 6!
b01000111 7!
b01000111 8!
b01000111 9!
b01000111 =!
b01000111 >!
b01000111 ?!
b01000111 @!
b01000111 A!
b01000111 B!
b01000111 C!
b01000111 D!
b01000111 E!
b01000111 I!
b01000111 J!
b01000111 K!
b01000111 L!
b01000111 M!
b01000111 N!
b01000111 O!
b01000111 P!
b01000111 Q!
b01000111 U!
b01000111 V!
b01000111 W!
b01000111 X!
b01000111 Y!
b01000111 Z!
b01000111 [!
b01000111 \!
b01000111 ]!
b01000111 a!
b01000111 b!
b01000111 c!
b01000111 d!
b01000111 e!
b01000111 f!
b01000111 g!
b01000111 h!
b01000111 i!
b01000111 m!
b01000111 n!
b01000111 o!
b01000111 p!
b01000111 q!
b01000111 r!
b01000111 s!
b01000111 t!
b01000111 u!
b01000111 y!
b01000111 z!
b01000111 {!
b01000111 |!
b01000111 }!
b01000111 ~!
b01000111 !"
b01000111 ""
b01000111 #"
b01000111 $"
b00000000000000000000000001000111 %"
b00000000000000000000000001000111 &"
b00000000000000000000000001000111 '"
b01000111 ("
b10001110010001110100011101000111 )"
b01000111 *"
b10001110010001110100011101000111 +"
b10001110010001110100011101000111 ,"
0-"
1."
01"
14"
15"
08"
1;"
0<"
1="
0@"
1C"
0D"
1E"
#15
0E"
#20
b10100011 -
b10100011 .
b10100011 /
b10100011 0
b10100011 1
b10100011 2
b10100011 3
b10100011 4
b10100011 5
b10100011 6
b10001110 :
b10001110 ;
b10001110 <
b10100011 =
b10100011 >
b10100011 ?
b10100011 @
b10100011 A
b10001110 E
b10001110 F
b10001110 G
b10100011 H
b10100011 I
b10100011 J
b10100011 K
b10100011 L
b10100011 M
b10100011 N
b010 O
1S
1W
1[
1_
b00000000000000000000000000000010 b
b10100011 e
b10100011 f
b10001110 g
b10001110 k
b10001110 l
b10001110 m
b10100011 n
b10100011 o
b10100011 p
b10100011 q
b10100011 r
b10100011 s
b10001110 w
b10001110 x
b10001110 y
b10100011 z
b10100011 {
b10100011 |
b10100011 }
b10100011 ~
b10100011 !!
b10001110 %!
b10001110 &!
b10001110 '!
b10100011 (!
b10100011 )!
b10100011 *!
b10100011 +!
b10100011 ,!
b10100011 -!
b10001110 1!
b10001110 2!
b10001110 3!
b10100011 4!
b10100011 5!
b10100011 6!
b10100011 7!
b10100011 8!
b10100011 9!
b10001110 =!
b10001110 >!
b10001110 ?!
b10100011 @!
b10100011 A!
b10100011 B!
b10100011 C!
b10100011 D!
b10100011 E!
b10001110 I!
b10001110 J!
b10001110 K!
b10100011 L!
b10100011 M!
b10100011 N!
b10100011 O!
b10100011 P!
b10100011 Q!
b10001110 U!
b10001110 V!
b10001110 W!
b10100011 X!
b10100011 Y!
b10100011 Z!
b10100011 [!
b10100011 \!
b10100011 ]!
b10001110 a!
b10001110 b!
b10001110 c!
b10100011 d!
b10100011 e!
b10100011 f!
b10100011 g!
b10100011 h!
b10100011 i!
b10001110 m!
b10001110 n!
b10001110 o!
b10100011 p!
b10100011 q!
b10100011 r!
b10100011 s!
b10100011 t!
b10100011 u!
b10001110 y!
b10001110 z!
b10001110 {!
b10100011 |!
b10100011 }!
b10100011 ~!
b10100011 !"
b10100011 ""
b10100011 #"
b10100011 $"
b00000000000000000000000010001110 %"
b00000000000000000000000010100011 &"
b00000000000000000000000010100011 '"
b10100011 ("
b10001110100011101010001110100011 )"
b10100011 *"
b10001110100011101010001110100011 +"
b10001110100011101010001110100011 ,"
1-"
0."
1/"
02"
07"
1:"
0;"
1<"
0?"
1B"
0C"
1D"
1E"
#25
0E"
#30
b11010001 -
b11010001 .
b11010001 /
b11010001 0
b11010001 1
b11010001 2
b11010001 3
b11010001 4
b11010001 5
b11010001 6
b01000111 :
b01000111 ;
b01000111 <
b11010001 =
b11010001 >
b11010001 ?
b11010001 @
b11010001 A
b01000111 E
b01000111 F
b01000111 G
b11010001 H
b11010001 I
b11010001 J
b11010001 K
b11010001 L
b11010001 M
b11010001 N
b011 O
1T
1X
1\
1`
b00000000000000000000000000000011 b
b11010001 e
b11010001 f
b01000111 g
b01000111 k
b01000111 l
b01000111 m
b11010001 n
b11010001 o
b11010001 p
b11010001 q
b11010001 r
b11010001 s
b01000111 w
b01000111 x
b01000111 y
b11010001 z
b11010001 {
b11010001 |
b11010001 }
b11010001 ~
b11010001 !!
b01000111 %!
b01000111 &!
b01000111 '!
b11010001 (!
b11010001 )!
b11010001 *!
b11010001 +!
b11010001 ,!
b11010001 -!
b01000111 1!
b01000111 2!
b01000111 3!
b11010001 4!
b11010001 5!
b11010001 6!
b11010001 7!
b11010001 8!
b11010001 9!
b01000111 =!
b01000111 >!
b01000111 ?!
b11010001 @!
b11010001 A!
b11010001 B!
b11010001 C!
b11010001 D!
b11010001 E!
b01000111 I!
b01000111 J!
b01000111 K!
b11010001 L!
b11010001 M!
b11010001 N!
b11010001 O!
b11010001 P!
b11010001 Q!
b01000111 U!
b01000111 V!
b01000111 W!
b11010001 X!
b11010001 Y!
b11010001 Z!
b11010001 [!
b11010001 \!
b11010001 ]!
b01000111 a!
b01000111 b!
b01000111 c!
b11010001 d!
b11010001 e!
b11010001 f!
b11010001 g!
b11010001 h!
b11010001 i!
b01000111 m!
b01000111 n!
b01000111 o!
b11010001 p!
b11010001 q!
b11010001 r!
b11010001 s!
b11010001 t!
b11010001 u!
b01000111 y!
b01000111 z!
b01000111 {!
b11010001 |!
b11010001 }!
b11010001 ~!
b11010001 !"
b11010001 ""
b11010001 #"
b11010001 $"
b00000000000000000000000001000111 %"
b00000000000000000000000011010001 &"
b00000000000000000000000011010001 '"
b11010001 ("
b10001110010001111101000111010001 )"
b11010001 *"
b10001110010001111101000111010001 +"
b10001110010001111101000111010001 ,"
1."
0/"
10"
03"
06"
19"
0:"
1;"
0>"
1A"
0B"
1C"
1E"
#35
0E"
#40
b11101000 -
b11101000 .
b11101000 /
b11101000 0
b11101000 1
b11101000 2
b11101000 3
b11101000 4
b11101000 5
b11101000 6
b10001110 :
b10001110 ;
b10001110 <
b10001110 =
b10001110 >
b10001110 ?
b11101000 @
b11101000 A
b10001110 E
b10001110 F
b10001110 G
b10001110 H
b10001110 I
b10001110 J
b11101000 K
b11101000 L
b11101000 M
b11101000 N
b100 O
1U
1Y
1]
1a
b00000000000000000000000000000100 b
b11101000 e
b10001110 f
b10001110 g
b10001110 k
b10001110 l
b10001110 m
b10001110 n
b10001110 o
b10001110 p
b11101000 q
b11101000 r
b11101000 s
b10001110 w
b10001110 x
b10001110 y
b10001110 z
b10001110 {
b10001110 |
b11101000 }
b11101000 ~
b11101000 !!
b10001110 %!
b10001110 &!
b10001110 '!
b10001110 (!
b10001110 )!
b10001110 *!
b11101000 +!
b11101000 ,!
b11101000 -!
b10001110 1!
b10001110 2!
b10001110 3!
b10001110 4!
b10001110 5!
b10001110 6!
b11101000 7!
b11101000 8!
b11101000 9!
b10001110 =!
b10001110 >!
b10001110 ?!
b10001110 @!
b10001110 A!
b10001110 B!
b11101000 C!
b11101000 D!
b11101000 E!
b10001110 I!
b10001110 J!
b10001110 K!
b10001110 L!
b10001110 M!
b10001110 N!
b11101000 O!
b11101000 P!
b11101000 Q!
b10001110 U!
b10001110 V!
b10001110 W!
b10001110 X!
b10001110 Y!
b10001110 Z!
b11101000 [!
b11101000 \!
b11101000 ]!
b10001110 a!
b10001110 b!
b10001110 c!
b10001110 d!
b10001110 e!
b10001110 f!
b11101000 g!
b11101000 h!
b11101000 i!
b10001110 m!
b10001110 n!
b10001110 o!
b10001110 p!
b10001110 q!
b10001110 r!
b11101000 s!
b11101000 t!
b11101000 u!
b10001110 y!
b10001110 z!
b10001110 {!
b10001110 |!
b10001110 }!
b10001110 ~!
b11101000 !"
b11101000 ""
b11101000 #"
b11101000 $"
b00000000000000000000000010001110 %"
b00000000000000000000000010001110 &"
b00000000000000000000000011101000 '"
b11101000 ("
b10001110100011101000111011101000 )"
b11101000 *"
b10001110100011101000111011101000 +"
b10001110100011101000111011101000 ,"
1/"
00"
11"
04"
05"
18"
09"
1:"
0="
1@"
0A"
1B"
1E"
#45
0E"
#50
b01110100 -
b01110100 .
b01110100 /
b01110100 0
b01110100 1
b01110100 2
b01110100 3
b01110100 4
b01110100 5
b01110100 6
b01000111 :
b01000111 ;
b01000111 <
b01000111 =
b01000111 >
b01000111 ?
b01110100 @
b01110100 A
b01000111 E
b01000111 F
b01000111 G
b01000111 H
b01000111 I
b01000111 J
b01110100 K
b01110100 L
b01110100 M
b01110100 N
b101 O
b00000000000000000000000000000101 b
b01110100 e
b01000111 f
b01000111 g
b01000111 k
b01000111 l
b01000111 m
b01000111 n
b01000111 o
b01000111 p
b01110100 q
b01110100 r
b01110100 s
b01000111 w
b01000111 x
b01000111 y
b01000111 z
b01000111 {
b01000111 |
b01110100 }
b01110100 ~
b01110100 !!
b01000111 %!
b01000111 &!
b01000111 '!
b01000111 (!
b01000111 )!
b01000111 *!
b01110100 +!
b01110100 ,!
b01110100 -!
b01000111 1!
b01000111 2!
b01000111 3!
b01000111 4!
b01000111 5!
b01000111 6!
b01110100 7!
b01110100 8!
b01110100 9!
b01000111 =!
b01000111 >!
b01000111 ?!
b01000111 @!
b01000111 A!
b01000111 B!
b01110100 C!
b01110100 D!
b01110100 E!
b01000111 I!
b01000111 J!
b01000111 K!
b01000111 L!
b01000111 M!
b01000111 N!
b01110100 O!
b01110100 P!
b01110100 Q!
b01000111 U!
b01000111 V!
b01000111 W!
b01000111 X!
b01000111 Y!
b01000111 Z!
b01110100 [!
b01110100 \!
b01110100 ]!
b01000111 a!
b01000111 b!
b01000111 c!
b01000111 d!
b01000111 e!
b01000111 f!
b01110100 g!
b01110100 h!
b01110100 i!
b01000111 m!
b01000111 n!
b01000111 o!
b01000111 p!
b01000111 q!
b01000111 r!
b01110100 s!
b01110100 t!
b01110100 u!
b01000111 y!
b01000111 z!
b01000111 {!
b01000111 |!
b01000111 }!
b01000111 ~!
b01110100 !"
b01110100 ""
b01110100 #"
b01110100 $"
b00000000000000000000000001000111 %"
b00000000000000000000000001000111 &"
b00000000000000000000000001110100 '"
b01110100 ("
b10001110010001110100011101110100 )"
b01110100 *"
b10001110010001110100011101110100 +"
b10001110010001110100011101110100 ,"
0-"
10"
01"
12"
17"
08"
19"
0<"
1?"
0@"
1A"
0D"
1E"
#55
0E"
#60
b00111010 -
b00111010 .
b00111010 /
b00111010 0
b00111010 1
b00111010 2
b00111010 3
b00111010 4
b00111010 5
b00111010 6
b10001110 :
b10001110 ;
b10001110 <
b10100011 =
b10100011 >
b10100011 ?
b00111010 @
b00111010 A
b10001110 E
b10001110 F
b10001110 G
b10100011 H
b10100011 I
b10100011 J
b00111010 K
b00111010 L
b00111010 M
b00111010 N
b110 O
b00000000000000000000000000000110 b
b00111010 e
b10100011 f
b10001110 g
b10001110 k
b10001110 l
b10001110 m
b10100011 n
b10100011 o
b10100011 p
b00111010 q
b00111010 r
b00111010 s
b10001110 w
b10001110 x
b10001110 y
b10100011 z
b10100011 {
b10100011 |
b00111010 }
b00111010 ~
b00111010 !!
b10001110 %!
b10001110 &!
b10001110 '!
b10100011 (!
b10100011 )!
b10100011 *!
b00111010 +!
b00111010 ,!
b00111010 -!
b10001110 1!
b10001110 2!
b10001110 3!
b10100011 4!
b10100011 5!
b10100011 6!
b00111010 7!
b00111010 8!
b00111010 9!
b10001110 =!
b10001110 >!
b10001110 ?!
b10100011 @!
b10100011 A!
b10100011 B!
b00111010 C!
b00111010 D!
b00111010 E!
b10001110 I!
b10001110 J!
b10001110 K!
b10100011 L!
b10100011 M!
b10100011 N!
b00111010 O!
b00111010 P!
b00111010 Q!
b10001110 U!
b10001110 V!
b10001110 W!
b10100011 X!
b10100011 Y!
b10100011 Z!
b00111010 [!
b00111010 \!
b00111010 ]!
b10001110 a!
b10001110 b!
b10001110 c!
b10100011 d!
b10100011 e!
b10100011 f!
b00111010 g!
b00111010 h!
b00111010 i!
b10001110 m!
b10001110 n!
b10001110 o!
b10100011 p!
b10100011 q!
b10100011 r!
b00111010 s!
b00111010 t!
b00111010 u!
b10001110 y!
b10001110 z!
b10001110 {!
b10100011 |!
b10100011 }!
b10100011 ~!
b00111010 !"
b00111010 ""
b00111010 #"
b00111010 $"
b00000000000000000000000010001110 %"
b00000000000000000000000010100011 &"
b00000000000000000000000000111010 '"
b00111010 ("
b10001110100011101010001100111010 )"
b00111010 *"
b10001110100011101010001100111010 +"
b10001110100011101010001100111010 ,"
0."
11"
02"
13"
16"
07"
18"
0;"
1>"
0?"
1@"
0C"
1E"
#65
0E"
#70
b00011101 -
b00011101 .
b00011101 /
b00011101 0
b00011101 1
b00011101 2
b00011101 3
b00011101 4
b00011101 5
b00011101 6
b01000111 :
b01000111 ;
b01000111 <
b11010001 =
b11010001 >
b11010001 ?
b00011101 @
b00011101 A
b01000111 E
b01000111 F
b01000111 G
b11010001 H
b11010001 I
b11010001 J
b00011101 K
b00011101 L
b00011101 M
b00011101 N
b111 O
b00000000000000000000000000000111 b
b00011101 e
b11010001 f
b01000111 g
b01000111 k
b01000111 l
b01000111 m
b11010001 n
b11010001 o
b11010001 p
b00011101 q
b00011101 r
b00011101 s
b01000111 w
b01000111 x
b01000111 y
b11010001 z
b11010001 {
b11010001 |
b00011101 }
b00011101 ~
b00011101 !!
b01000111 %!
b01000111 &!
b01000111 '!
b11010001 (!
b11010001 )!
b11010001 *!
b00011101 +!
b00011101 ,!
b00011101 -!
b01000111 1!
b01000111 2!
b01000111 3!
b11010001 4!
b11010001 5!
b11010001 6!
b00011101 7!
b00011101 8!
b00011101 9!
b01000111 =!
b01000111 >!
b01000111 ?!
b11010001 @!
b11010001 A!
b11010001 B!
b00011101 C!
b00011101 D!
b00011101 E!
b01000111 I!
b01000111 J!
b01000111 K!
b11010001 L!
b11010001 M!
b11010001 N!
b00011101 O!
b00011101 P!
b00011101 Q!
b01000111 U!
b01000111 V!
b01000111 W!
b11010001 X!
b11010001 Y!
b11010001 Z!
b00011101 [!
b00011101 \!
b00011101 ]!
b01000111 a!
b01000111 b!
b01000111 c!
b11010001 d!
b11010001 e!
b11010001 f!
b00011101 g!
b00011101 h!
b00011101 i!
b01000111 m!
b01000111 n!
b01000111 o!
b11010001 p!
b11010001 q!
b11010001 r!
b00011101 s!
b00011101 t!
b00011101 u!
b01000111 y!
b01000111 z!
b01000111 {!
b11010001 |!
b11010001 }!
b11010001 ~!
b00011101 !"
b00011101 ""
b00011101 #"
b00011101 $"
b00000000000000000000000001000111 %"
b00000000000000000000000011010001 &"
b00000000000000000000000000011101 '"
b00011101 ("
b10001110010001111101000100011101 )"
b00011101 *"
b10001110010001111101000100011101 +"
b10001110010001111101000100011101 ,"
0/"
12"
03"
14"
15"
06"
17"
0:"
1="
0>"
1?"
0B"
1E"
#75
0E"
#80
b10001110 -
b10001110 .
b10001110 /
b10001110 0
b10001110 1
b10001110 2
b10001110 3
b10001110 4
b10001110 5
b10001110 6
b10001110 :
b10001110 ;
b10001110 <
b10001110 =
b10001110 >
b10001110 ?
b10001110 @
b10001110 A
b10001110 E
b10001110 F
b10001110 G
b10001110 H
b10001110 I
b10001110 J
b10001110 K
b10001110 L
b10001110 M
b10001110 N
b000 O
b00000000000000000000000000001000 b
b10001110 e
b10001110 f
b10001110 g
b10001110 k
b10001110 l
b10001110 m
b10001110 n
b10001110 o
b10001110 p
b10001110 q
b10001110 r
b10001110 s
b10001110 w
b10001110 x
b10001110 y
b10001110 z
b10001110 {
b10001110 |
b10001110 }
b10001110 ~
b10001110 !!
b10001110 %!
b10001110 &!
b10001110 '!
b10001110 (!
b10001110 )!
b10001110 *!
b10001110 +!
b10001110 ,!
b10001110 -!
b10001110 1!
b10001110 2!
b10001110 3!
b10001110 4!
b10001110 5!
b10001110 6!
b10001110 7!
b10001110 8!
b10001110 9!
b10001110 =!
b10001110 >!
b10001110 ?!
b10001110 @!
b10001110 A!
b10001110 B!
b10001110 C!
b10001110 D!
b10001110 E!
b10001110 I!
b10001110 J!
b10001110 K!
b10001110 L!
b10001110 M!
b10001110 N!
b10001110 O!
b10001110 P!
b10001110 Q!
b10001110 U!
b10001110 V!
b10001110 W!
b10001110 X!
b10001110 Y!
b10001110 Z!
b10001110 [!
b10001110 \!
b10001110 ]!
b10001110 a!
b10001110 b!
b10001110 c!
b10001110 d!
b10001110 e!
b10001110 f!
b10001110 g!
b10001110 h!
b10001110 i!
b10001110 m!
b10001110 n!
b10001110 o!
b10001110 p!
b10001110 q!
b10001110 r!
b10001110 s!
b10001110 t!
b10001110 u!
b10001110 y!
b10001110 z!
b10001110 {!
b10001110 |!
b10001110 }!
b10001110 ~!
b10001110 !"
b10001110 ""
b10001110 #"
b10001110 $"
b00000000000000000000000010001110 %"
b00000000000000000000000010001110 &"
b00000000000000000000000010001110 '"
b10001110 ("
b10001110100011101000111010001110 )"
b10001110 *"
b10001110100011101000111010001110 +"
b10001110100011101000111010001110 ,"
1-"
00"
13"
04"
05"
16"
09"
1<"
0="
1>"
0A"
1D"
1E"
