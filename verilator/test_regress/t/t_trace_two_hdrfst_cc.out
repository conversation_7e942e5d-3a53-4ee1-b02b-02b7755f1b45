$date
	Wed Feb 23 00:03:39 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module topa $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var integer 32 " cyc [31:0] $end
$var integer 32 # c_trace_on [31:0] $end
$scope module sub $end
$var integer 32 $ inside_sub_a [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$scope module topb $end
$var wire 1 % clk $end
$scope module t $end
$var wire 1 % clk $end
$var integer 32 & cyc [31:0] $end
$var integer 32 ' c_trace_on [31:0] $end
$var real 64 ( r $end
$scope module sub $end
$var integer 32 ) inside_sub_a [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b00000000000000000000000000000010 )
r0 (
b00000000000000000000000000000000 '
b00000000000000000000000000000001 &
0%
b00000000000000000000000000000001 $
b00000000000000000000000000000000 #
b00000000000000000000000000000001 "
0!
$end
#10
1!
b00000000000000000000000000000010 "
b00000000000000000000000000000011 #
1%
r0.1 (
#15
0%
0!
#20
1!
1%
r0.2 (
b00000000000000000000000000000100 #
b00000000000000000000000000000011 "
#25
0%
0!
#30
1!
1%
b00000000000000000000000000000100 "
b00000000000000000000000000000101 #
r0.3 (
#35
0%
0!
#40
1!
1%
r0.4 (
b00000000000000000000000000000110 #
b00000000000000000000000000000101 "
#45
0%
0!
#50
1!
1%
b00000000000000000000000000000110 "
b00000000000000000000000000000111 #
r0.5 (
#55
0%
0!
#60
1!
1%
r0.6 (
b00000000000000000000000000001000 #
b00000000000000000000000000000111 "
#65
0%
0!
#70
1!
1%
b00000000000000000000000000001000 "
b00000000000000000000000000001001 #
r0.7 (
#75
0%
0!
#80
1!
1%
r0.7999999999999999 (
b00000000000000000000000000001010 #
b00000000000000000000000000001001 "
#85
0%
0!
#90
1!
1%
b00000000000000000000000000001010 "
b00000000000000000000000000001011 #
r0.8999999999999999 (
#95
0%
0!
#100
1!
1%
r0.9999999999999999 (
b00000000000000000000000000001100 #
b00000000000000000000000000001011 "
