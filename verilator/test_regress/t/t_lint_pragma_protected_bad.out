%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:14:17: Unknown '`pragma protect' error
   14 | `pragma protect encrypt_agent=123
      |                 ^~~~~~~~~~~~~~~~~
                     ... For error description see https://verilator.org/warn/BADSTDPRAGMA?v=latest
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:16:17: Unknown '`pragma protect' error
   16 | `pragma protect encrypt_agent_info
      |                 ^~~~~~~~~~~~~~~~~~
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:29:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:33:17: Multiple `pragma protected encoding sections
   33 | `pragma protect encoding = (enctype = "BASE64", line_length = 76, bytes = 128)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:50:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
                    ... Use "/* verilator lint_off PROTECTED */" and lint_on around source to disable this message.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:57:17: Illegal encoding type for `pragma protected encoding
   57 | `pragma protect encoding = (enctype = "A-bad-not-BASE64", line_length = 1, bytes = 295)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error-UNSUPPORTED: t/t_lint_pragma_protected_bad.v:57:17: Unsupported: only BASE64 is recognized for `pragma protected encoding
   57 | `pragma protect encoding = (enctype = "A-bad-not-BASE64", line_length = 1, bytes = 295)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:59:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:60:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
   60 | c2lvbiAzIG9mIHRoZSBHTlUgTGVzc2VyCkdlbmVyYWwgUHVibGljIExpY2Vuc2UsIGFuZCB0aGUg
      | ^
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:61:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
   61 | IkdOVSBHUEwiIHJlZmVycyB0byB2ZXJzaW9uIDMgb2YgdGhlIEdOVQpHZW5lcmFsIFB1YmxpYyBM
      | ^
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:62:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
   62 | aWNlbnNlLgoKICAiVGhlIExpYnJhcnkiIHJlZmVycyB0byBhIGNvdmVyZWQgd29yayBnb3Zlcm5l
      | ^
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:63:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
   63 | ZCBieSB0aGlzIExpY2Vuc2UsCm90aGVyIHRoYW4gYW4gQXBwbGljYXRpb24gb3IgYSBDb21iaW5l
      | ^
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:64:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
   64 | ZCBXb3JrIGFzIG==
      | ^
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:65:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:65:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:69:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:70:1: BASE64 line too long in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:70:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:72:17: Multiple `pragma protected encoding sections
   72 | `pragma protect encoding = (enctype = "BASE64", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:72:17: line_length must be multiple of 4 for BASE64
   72 | `pragma protect encoding = (enctype = "BASE64", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:74:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:75:1: BASE64 line too long in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:75:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:77:17: Multiple `pragma protected encoding sections
   77 | `pragma protect encoding = (enctype = "UUENCODE", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error-UNSUPPORTED: t/t_lint_pragma_protected_bad.v:77:17: Unsupported: only BASE64 is recognized for `pragma protected encoding
   77 | `pragma protect encoding = (enctype = "UUENCODE", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:79:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:80:1: BASE64 line too long in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:80:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:81:17: Multiple `pragma protected encoding sections
   81 | `pragma protect encoding = (enctype = "QUOTED-PRINTABLE", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:81:17: Illegal encoding type for `pragma protected encoding
   81 | `pragma protect encoding = (enctype = "QUOTED-PRINTABLE", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error-UNSUPPORTED: t/t_lint_pragma_protected_bad.v:81:17: Unsupported: only BASE64 is recognized for `pragma protected encoding
   81 | `pragma protect encoding = (enctype = "QUOTED-PRINTABLE", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:83:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:84:1: BASE64 encoding (too short) in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:84:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-UNSUPPORTED: t/t_lint_pragma_protected_bad.v:85:17: Unsupported: only BASE64 is recognized for `pragma protected encoding
   85 | `pragma protect encoding = (enctype = "RAW", line_length = 1, bytes = 4)
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Warning-PROTECTED: t/t_lint_pragma_protected_bad.v:87:17: A '`pragma protected data_block' encrypted section was detected and will be skipped.
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:88:1: BASE64 line too long in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:88:1: BASE64 encoding length mismatch in `pragma protect key_bloock/data_block
%Error-BADSTDPRAGMA: t/t_lint_pragma_protected_bad.v:96:1: `pragma is missing a pragma_expression.
   96 | `pragma
      | ^~~~~~~
%Error: Exiting due to
