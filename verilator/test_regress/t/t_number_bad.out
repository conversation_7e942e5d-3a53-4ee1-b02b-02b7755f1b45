%Error: t/t_number_bad.v:9:29: Number is missing value digits: 32'd
    9 |    parameter integer FOO2 = 32'd-6;   
      |                             ^~~~
%Error: t/t_number_bad.v:10:29: Number is missing value digits: 32'd
   10 |    parameter integer FOO3 = 32'd;
      |                             ^~~~
%Error: t/t_number_bad.v:11:29: Number is missing value digits: 32'h
   11 |    parameter integer FOO4 = 32'h;
      |                             ^~~~
%Error: t/t_number_bad.v:13:29: Illegal character in binary constant: 2
   13 |    parameter integer FOO5 = 32'b2;
      |                             ^~~~~
%Error: t/t_number_bad.v:14:29: Illegal character in octal constant
   14 |    parameter integer FOO6 = 32'o8;
      |                             ^~~~~
%Error: t/t_number_bad.v:17:33: Illegal character in binary constant: 4
   17 |    parameter logic [3:0] FOO7 = 1'b1?4'hF:4'h1;   
      |                                 ^~~~~~
%Error: t/t_number_bad.v:17:33: Too many digits for 1 bit number: 1'b1?4
   17 |    parameter logic [3:0] FOO7 = 1'b1?4'hF:4'h1;   
      |                                 ^~~~~~
%Error: t/t_number_bad.v:17:39: syntax error, unexpected INTEGER NUMBER, expecting ';'
   17 |    parameter logic [3:0] FOO7 = 1'b1?4'hF:4'h1;   
      |                                       ^~~
%Error: Exiting due to
