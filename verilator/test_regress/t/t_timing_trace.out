$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module TOP $end
  $scope module t $end
   $var wire 32 * CLK_PERIOD [31:0] $end
   $var wire 32 + CLK_HALF_PERIOD [31:0] $end
   $var wire 1 # rst $end
   $var wire 1 ( clk $end
   $var wire 1 $ a $end
   $var wire 1 ) b $end
   $var wire 1 % c $end
   $var wire 1 & d $end
   $var event 1 ' ev $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
0$
1%
0&
1'
0(
1)
b00000000000000000000000000001010 *
b00000000000000000000000000000101 +
#5
1(
#10
0%
1'
0(
#15
1(
#20
1%
1'
0(
#25
1(
#30
0%
1'
0(
#35
1(
#40
1%
1'
0(
#45
1(
#50
0%
1'
0(
#55
1(
#60
1%
1'
0(
#65
1(
#70
0%
1'
0(
#75
1(
#80
1%
1'
0(
#85
1(
#90
0%
1'
0(
#95
1(
#100
1%
1'
0(
