#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003-2009 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(simulator => 1);

top_filename("t/t_interface_ref_trace.v");
# Should be the same as the inlined version, but might have declarations
# in a different order. Sadly vcddiff can't check equivalence
# golden_filename("t/t_interface_ref_trace.out");

compile(
    verilator_flags2 => ['-fno-inline --trace-structs --trace'],
    );

execute(
    check_finished => 1,
    );

vcd_identical($Self->trace_filename,
              $Self->{golden_filename});

ok(1);
1;
