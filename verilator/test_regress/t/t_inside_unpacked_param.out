%Error: t/t_inside_unpacked_param.v:13:35: Expecting expression to be constant, but can't convert a CMETHODHARD 'inside' to constant.
                                         : ... note: In instance 't'
   13 |    localparam HIT_INSIDE = HIT_LP inside {CHECKLIST_P};
      |                                   ^~~~~~
%Error: t/t_inside_unpacked_param.v:14:37: Expecting expression to be constant, but can't convert a CMETHODHARD 'inside' to constant.
                                         : ... note: In instance 't'
   14 |    localparam MISS_INSIDE = MISS_LP inside {CHECKLIST_P};
      |                                     ^~~~~~
%Error: Exiting due to
