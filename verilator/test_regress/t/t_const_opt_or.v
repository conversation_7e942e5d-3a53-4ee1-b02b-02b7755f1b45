// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2020 <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t(/*AUTOARG*/
   // Inputs
   clk
   );
   input clk;

   integer cyc = 0;
   reg [63:0] crc;
   reg [63:0] sum;

   // Take CRC data and apply to testblock inputs
   wire [31:0] in = crc[31:0];

   /*AUTOWIRE*/
   // Beginning of automatic wires (for undeclared instantiated-module outputs)
   wire [31:0]          rd0;                    // From test of Test.v
   wire [31:0]          rd1;                    // From test of Test.v
   // End of automatics

   wire                 rden0 = crc[0];
   wire                 rden1 = crc[1];
   wire [4:0]           raddr0 = crc[20:16];
   wire [4:0]           raddr1 = crc[28:24];

   Test test(/*AUTOINST*/
             // Outputs
             .rd0                       (rd0[31:0]),
             .rd1                       (rd1[31:0]),
             // Inputs
             .clk                       (clk),
             .raddr0                    (raddr0[4:0]),
             .raddr1                    (raddr1[4:0]),
             .rden0                     (rden0),
             .rden1                     (rden1));

   // Aggregate outputs into a single result vector
   wire [63:0] result = {rd1, rd0};

   // Test loop
   always @ (posedge clk) begin
`ifdef TEST_VERBOSE
      $write("[%0t] cyc==%0d crc=%x result=%x\n", $time, cyc, crc, result);
`endif
      cyc <= cyc + 1;
      crc <= {crc[62:0], crc[63] ^ crc[2] ^ crc[0]};
      sum <= result ^ {sum[62:0], sum[63] ^ sum[2] ^ sum[0]};
      if (cyc == 0) begin
         // Setup
         crc <= 64'h5aef0c8d_d70a4497;
         sum <= '0;
      end
      else if (cyc < 10) begin
         sum <= '0;
      end
      else if (cyc == 99) begin
         $write("[%0t] cyc==%0d crc=%x sum=%x\n", $time, cyc, crc, sum);
         if (crc !== 64'hc77bb9b3784ea091) $stop;
         // What checksum will we end up with (above print should match)
`define EXPECTED_SUM 64'hdc97b141ac5d6d7d
         if (sum !== `EXPECTED_SUM) $stop;
         $write("*-* All Finished *-*\n");
         $finish;
      end
   end

endmodule

module Test(/*AUTOARG*/
   // Outputs
   rd0, rd1,
   // Inputs
   clk, raddr0, raddr1, rden0, rden1
   );

   input clk;
   input [4:0]  raddr0;
   input [4:0]  raddr1;
   input        rden0;
   input        rden1;

   output reg [31:0] rd0;
   output reg [31:0] rd1;

   reg [31:0] gpr [31:1];

   initial begin
      for (int j=1; j<32; j++ )  begin
         gpr[j] = {8'(j), 8'(j), 8'(j), 8'(j)};
      end
   end

   always_comb begin
      rd0[31:0] = 32'b0;
      rd1[31:0] = 32'b0;
      // Future optimization:
      // Multiple assignments to same variable with OR between them
      // ASSIGN(a, OR(a, aq)), ASSIGN(a, OR(a, bq)) -> ASSIGN(a, OR(a, OR(aq, bq))
      // Skip if we're not const'ing an entire module (IE doing only one assign, etc)
      for (int j=1; j<32; j++ )  begin
         rd0[31:0] |= ({32{rden0 & (raddr0[4:0]== 5'(j))}} & gpr[j][31:0]);
         rd1[31:0] |= ({32{rden1 & (raddr1[4:0]== 5'(j))}} & gpr[j][31:0]);
      end
   end

endmodule
