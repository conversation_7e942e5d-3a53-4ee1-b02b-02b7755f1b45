// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Prototypes for DPI import and export functions.
//
// Verilator includes this file in all generated .cpp files that use DPI functions.
// Manually include this file where DPI .c import functions are declared to ensure
// the C functions match the expectations of the DPI imports.

#ifndef VERILATED_VT_DPI_ARG_INPUT_UNPACK__DPI_H_
#define VERILATED_VT_DPI_ARG_INPUT_UNPACK__DPI_H_  // guard

#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif


// DPI EXPORTS
extern void e_bit121_0d(const svBitVecVal* val);
extern void e_bit121_1d(const svBitVecVal* val);
extern void e_bit121_2d(const svBitVecVal* val);
extern void e_bit121_3d(const svBitVecVal* val);
extern void e_bit1_0d(svBit val);
extern void e_bit1_1d(const svBit* val);
extern void e_bit1_2d(const svBit* val);
extern void e_bit1_3d(const svBit* val);
extern void e_bit7_0d(const svBitVecVal* val);
extern void e_bit7_1d(const svBitVecVal* val);
extern void e_bit7_2d(const svBitVecVal* val);
extern void e_bit7_3d(const svBitVecVal* val);
extern void e_byte_0d(char val);
extern void e_byte_1d(const char* val);
extern void e_byte_2d(const char* val);
extern void e_byte_3d(const char* val);
extern void e_byte_unsigned_0d(unsigned char val);
extern void e_byte_unsigned_1d(const unsigned char* val);
extern void e_byte_unsigned_2d(const unsigned char* val);
extern void e_byte_unsigned_3d(const unsigned char* val);
extern void e_chandle_0d(void* val);
extern void e_chandle_1d(const void** val);
extern void e_chandle_2d(const void** val);
extern void e_chandle_3d(const void** val);
extern void e_int_0d(int val);
extern void e_int_1d(const int* val);
extern void e_int_2d(const int* val);
extern void e_int_3d(const int* val);
extern void e_int_unsigned_0d(unsigned int val);
extern void e_int_unsigned_1d(const unsigned int* val);
extern void e_int_unsigned_2d(const unsigned int* val);
extern void e_int_unsigned_3d(const unsigned int* val);
extern void e_integer_0d(const svLogicVecVal* val);
extern void e_integer_1d(const svLogicVecVal* val);
extern void e_integer_2d(const svLogicVecVal* val);
extern void e_integer_3d(const svLogicVecVal* val);
extern void e_logic121_0d(const svLogicVecVal* val);
extern void e_logic121_1d(const svLogicVecVal* val);
extern void e_logic121_2d(const svLogicVecVal* val);
extern void e_logic121_3d(const svLogicVecVal* val);
extern void e_logic1_0d(svLogic val);
extern void e_logic1_1d(const svLogic* val);
extern void e_logic1_2d(const svLogic* val);
extern void e_logic1_3d(const svLogic* val);
extern void e_logic7_0d(const svLogicVecVal* val);
extern void e_logic7_1d(const svLogicVecVal* val);
extern void e_logic7_2d(const svLogicVecVal* val);
extern void e_logic7_3d(const svLogicVecVal* val);
extern void e_longint_0d(long long val);
extern void e_longint_1d(const long long* val);
extern void e_longint_2d(const long long* val);
extern void e_longint_3d(const long long* val);
extern void e_longint_unsigned_0d(unsigned long long val);
extern void e_longint_unsigned_1d(const unsigned long long* val);
extern void e_longint_unsigned_2d(const unsigned long long* val);
extern void e_longint_unsigned_3d(const unsigned long long* val);
extern void e_pack_struct_0d(const svLogicVecVal* val);
extern void e_pack_struct_1d(const svLogicVecVal* val);
extern void e_pack_struct_2d(const svLogicVecVal* val);
extern void e_pack_struct_3d(const svLogicVecVal* val);
extern void e_real_0d(double val);
extern void e_real_1d(const double* val);
extern void e_real_2d(const double* val);
extern void e_real_3d(const double* val);
extern void e_shortint_0d(short val);
extern void e_shortint_1d(const short* val);
extern void e_shortint_2d(const short* val);
extern void e_shortint_3d(const short* val);
extern void e_shortint_unsigned_0d(unsigned short val);
extern void e_shortint_unsigned_1d(const unsigned short* val);
extern void e_shortint_unsigned_2d(const unsigned short* val);
extern void e_shortint_unsigned_3d(const unsigned short* val);
extern void e_string_0d(const char* val);
extern void e_string_1d(const char** val);
extern void e_string_2d(const char** val);
extern void e_string_3d(const char** val);
extern void e_time_0d(const svLogicVecVal* val);
extern void e_time_1d(const svLogicVecVal* val);
extern void e_time_2d(const svLogicVecVal* val);
extern void e_time_3d(const svLogicVecVal* val);

// DPI IMPORTS
extern void check_exports();
extern void* get_non_null();
extern void i_bit121_0d(const svBitVecVal* val);
extern void i_bit121_1d(const svBitVecVal* val);
extern void i_bit121_2d(const svBitVecVal* val);
extern void i_bit121_3d(const svBitVecVal* val);
extern void i_bit1_0d(svBit val);
extern void i_bit1_1d(const svBit* val);
extern void i_bit1_2d(const svBit* val);
extern void i_bit1_3d(const svBit* val);
extern void i_bit7_0d(const svBitVecVal* val);
extern void i_bit7_1d(const svBitVecVal* val);
extern void i_bit7_2d(const svBitVecVal* val);
extern void i_bit7_3d(const svBitVecVal* val);
extern void i_byte_0d(char val);
extern void i_byte_1d(const char* val);
extern void i_byte_2d(const char* val);
extern void i_byte_3d(const char* val);
extern void i_byte_unsigned_0d(unsigned char val);
extern void i_byte_unsigned_1d(const unsigned char* val);
extern void i_byte_unsigned_2d(const unsigned char* val);
extern void i_byte_unsigned_3d(const unsigned char* val);
extern void i_chandle_0d(void* val);
extern void i_chandle_1d(const void** val);
extern void i_chandle_2d(const void** val);
extern void i_chandle_3d(const void** val);
extern void i_int_0d(int val);
extern void i_int_1d(const int* val);
extern void i_int_2d(const int* val);
extern void i_int_3d(const int* val);
extern void i_int_unsigned_0d(unsigned int val);
extern void i_int_unsigned_1d(const unsigned int* val);
extern void i_int_unsigned_2d(const unsigned int* val);
extern void i_int_unsigned_3d(const unsigned int* val);
extern void i_integer_0d(const svLogicVecVal* val);
extern void i_integer_1d(const svLogicVecVal* val);
extern void i_integer_2d(const svLogicVecVal* val);
extern void i_integer_3d(const svLogicVecVal* val);
extern void i_logic121_0d(const svLogicVecVal* val);
extern void i_logic121_1d(const svLogicVecVal* val);
extern void i_logic121_2d(const svLogicVecVal* val);
extern void i_logic121_3d(const svLogicVecVal* val);
extern void i_logic1_0d(svLogic val);
extern void i_logic1_1d(const svLogic* val);
extern void i_logic1_2d(const svLogic* val);
extern void i_logic1_3d(const svLogic* val);
extern void i_logic7_0d(const svLogicVecVal* val);
extern void i_logic7_1d(const svLogicVecVal* val);
extern void i_logic7_2d(const svLogicVecVal* val);
extern void i_logic7_3d(const svLogicVecVal* val);
extern void i_longint_0d(long long val);
extern void i_longint_1d(const long long* val);
extern void i_longint_2d(const long long* val);
extern void i_longint_3d(const long long* val);
extern void i_longint_unsigned_0d(unsigned long long val);
extern void i_longint_unsigned_1d(const unsigned long long* val);
extern void i_longint_unsigned_2d(const unsigned long long* val);
extern void i_longint_unsigned_3d(const unsigned long long* val);
extern void i_pack_struct_0d(const svLogicVecVal* val);
extern void i_pack_struct_1d(const svLogicVecVal* val);
extern void i_pack_struct_2d(const svLogicVecVal* val);
extern void i_pack_struct_3d(const svLogicVecVal* val);
extern void i_real_0d(double val);
extern void i_real_1d(const double* val);
extern void i_real_2d(const double* val);
extern void i_real_3d(const double* val);
extern void i_shortint_0d(short val);
extern void i_shortint_1d(const short* val);
extern void i_shortint_2d(const short* val);
extern void i_shortint_3d(const short* val);
extern void i_shortint_unsigned_0d(unsigned short val);
extern void i_shortint_unsigned_1d(const unsigned short* val);
extern void i_shortint_unsigned_2d(const unsigned short* val);
extern void i_shortint_unsigned_3d(const unsigned short* val);
extern void i_string_0d(const char* val);
extern void i_string_1d(const char** val);
extern void i_string_2d(const char** val);
extern void i_string_3d(const char** val);
extern void i_time_0d(const svLogicVecVal* val);
extern void i_time_1d(const svLogicVecVal* val);
extern void i_time_2d(const svLogicVecVal* val);
extern void i_time_3d(const svLogicVecVal* val);

#ifdef __cplusplus
}
#endif

#endif  // guard
