:: In top.t
Time scale of t is 1ms / 10ns
[6000000] time%0d=60  123%0t=12300000
  dig%0t=543200000 dig%0d=5432
  rdig%0t=543210988 rdig%0f=5432.109877
  acc%0t=1234567890123456789000000 acc%0d=12345678901234567890
[60000000.000000ns] time%0d=60  123%0t=123000000.000000ns
  dig%0t=5432000000.000000ns dig%0d=5432
  rdig%0t=5432109876.543210ns rdig%0f=5432.109877
  acc%0t=12345678901234567890000000.000000ns acc%0d=12345678901234567890
[60000000.000000ns] stime%0t=60000000.000000ns  stime%0d=60  stime%0f=60.000000
[60000000.000000ns] rtime%0t=60000000.000000ns  rtime%0d=60  rtime%0f=60.000000
global svGetTime = 0 0,6000000
global svGetTimeUnit = 0 -3  svGetTmePrecision = 0 -8
global vpiSimTime = 0,6000000  vpiScaledRealTime = 6e+06
global vpiTimeUnit = -3  vpiTimePrecision = -8
top.t svGetTime = 0 0,6000000
top.t svGetTimeUnit = 0 -3  svGetTmePrecision = 0 -8
top.t vpiSimTime = 0,6000000  vpiScaledRealTime = 60
top.t vpiTimeUnit = -3  vpiTimePrecision = -8
*-* All Finished *-*
