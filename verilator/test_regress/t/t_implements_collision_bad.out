%Error: t/t_implements_collision_bad.v:15:11: Class 'IclsBoth' implements 'Icls2' but missing inheritance conflict resolution for 'icfboth' (IEEE 1800-2023 8.26.6.2)
   15 | interface class IclsBoth extends Icls1, Icls2;
      |           ^~~~~
        t/t_implements_collision_bad.v:12:30: ... Location of interface class's function
   12 |    pure virtual function int icfboth;
      |                              ^~~~~~~
%Error: t/t_implements_collision_bad.v:19:1: Class 'Cls' implements 'IclsBoth' but is missing implementation for 'icfboth' (IEEE 1800-2023 8.26)
   19 | class Cls implements IclsBoth;
      | ^~~~~
        t/t_implements_collision_bad.v:8:30: ... Location of interface class's function
    8 |    pure virtual function int icfboth;
      |                              ^~~~~~~
%Error: Exiting due to
