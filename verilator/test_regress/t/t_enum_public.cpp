// -*- mode: C++; c-file-style: "cc-mode" -*-
//
// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2006 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

#include <verilated.h>

#include "Vt_enum_public.h"
#include "Vt_enum_public_p3.h"
#include "Vt_enum_public_p62.h"

double sc_time_stamp() { return 0; }

int main(int argc, char* argv[]) {
    Verilated::debug(0);
    Verilated::commandArgs(argc, argv);

    VM_PREFIX* topp = new VM_PREFIX;

    // Make sure public tag worked
    if (Vt_enum_public_p3::ZERO == Vt_enum_public_p3::ONE) {}
    if (Vt_enum_public_p62::ZERO == Vt_enum_public_p62::ALLONE) {}

    for (int i = 0; i < 10; i++) {  //
        topp->eval();
    }

    topp->final();
    VL_DO_DANGLING(delete topp, topp);
}
