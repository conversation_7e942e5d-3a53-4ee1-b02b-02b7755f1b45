%Warning-SELRANGE: t/t_select_bad_range6.v:13:16: Extracting 31 bits from only 12 bit number
                                                : ... note: In instance 't'
   13 |    assign o = i[31:1];
      |                ^
                   ... For warning description see https://verilator.org/warn/SELRANGE?v=latest
                   ... Use "/* verilator lint_off SELRANGE */" and lint_on around source to disable this message.
%Warning-SELRANGE: t/t_select_bad_range6.v:13:16: Selection index out of range: 31:1 outside 11:0
                                                : ... note: In instance 't'
   13 |    assign o = i[31:1];
      |                ^
%Error: Exiting due to
