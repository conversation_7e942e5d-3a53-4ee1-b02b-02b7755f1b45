%Warning-CASEWITHX: t/t_case_zx_bad.v:16:9: Use of x constant in casez statement, (perhaps intended ?/z in constant)
   16 |         4'b1xxx: $stop;
      |         ^~~~~~~
                    ... For warning description see https://verilator.org/warn/CASEWITHX?v=latest
                    ... Use "/* verilator lint_off CASEWITHX */" and lint_on around source to disable this message.
%Error: Exiting due to
