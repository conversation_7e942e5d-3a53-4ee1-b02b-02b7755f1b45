$date
	Sat Mar  5 14:06:13 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var int 32 " cyc [31:0] $end
$scope module sub1a $end
$var parameter 32 # ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 $ value [31:0] $end
$scope module sub2a $end
$var parameter 32 % ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 & value [31:0] $end
$upscope $end
$scope module sub2b $end
$var parameter 32 ' ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 ( value [31:0] $end
$upscope $end
$scope module sub2c $end
$var parameter 32 ) ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 * value [31:0] $end
$upscope $end
$upscope $end
$scope module sub1b $end
$var parameter 32 + ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 , value [31:0] $end
$scope module sub2a $end
$var parameter 32 - ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 . value [31:0] $end
$upscope $end
$scope module sub2b $end
$var parameter 32 / ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 0 value [31:0] $end
$upscope $end
$scope module sub2c $end
$var parameter 32 1 ADD [31:0] $end
$var wire 32 " cyc [31:0] $end
$var wire 32 2 value [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b00000000000000000000000000010111 2
b00000000000000000000000000010111 1
b00000000000000000000000000010110 0
b00000000000000000000000000010110 /
b00000000000000000000000000010101 .
b00000000000000000000000000010101 -
b00000000000000000000000000010100 ,
b00000000000000000000000000010100 +
b00000000000000000000000000001101 *
b00000000000000000000000000001101 )
b00000000000000000000000000001100 (
b00000000000000000000000000001100 '
b00000000000000000000000000001011 &
b00000000000000000000000000001011 %
b00000000000000000000000000001010 $
b00000000000000000000000000001010 #
b00000000000000000000000000000000 "
0!
$end
#1
1!
b00000000000000000000000000000001 "
b00000000000000000000000000001011 $
b00000000000000000000000000001100 &
b00000000000000000000000000001101 (
b00000000000000000000000000001110 *
b00000000000000000000000000010101 ,
b00000000000000000000000000010110 .
b00000000000000000000000000010111 0
b00000000000000000000000000011000 2
#2
0!
#3
1!
b00000000000000000000000000011001 2
b00000000000000000000000000011000 0
b00000000000000000000000000010111 .
b00000000000000000000000000010110 ,
b00000000000000000000000000001111 *
b00000000000000000000000000001110 (
b00000000000000000000000000001101 &
b00000000000000000000000000001100 $
b00000000000000000000000000000010 "
#4
0!
#5
1!
b00000000000000000000000000000011 "
b00000000000000000000000000001101 $
b00000000000000000000000000001110 &
b00000000000000000000000000001111 (
b00000000000000000000000000010000 *
b00000000000000000000000000010111 ,
b00000000000000000000000000011000 .
b00000000000000000000000000011001 0
b00000000000000000000000000011010 2
#6
0!
#7
1!
b00000000000000000000000000011011 2
b00000000000000000000000000011010 0
b00000000000000000000000000011001 .
b00000000000000000000000000011000 ,
b00000000000000000000000000010001 *
b00000000000000000000000000010000 (
b00000000000000000000000000001111 &
b00000000000000000000000000001110 $
b00000000000000000000000000000100 "
#8
0!
#9
1!
b00000000000000000000000000000101 "
b00000000000000000000000000001111 $
b00000000000000000000000000010000 &
b00000000000000000000000000010001 (
b00000000000000000000000000010010 *
b00000000000000000000000000011001 ,
b00000000000000000000000000011010 .
b00000000000000000000000000011011 0
b00000000000000000000000000011100 2
#10
0!
#11
1!
b00000000000000000000000000011101 2
b00000000000000000000000000011100 0
b00000000000000000000000000011011 .
b00000000000000000000000000011010 ,
b00000000000000000000000000010011 *
b00000000000000000000000000010010 (
b00000000000000000000000000010001 &
b00000000000000000000000000010000 $
b00000000000000000000000000000110 "
#12
0!
#13
1!
b00000000000000000000000000000111 "
b00000000000000000000000000010001 $
b00000000000000000000000000010010 &
b00000000000000000000000000010011 (
b00000000000000000000000000010100 *
b00000000000000000000000000011011 ,
b00000000000000000000000000011100 .
b00000000000000000000000000011101 0
b00000000000000000000000000011110 2
#14
0!
#15
1!
b00000000000000000000000000011111 2
b00000000000000000000000000011110 0
b00000000000000000000000000011101 .
b00000000000000000000000000011100 ,
b00000000000000000000000000010101 *
b00000000000000000000000000010100 (
b00000000000000000000000000010011 &
b00000000000000000000000000010010 $
b00000000000000000000000000001000 "
#16
0!
#17
1!
b00000000000000000000000000001001 "
b00000000000000000000000000010011 $
b00000000000000000000000000010100 &
b00000000000000000000000000010101 (
b00000000000000000000000000010110 *
b00000000000000000000000000011101 ,
b00000000000000000000000000011110 .
b00000000000000000000000000011111 0
b00000000000000000000000000100000 2
#18
0!
#19
1!
b00000000000000000000000000100001 2
b00000000000000000000000000100000 0
b00000000000000000000000000011111 .
b00000000000000000000000000011110 ,
b00000000000000000000000000010111 *
b00000000000000000000000000010110 (
b00000000000000000000000000010101 &
b00000000000000000000000000010100 $
b00000000000000000000000000001010 "
#20
0!
