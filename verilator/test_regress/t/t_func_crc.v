// DESCRIPTION: Verilator: Verilog Test module
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2020 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/
   // Inputs
   clk
   );

   input clk;

   reg [63:0] d;
   reg [31:0] c;

   wire [31:0] q = crc (d, c);
   reg [31:0]  q_r;

   integer cyc; initial cyc=1;
   always @ (posedge clk) begin
      if (cyc!=0) begin
         cyc <= cyc + 1;
         q_r <= q;
         c <= q;
         d <= {d[62:0], ^d[63:48]};
         //$write("%d crc(%x,%x)=%x\n", cyc, d, c, q);
         if (cyc==1) begin
            // Assign inputs randomly
            q_r <= 32'h12345678;
            c   <= 32'h12345678;
            d <= 64'hffffffff_ffffffff;
         end
         if (cyc==2) begin
            d <= 64'hffffffff_ffffffff;
         end
         if (cyc==3) begin
            d <= 64'hffffffff_ffffffff;
         end
         if (cyc==4) begin
            d <= 64'h50183721_81a04b1a;
         end
         if (cyc==5) begin
         end
         if (cyc==9) begin
            if (q !== 32'h38295e96) $stop;
            $write("*-* All Finished *-*\n");
            $finish;
         end
      end
   end

   function [31:0] crc;
      input [63:0] di;
      input [31:0] ci;
      reg [63:0]   drev;
      begin
         drev = reverse(di);
         crc = newcrc(drev, ci);
      end
   endfunction

   function [63:0] reverse;
      input [63:0] di;
      integer      i;
      begin
         reverse = 64'b0;
         for (i=0; i<64; i=i+1) reverse[i] = di[63-i];
      end
   endfunction

   function [31:0] newcrc;
      input [63:0] D;
      input [31:0] C;
      reg [31:0]   N;
      reg [31:0]   DT;
      begin
         N = 32'b0;
         // Note this isn't a real CRC code; it's been munged for privacy
         N[0] = D[59]^D[53]^D[52]^D[49]^D[44]^D[41]^D[40]^D[39]^D[37]^D[32]^D[29]^D[26]^D[22]^D[21]^D[20]^D[16]^D[15]^D[14]^D[9]^D[7]^D[0]
           ^C[29]^C[27]^C[24]^C[23]^C[22]^C[21]^C[19]^C[15]^C[13]^C[10]^C[8]^C[3]^C[1];
         N[1] = D[61]^D[57]^D[51]^D[47]^D[43]^D[37]^D[35]^D[32]^D[28]^D[24]^D[22]^D[21]^D[20]^D[16]^D[12]^D[11]^D[10]^D[8]^D[7]^D[6]^D[1]^D[0]
           ^C[30]^C[27]^C[26]^C[20]^C[16]^C[14]^C[13]^C[11]^C[10]^C[8]^C[5]^C[0];
         N[2] = D[63]^D[62]^D[61]^D[60]^D[55]^D[54]^D[52]^D[44]^D[43]^D[42]^D[37]^D[34]^D[33]^D[29]^D[28]^D[25]^D[24]^D[23]^D[22]^D[18]^D[16]^D[15]^D[13]^D[12]^D[11]
           ^C[31]^C[30]^C[27]^C[22]^C[21]^C[18]^C[15]^C[12]^C[11]^C[10]^C[7];
         N[3] = D[62]^D[54]^D[50]^D[47]^D[46]^D[38]^D[36]^D[35]^D[34]^D[33]^D[32]^D[30]^D[27]^D[25]^D[21]^D[20]^D[19]^D[17]^D[15]^D[11]^D[8]^D[5]^D[3]^D[1]^D[0]
           ^C[28]^C[25]^C[24]^C[13]^C[11]^C[9]^C[8]^C[7]^C[3]^C[1];
         N[4] = D[57]^D[54]^D[53]^D[52]^D[45]^D[44]^D[43]^D[39]^D[37]^D[34]^D[33]^D[32]^D[31]^D[28]^D[24]^D[23]^D[20]^D[19]^D[15]^D[14]^D[10]^D[6]^D[1]^D[0]
           ^C[30]^C[24]^C[20]^C[16]^C[14]^C[11]^C[8]^C[7]^C[6]^C[5]^C[2];
         N[5] = D[58]^D[57]^D[50]^D[49]^D[48]^D[47]^D[43]^D[39]^D[29]^D[26]^D[23]^D[22]^D[20]^D[18]^D[14]^D[10]^D[9]^D[6]^D[5]^D[4]^D[1]
           ^C[27]^C[24]^C[20]^C[19]^C[18]^C[14]^C[13]^C[12]^C[11]^C[8]^C[7]^C[1];
         N[6] = D[63]^D[62]^D[61]^D[57]^D[51]^D[50]^D[47]^D[38]^D[37]^D[34]^D[30]^D[28]^D[27]^D[25]^D[21]^D[16]^D[15]^D[10]^D[9]^D[6]^D[5]^D[2]^D[1]
           ^C[31]^C[27]^C[25]^C[16]^C[13]^C[9]^C[8]^C[7]^C[0];
         N[7] = ^D[62]^D[61]^D[59]^D[54]^D[52]^D[51]^D[49]^D[46]^D[45]^D[42]^D[41]^D[38]^D[35]^D[29]^D[26]^D[24]^D[15]^D[12]^D[11]^D[9]^D[2]^D[0]
           ^C[28]^C[27]^C[26]^C[20]^C[19]^C[18]^C[15]^C[12]^C[7]^C[4];
         N[8] = D[62]^D[61]^D[60]^D[59]^D[52]^D[50]^D[48]^D[47]^D[46]^D[45]^D[44]^D[42]^D[41]^D[40]^D[30]^D[24]^D[23]^D[22]^D[19]^D[17]^D[11]^D[10]^D[7]^D[6]^D[2]
           ^C[31]^C[29]^C[27]^C[22]^C[21]^C[19]^C[17]^C[11]^C[9]^C[7]^C[6];
         N[9] = D[62]^D[59]^D[58]^D[57]^D[54]^D[51]^D[50]^D[43]^D[41]^D[39]^D[28]^D[25]^D[24]^D[23]^D[22]^D[21]^D[18]^D[16]^D[15]^D[7]
           ^C[30]^C[29]^C[27]^C[25]^C[23]^C[22]^C[13]^C[12]^C[7]^C[6]^C[5]^C[1];
         N[10] = D[61]^D[60]^D[58]^D[56]^D[54]^D[53]^D[51]^D[48]^D[46]^D[43]^D[42]^D[38]^D[37]^D[35]^D[33]^D[31]^D[30]^D[27]^D[26]^D[24]^D[19]^D[10]^D[8]^D[6]^D[1]
           ^C[31]^C[30]^C[26]^C[25]^C[24]^C[21]^C[16]^C[12]^C[3]^C[2];
         N[11] = D[59]^D[57]^D[56]^D[50]^D[49]^D[48]^D[47]^D[46]^D[45]^D[42]^D[41]^D[40]^D[33]^D[32]^D[30]^D[25]^D[21]^D[15]^D[14]^D[13]^D[12]^D[11]^D[5]^D[1]
           ^C[27]^C[25]^C[24]^C[21]^C[16]^C[12]^C[7]^C[3]^C[2]^C[1];
         N[12] = D[62]^D[61]^D[59]^D[58]^D[56]^D[55]^D[53]^D[48]^D[47]^D[44]^D[43]^D[35]^D[31]^D[30]^D[28]^D[24]^D[23]^D[21]^D[14]^D[5]^D[2]
           ^C[28]^C[26]^C[25]^C[23]^C[22]^C[18]^C[16]^C[15]^C[6];
         N[13] = D[63]^D[60]^D[58]^D[57]^D[55]^D[54]^D[53]^D[51]^D[47]^D[45]^D[42]^D[41]^D[38]^D[28]^D[26]^D[25]^D[22]^D[20]^D[18]^D[17]^D[15]^D[13]^D[12]^D[11]
           ^C[29]^C[28]^C[25]^C[22]^C[19]^C[17]^C[16]^C[15]^C[14]^C[12]^C[10]^C[9];
         N[14] = D[58]^D[56]^D[55]^D[52]^D[47]^D[43]^D[41]^D[40]^D[39]^D[38]^D[30]^D[26]^D[25]^D[22]^D[19]^D[17]^D[13]^D[11]^D[10]^D[9]^D[8]^D[3]^D[2]^D[0]
           ^C[31]^C[28]^C[20]^C[18]^C[17]^C[16]^C[15]^C[13]^C[11]^C[4]^C[2]^C[1];
         N[15] = D[63]^D[62]^D[61]^D[59]^D[58]^D[48]^D[47]^D[43]^D[42]^D[35]^D[28]^D[26]^D[25]^D[24]^D[23]^D[22]^D[21]^D[20]^D[19]^D[17]^D[11]^D[7]^D[2]
           ^C[30]^C[29]^C[27]^C[24]^C[20]^C[17]^C[16]^C[15]^C[11]^C[9]^C[5];
         N[16] = D[60]^D[57]^D[49]^D[46]^D[45]^D[43]^D[39]^D[36]^D[32]^D[30]^D[29]^D[28]^D[27]^D[26]^D[23]^D[20]^D[19]^D[17]^D[11]^D[8]^D[5]^D[1]
           ^C[28]^C[26]^C[23]^C[22]^C[18]^C[16]^C[13]^C[12]^C[10]^C[9]^C[6];
         N[17] = D[63]^D[62]^D[61]^D[60]^D[58]^D[54]^D[53]^D[51]^D[48]^D[42]^D[41]^D[37]^D[36]^D[34]^D[28]^D[27]^D[26]^D[24]^D[13]^D[12]^D[9]^D[7]^D[4]^D[0]
           ^C[31]^C[30]^C[27]^C[23]^C[20]^C[17]^C[14]^C[9]^C[6]^C[4]^C[3]^C[0];
         N[18] = D[63]^D[61]^D[59]^D[56]^D[52]^D[50]^D[47]^D[42]^D[37]^D[35]^D[34]^D[31]^D[30]^D[29]^D[22]^D[19]^D[17]^D[16]^D[11]^D[9]^D[8]^D[7]
           ^C[26]^C[22]^C[20]^C[19]^C[16]^C[11]^C[8]^C[6]^C[5]^C[0];
         N[19] = D[62]^D[60]^D[52]^D[49]^D[44]^D[43]^D[42]^D[37]^D[33]^D[32]^D[29]^D[26]^D[19]^D[17]^D[16]^D[12]^D[10]^D[7]^D[6]^D[4]^D[3]^D[2]
           ^C[30]^C[29]^C[26]^C[25]^C[22]^C[19]^C[14]^C[7]^C[6]^C[5]^C[2]^C[0];
         N[20] = D[63]^D[58]^D[54]^D[48]^D[47]^D[40]^D[39]^D[35]^D[34]^D[32]^D[31]^D[28]^D[27]^D[25]^D[18]^D[12]^D[9]^D[7]^D[5]^D[4]^D[3]^D[2]^D[1]
           ^C[31]^C[29]^C[28]^C[25]^C[19]^C[18]^C[17]^C[15]^C[10]^C[9]^C[6]^C[4];
         N[21] = D[61]^D[59]^D[57]^D[56]^D[53]^D[48]^D[44]^D[43]^D[41]^D[35]^D[29]^D[26]^D[25]^D[20]^D[18]^D[17]^D[16]^D[12]^D[9]^D[6]^D[5]^D[3]^D[1]
           ^C[30]^C[27]^C[24]^C[23]^C[22]^C[21]^C[20]^C[13]^C[9]^C[3]^C[2];
         N[22] = D[63]^D[62]^D[60]^D[57]^D[53]^D[51]^D[45]^D[44]^D[42]^D[34]^D[33]^D[27]^D[20]^D[19]^D[18]^D[15]^D[10]^D[9]^D[8]^D[4]^D[3]
           ^C[24]^C[23]^C[18]^C[17]^C[16]^C[14]^C[12]^C[11]^C[10]^C[9]^C[6]^C[5];
         N[23] = D[58]^D[56]^D[54]^D[51]^D[47]^D[43]^D[42]^D[40]^D[37]^D[36]^D[33]^D[25]^D[23]^D[20]^D[18]^D[16]^D[15]^D[12]^D[10]^D[8]^D[7]^D[5]^D[3]
           ^C[31]^C[27]^C[26]^C[23]^C[21]^C[18]^C[15]^C[11]^C[10]^C[8]^C[7]^C[1];
         N[24] = D[60]^D[59]^D[52]^D[50]^D[48]^D[44]^D[39]^D[36]^D[35]^D[31]^D[30]^D[28]^D[27]^D[23]^D[22]^D[21]^D[19]^D[14]^D[13]^D[12]^D[9]^D[4]^D[1]^D[0]
           ^C[27]^C[25]^C[23]^C[21]^C[17]^C[11]^C[10]^C[4]^C[0];
         N[25] = D[61]^D[60]^D[56]^D[54]^D[51]^D[46]^D[43]^D[41]^D[40]^D[38]^D[37]^D[36]^D[29]^D[28]^D[27]^D[22]^D[17]^D[15]^D[10]^D[7]^D[4]^D[2]
           ^C[29]^C[28]^C[26]^C[23]^C[18]^C[14]^C[13]^C[12]^C[11]^C[9]^C[8]^C[6];
         N[26] = D[63]^D[62]^D[58]^D[55]^D[54]^D[52]^D[50]^D[39]^D[37]^D[36]^D[35]^D[33]^D[31]^D[29]^D[27]^D[18]^D[14]^D[10]^D[3]^D[2]^D[0]
           ^C[31]^C[27]^C[26]^C[25]^C[24]^C[21]^C[13]^C[12]^C[10]^C[1];
         N[27] = D[62]^D[60]^D[58]^D[56]^D[55]^D[54]^D[51]^D[44]^D[41]^D[36]^D[34]^D[32]^D[31]^D[29]^D[28]^D[27]^D[23]^D[17]^D[12]^D[11]^D[8]^D[6]^D[4]^D[2]
           ^C[31]^C[30]^C[28]^C[27]^C[23]^C[19]^C[17]^C[16]^C[14]^C[12]^C[11]^C[10]^C[3];
         N[28] = D[57]^D[54]^D[53]^D[51]^D[50]^D[48]^D[40]^D[38]^D[34]^D[33]^D[31]^D[30]^D[29]^D[27]^D[23]^D[21]^D[14]^D[9]^D[7]^D[6]^D[5]^D[4]^D[0]
           ^C[31]^C[30]^C[26]^C[24]^C[15]^C[14]^C[13]^C[7]^C[6]^C[4]^C[3]^C[0];
         N[29] = D[62]^D[60]^D[55]^D[46]^D[45]^D[44]^D[43]^D[41]^D[40]^D[35]^D[33]^D[32]^D[30]^D[28]^D[25]^D[23]^D[22]^D[13]^D[8]^D[7]^D[6]^D[5]^D[4]^D[3]^D[1]^D[0]
           ^C[31]^C[28]^C[27]^C[18]^C[11]^C[8]^C[6]^C[4]^C[2]^C[1]^C[0];
         N[30] = D[63]^D[62]^D[59]^D[58]^D[55]^D[52]^D[47]^D[44]^D[36]^D[35]^D[34]^D[31]^D[29]^D[22]^D[21]^D[20]^D[19]^D[15]^D[14]^D[10]^D[6]^D[3]^D[2]^D[0]
           ^C[28]^C[25]^C[24]^C[22]^C[20]^C[15]^C[14]^C[12]^C[10]^C[9]^C[4]^C[0];
         N[31] = D[61]^D[58]^D[56]^D[55]^D[54]^D[52]^D[51]^D[50]^D[49]^D[42]^D[38]^D[37]^D[36]^D[34]^D[31]^D[30]^D[27]^D[26]^D[23]^D[22]^D[21]^D[19]^D[18]^D[12]^D[0]
           ^C[28]^C[26]^C[24]^C[21]^C[17]^C[16]^C[14]^C[13]^C[10]^C[8]^C[2];
         newcrc = N;
      end
   endfunction
endmodule
