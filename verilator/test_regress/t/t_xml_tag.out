<?xml version="1.0" ?>
<!-- DESCRIPTION: Verilator output: XML representation of netlist -->
<verilator_xml>
  <files>
    <file id="a" filename="&lt;built-in&gt;" language="1800-2023"/>
    <file id="b" filename="&lt;command-line&gt;" language="1800-2023"/>
    <file id="c" filename="input.vc" language="1800-2023"/>
    <file id="d" filename="t/t_xml_tag.v" language="1800-2023"/>
  </files>
  <module_files>
    <file id="d" filename="t/t_xml_tag.v" language="1800-2023"/>
  </module_files>
  <cells>
    <cell loc="d,12,8,12,9" name="m" submodname="m" hier="m">
      <cell loc="d,29,8,29,12" name="itop" submodname="ifc" hier="m.itop"/>
    </cell>
  </cells>
  <netlist>
    <module loc="d,12,8,12,9" name="m" origName="m" topModule="1">
      <var loc="d,14,11,14,17" name="clk_ip" tag="clk_ip" dtype_id="1" dir="input" pinIndex="1" vartype="logic" origName="clk_ip"/>
      <var loc="d,15,11,15,17" name="rst_ip" dtype_id="1" dir="input" pinIndex="2" vartype="logic" origName="rst_ip"/>
      <var loc="d,16,11,16,17" name="foo_op" tag="foo_op" dtype_id="1" dir="output" pinIndex="3" vartype="logic" origName="foo_op"/>
      <typedef loc="d,25,6,25,15" name="my_struct" tag="my_struct" dtype_id="2"/>
      <instance loc="d,29,8,29,12" name="itop" defName="ifc" origName="itop"/>
      <var loc="d,29,8,29,12" name="itop__Viftop" dtype_id="3" vartype="ifaceref" origName="itop__Viftop"/>
      <var loc="d,31,14,31,25" name="this_struct" tag="this_struct" dtype_id="4" vartype="" origName="this_struct"/>
      <var loc="d,33,16,33,22" name="dotted" dtype_id="5" vartype="logic" origName="dotted"/>
      <contassign loc="d,33,23,33,24" dtype_id="5">
        <varxref loc="d,33,30,33,35" name="value" dtype_id="6" dotted="itop"/>
        <varref loc="d,33,16,33,22" name="dotted" dtype_id="5"/>
      </contassign>
      <func loc="d,35,13,35,14" name="f" dtype_id="1">
        <var loc="d,35,13,35,14" name="f" dtype_id="1" dir="output" vartype="logic" origName="f"/>
        <var loc="d,35,28,35,29" name="m" dtype_id="7" dir="input" vartype="string" origName="m"/>
        <display loc="d,36,7,36,15" displaytype="$display">
          <sformatf loc="d,36,7,36,15" name="%@" dtype_id="7">
            <varref loc="d,36,22,36,23" name="m" dtype_id="7"/>
          </sformatf>
        </display>
      </func>
      <initial loc="d,39,4,39,11">
        <begin loc="d,39,12,39,17">
          <stmtexpr loc="d,41,7,41,8">
            <taskref loc="d,41,7,41,8" name="f" dtype_id="8">
              <arg loc="d,41,9,41,736">
                <const loc="d,41,9,41,736" name="&quot;&#1;&#2;&#3;&#4;&#5;&#6;&#7;&#8;&#9;&#10;&#11;&#12;&#13;&#14;&#15;&#16;&#17;&#18;&#19;&#20;&#21;&#22;&#23;&#24;&#25;&#26;&#27;&#28;&#29;&#30;&#31; !&quot;#$%&amp;&apos;()*+,-./0123456789:;&lt;=&gt;?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abcdefghijklmnopqrstuvwxyz{|}~&#127;&#128;&#129;&#130;&#131;&#132;&#133;&#134;&#135;&#136;&#137;&#138;&#139;&#140;&#141;&#142;&#143;&#144;&#145;&#146;&#147;&#148;&#149;&#150;&#151;&#152;&#153;&#154;&#155;&#156;&#157;&#158;&#159;&#160;&#161;&#162;&#163;&#164;&#165;&#166;&#167;&#168;&#169;&#170;&#171;&#172;&#173;&#174;&#175;&#176;&#177;&#178;&#179;&#180;&#181;&#182;&#183;&#184;&#185;&#186;&#187;&#188;&#189;&#190;&#191;&#192;&#193;&#194;&#195;&#196;&#197;&#198;&#199;&#200;&#201;&#202;&#203;&#204;&#205;&#206;&#207;&#208;&#209;&#210;&#211;&#212;&#213;&#214;&#215;&#216;&#217;&#218;&#219;&#220;&#221;&#222;&#223;&#224;&#225;&#226;&#227;&#228;&#229;&#230;&#231;&#232;&#233;&#234;&#235;&#236;&#237;&#238;&#239;&#240;&#241;&#242;&#243;&#244;&#245;&#246;&#247;&#248;&#249;&#250;&#251;&#252;&#253;&#254;&#255;&quot;" dtype_id="7"/>
              </arg>
            </taskref>
          </stmtexpr>
        </begin>
      </initial>
    </module>
    <iface loc="d,7,11,7,14" name="ifc" origName="ifc">
      <var loc="d,8,12,8,17" name="value" dtype_id="6" vartype="integer" origName="value"/>
      <modport loc="d,9,12,9,23" name="out_modport">
        <modportvarref loc="d,9,32,9,37" name="value" direction="out"/>
      </modport>
    </iface>
    <typetable loc="a,0,0,0,0">
      <voiddtype loc="d,41,7,41,8" id="8"/>
      <basicdtype loc="d,8,4,8,11" id="6" name="integer" left="31" right="0" signed="true"/>
      <basicdtype loc="d,14,11,14,17" id="1" name="logic"/>
      <basicdtype loc="d,21,7,21,12" id="9" name="logic"/>
      <basicdtype loc="d,22,7,22,12" id="10" name="logic"/>
      <basicdtype loc="d,23,7,23,12" id="11" name="logic"/>
      <basicdtype loc="d,24,7,24,12" id="12" name="logic"/>
      <structdtype loc="d,20,12,20,18" id="2" name="m.my_struct">
        <memberdtype loc="d,21,19,21,22" id="13" name="clk" tag="this is clk" sub_dtype_id="9"/>
        <memberdtype loc="d,22,19,22,20" id="14" name="k" sub_dtype_id="10"/>
        <memberdtype loc="d,23,19,23,25" id="15" name="enable" tag="enable" sub_dtype_id="11"/>
        <memberdtype loc="d,24,19,24,23" id="16" name="data" tag="data" sub_dtype_id="12"/>
      </structdtype>
      <ifacerefdtype loc="d,29,8,29,12" id="3" modportname=""/>
      <basicdtype loc="d,31,27,31,28" id="5" name="logic" left="31" right="0"/>
      <refdtype loc="d,31,4,31,13" id="17" name="my_struct" sub_dtype_id="2"/>
      <unpackarraydtype loc="d,31,26,31,27" id="4" sub_dtype_id="2">
        <range loc="d,31,26,31,27">
          <const loc="d,31,27,31,28" name="32&apos;h0" dtype_id="5"/>
          <const loc="d,31,27,31,28" name="32&apos;h1" dtype_id="5"/>
        </range>
      </unpackarraydtype>
      <basicdtype loc="d,35,21,35,27" id="7" name="string"/>
    </typetable>
  </netlist>
</verilator_xml>
