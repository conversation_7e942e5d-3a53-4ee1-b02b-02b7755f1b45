%Warning-WIDTHTRUNC: t/t_lint_width_genfor_bad.v:25:13: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's CONST '?32?sh10' generates 32 or 5 bits.
                                                      : ... note: In instance 't'
   25 |          rg = g;   
      |             ^
                     ... For warning description see https://verilator.org/warn/WIDTHTRUNC?v=latest
                     ... Use "/* verilator lint_off WIDTHTRUNC */" and lint_on around source to disable this message.
%Warning-WIDTHTRUNC: t/t_lint_width_genfor_bad.v:26:13: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's VARREF 'P' generates 32 or 5 bits.
                                                      : ... note: In instance 't'
   26 |          rp = P;   
      |             ^
%Warning-WIDTHTRUNC: t/t_lint_width_genfor_bad.v:27:13: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's VARREF 'w' generates 5 bits.
                                                      : ... note: In instance 't'
   27 |          rw = w;   
      |             ^
%Warning-WIDTHTRUNC: t/t_lint_width_genfor_bad.v:28:13: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's CONST '64'h1' generates 64 bits.
                                                      : ... note: In instance 't'
   28 |          rc = 64'h1;   
      |             ^
%Warning-WIDTHTRUNC: t/t_lint_width_genfor_bad.v:33:13: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's VARREF 'i' generates 32 bits.
                                                      : ... note: In instance 't'
   33 |          ri = i;   
      |             ^
%Error: Exiting due to
