#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(simulator => 1);

sub gen {
    # Generate using file to avoid missing newline in repository
    my $filename = shift;
    my $fh = IO::File->new(">$filename");
    $fh->print("// Generated by t_vthread.pl\n");
    $fh->print("1\n");
    $fh->print("10\n");
    $fh->print("20\n");
    $fh->print("30");  # No newline
}

gen($Self->{obj_dir} . "/dat.mem");

compile(
    );

execute(
    check_finished => 1,
    );

ok(1);
1;
