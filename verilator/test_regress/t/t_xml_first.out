<?xml version="1.0" ?>
<!-- DESCRIPTION: Verilator output: XML representation of netlist -->
<verilator_xml>
  <files>
    <file id="a" filename="&lt;built-in&gt;" language="1800-2023"/>
    <file id="b" filename="&lt;command-line&gt;" language="1800-2023"/>
    <file id="c" filename="input.vc" language="1800-2023"/>
    <file id="d" filename="t/t_xml_first.v" language="1800-2023"/>
  </files>
  <module_files>
    <file id="d" filename="t/t_xml_first.v" language="1800-2023"/>
  </module_files>
  <cells>
    <cell loc="d,7,8,7,9" name="t" submodname="t" hier="t">
      <cell loc="d,20,4,20,9" name="cell1" submodname="mod1__W4" hier="t.cell1"/>
      <cell loc="d,25,6,25,11" name="cell2" submodname="mod2" hier="t.cell2"/>
    </cell>
  </cells>
  <netlist>
    <module loc="d,7,8,7,9" name="t" origName="t" topModule="1">
      <var loc="d,15,22,15,23" name="q" dtype_id="1" dir="output" pinIndex="1" vartype="logic" origName="q"/>
      <var loc="d,13,10,13,13" name="clk" dtype_id="2" dir="input" pinIndex="2" vartype="logic" origName="clk"/>
      <var loc="d,14,16,14,17" name="d" dtype_id="1" dir="input" pinIndex="3" vartype="logic" origName="d"/>
      <var loc="d,17,22,17,29" name="between" dtype_id="1" vartype="logic" origName="between"/>
      <instance loc="d,20,4,20,9" name="cell1" defName="mod1__W4" origName="cell1">
        <port loc="d,20,12,20,13" name="q" direction="out" portIndex="1">
          <varref loc="d,20,14,20,21" name="between" dtype_id="1"/>
        </port>
        <port loc="d,21,12,21,15" name="clk" direction="in" portIndex="2">
          <varref loc="d,21,42,21,45" name="clk" dtype_id="2"/>
        </port>
        <port loc="d,22,12,22,13" name="d" direction="in" portIndex="3">
          <varref loc="d,22,42,22,43" name="d" dtype_id="1"/>
        </port>
      </instance>
      <instance loc="d,25,6,25,11" name="cell2" defName="mod2" origName="cell2">
        <port loc="d,25,14,25,15" name="d" direction="in" portIndex="1">
          <varref loc="d,25,16,25,23" name="between" dtype_id="1"/>
        </port>
        <port loc="d,26,14,26,15" name="q" direction="out" portIndex="2">
          <varref loc="d,26,42,26,43" name="q" dtype_id="1"/>
        </port>
        <port loc="d,27,14,27,17" name="clk" direction="in" portIndex="3">
          <varref loc="d,27,42,27,45" name="clk" dtype_id="2"/>
        </port>
      </instance>
    </module>
    <module loc="d,46,8,46,12" name="mod2" origName="mod2">
      <var loc="d,48,10,48,13" name="clk" dtype_id="2" dir="input" pinIndex="1" vartype="logic" origName="clk"/>
      <var loc="d,49,16,49,17" name="d" dtype_id="1" dir="input" pinIndex="2" vartype="logic" origName="d"/>
      <var loc="d,50,22,50,23" name="q" dtype_id="1" dir="output" pinIndex="3" vartype="logic" origName="q"/>
      <contassign loc="d,53,13,53,14" dtype_id="1">
        <varref loc="d,49,16,49,17" name="d" dtype_id="1"/>
        <varref loc="d,53,13,53,14" name="q" dtype_id="1"/>
      </contassign>
    </module>
    <module loc="d,31,8,31,12" name="mod1__W4" origName="mod1">
      <var loc="d,32,15,32,20" name="WIDTH" dtype_id="3" vartype="logic" origName="WIDTH" param="true">
        <const loc="d,19,18,19,19" name="32&apos;sh4" dtype_id="3"/>
      </var>
      <var loc="d,34,24,34,27" name="clk" dtype_id="2" dir="input" pinIndex="1" vartype="logic" origName="clk"/>
      <var loc="d,35,30,35,31" name="d" dtype_id="1" dir="input" pinIndex="2" vartype="logic" origName="d"/>
      <var loc="d,36,30,36,31" name="q" dtype_id="1" dir="output" pinIndex="3" vartype="logic" origName="q"/>
      <var loc="d,39,15,39,22" name="IGNORED" dtype_id="3" vartype="logic" origName="IGNORED" localparam="true">
        <const loc="d,39,25,39,26" name="32&apos;sh1" dtype_id="3"/>
      </var>
      <always loc="d,41,4,41,10">
        <sentree loc="d,41,11,41,12">
          <senitem loc="d,41,13,41,20" edgeType="POS">
            <varref loc="d,41,21,41,24" name="clk" dtype_id="2"/>
          </senitem>
        </sentree>
        <assigndly loc="d,42,8,42,10" dtype_id="1">
          <varref loc="d,42,11,42,12" name="d" dtype_id="1"/>
          <varref loc="d,42,6,42,7" name="q" dtype_id="1"/>
        </assigndly>
      </always>
    </module>
    <typetable loc="a,0,0,0,0">
      <basicdtype loc="d,34,24,34,27" id="2" name="logic"/>
      <basicdtype loc="d,15,16,15,17" id="1" name="logic" left="3" right="0"/>
      <basicdtype loc="d,19,18,19,19" id="3" name="logic" left="31" right="0" signed="true"/>
    </typetable>
  </netlist>
</verilator_xml>
