#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003-2009 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(vlt => 1);

# Hit the debug statements in the preprocessor for internal coverage

run(cmd => ["$ENV{VERILATOR_ROOT}/bin/verilator",
            "-E",
            "t/t_preproc_debugi.v",
            "--debug",
            "--debugi-V3PreShell 10",
    ],
    tee => $Self->{verbose},
    logfile => "$Self->{obj_dir}/sim.log",
    verilator_run => 1,
    );

ok(1);
1;
