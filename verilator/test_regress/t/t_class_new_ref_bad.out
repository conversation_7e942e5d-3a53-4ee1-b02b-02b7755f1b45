%Error: t/t_class_new_ref_bad.v:16:24: new() assignment not legal to non-class data type 'int'
                                     : ... note: In instance 't'
   16 |       txn_type_t txn = new;
      |                        ^~~
%Error: t/t_class_new_ref_bad.v:17:25: new() cannot copy from non-class data type 'int'
                                     : ... note: In instance 't'
   17 |       txn_type_t copy = new txn;
      |                         ^~~
%Error: t/t_class_new_ref_bad.v:26:21: Assign RHS expects a CLASSREFDTYPE 'Base', got BASICDTYPE 'int'
                                     : ... note: In instance 't'
   26 |       Base b = Cls::generate_txn();
      |                     ^~~~~~~~~~~~
%Error: Exiting due to
