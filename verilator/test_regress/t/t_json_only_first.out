{"type": "NETLIST", "name": "$root", "addr": "(B)", "loc": "a,0:0,0:0", "timeunit": "1ps", "timeprecision": "1ps", "typeTablep": "(C)", "constPoolp": "(D)", "dollarUnitPkgp": "UNLINKED", "stdPackagep": "UNLINKED", "evalp": "UNLINKED", "evalNbap": "UNLINKED", "dpiExportTriggerp": "UNLINKED", "delaySchedulerp": "UNLINKED", "nbaEventp": "UNLINKED", "nbaEventTriggerp": "UNLINKED", "topScopep": "UNLINKED", "modulesp": [{"type": "MODULE", "name": "t", "addr": "(E)", "loc": "d,7:8,7:9", "origName": "t", "level": 2, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "q", "addr": "(F)", "loc": "d,15:22,15:23", "dtypep": "(G)", "origName": "q", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "WIRE", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "clk", "addr": "(H)", "loc": "d,13:10,13:13", "dtypep": "(I)", "origName": "clk", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "d", "addr": "(J)", "loc": "d,14:16,14:17", "dtypep": "(G)", "origName": "d", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "between", "addr": "(K)", "loc": "d,17:22,17:29", "dtypep": "(G)", "origName": "between", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CELL", "name": "cell1", "addr": "(L)", "loc": "d,20:4,20:9", "origName": "cell1", "recursive": false, "modp": "(M)", "pinsp": [{"type": "PIN", "name": "q", "addr": "(N)", "loc": "d,20:12,20:13", "svDotName": true, "svImplicit": false, "modVarp": "(O)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "between", "addr": "(P)", "loc": "d,20:14,20:21", "dtypep": "(G)", "access": "WR", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "PIN", "name": "clk", "addr": "(Q)", "loc": "d,21:12,21:15", "svDotName": true, "svImplicit": false, "modVarp": "(R)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "clk", "addr": "(S)", "loc": "d,21:42,21:45", "dtypep": "(I)", "access": "RD", "varp": "(H)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "PIN", "name": "d", "addr": "(T)", "loc": "d,22:12,22:13", "svDotName": true, "svImplicit": false, "modVarp": "(U)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "d", "addr": "(V)", "loc": "d,22:42,22:43", "dtypep": "(G)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "paramsp": [], "rangep": [], "intfRefsp": []}, {"type": "CELL", "name": "cell2", "addr": "(W)", "loc": "d,25:6,25:11", "origName": "cell2", "recursive": false, "modp": "(X)", "pinsp": [{"type": "PIN", "name": "d", "addr": "(Y)", "loc": "d,25:14,25:15", "svDotName": true, "svImplicit": false, "modVarp": "(Z)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "between", "addr": "(AB)", "loc": "d,25:16,25:23", "dtypep": "(G)", "access": "RD", "varp": "(K)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "PIN", "name": "q", "addr": "(BB)", "loc": "d,26:14,26:15", "svDotName": true, "svImplicit": false, "modVarp": "(CB)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "q", "addr": "(DB)", "loc": "d,26:42,26:43", "dtypep": "(G)", "access": "WR", "varp": "(F)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "PIN", "name": "clk", "addr": "(EB)", "loc": "d,27:14,27:17", "svDotName": true, "svImplicit": false, "modVarp": "(FB)", "modPTypep": "UNLINKED", "exprp": [{"type": "VARREF", "name": "clk", "addr": "(GB)", "loc": "d,27:42,27:45", "dtypep": "(I)", "access": "RD", "varp": "(H)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "paramsp": [], "rangep": [], "intfRefsp": []}], "activesp": []}, {"type": "MODULE", "name": "mod2", "addr": "(X)", "loc": "d,46:8,46:12", "origName": "mod2", "level": 3, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "clk", "addr": "(FB)", "loc": "d,48:10,48:13", "dtypep": "(I)", "origName": "clk", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "d", "addr": "(Z)", "loc": "d,49:16,49:17", "dtypep": "(G)", "origName": "d", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "q", "addr": "(CB)", "loc": "d,50:22,50:23", "dtypep": "(G)", "origName": "q", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "WIRE", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "ASSIGNW", "name": "", "addr": "(HB)", "loc": "d,53:13,53:14", "dtypep": "(G)", "rhsp": [{"type": "VARREF", "name": "d", "addr": "(IB)", "loc": "d,49:16,49:17", "dtypep": "(G)", "access": "RD", "varp": "(Z)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "q", "addr": "(JB)", "loc": "d,53:13,53:14", "dtypep": "(G)", "access": "WR", "varp": "(CB)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": [], "strengthSpecp": []}], "activesp": []}, {"type": "MODULE", "name": "mod1__W4", "addr": "(M)", "loc": "d,31:8,31:12", "origName": "mod1", "level": 3, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "WIDTH", "addr": "(KB)", "loc": "d,32:15,32:20", "dtypep": "(LB)", "origName": "WIDTH", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "GPARAM", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": true, "isParam": true, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [{"type": "CONST", "name": "32'sh4", "addr": "(MB)", "loc": "d,19:18,19:19", "dtypep": "(LB)"}], "attrsp": []}, {"type": "VAR", "name": "clk", "addr": "(R)", "loc": "d,34:24,34:27", "dtypep": "(I)", "origName": "clk", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "d", "addr": "(U)", "loc": "d,35:30,35:31", "dtypep": "(G)", "origName": "d", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "q", "addr": "(O)", "loc": "d,36:30,36:31", "dtypep": "(G)", "origName": "q", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "IGNORED", "addr": "(NB)", "loc": "d,39:15,39:22", "dtypep": "(LB)", "origName": "IGNORED", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "LPARAM", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": true, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [{"type": "CONST", "name": "32'sh1", "addr": "(OB)", "loc": "d,39:25,39:26", "dtypep": "(LB)"}], "attrsp": []}, {"type": "ALWAYS", "name": "", "addr": "(PB)", "loc": "d,41:4,41:10", "keyword": "always", "isSuspendable": false, "needProcess": false, "sensesp": [{"type": "SENTREE", "name": "", "addr": "(QB)", "loc": "d,41:11,41:12", "isMulti": false, "sensesp": [{"type": "SENITEM", "name": "", "addr": "(RB)", "loc": "d,41:13,41:20", "edgeType": "POS", "sensp": [{"type": "VARREF", "name": "clk", "addr": "(SB)", "loc": "d,41:21,41:24", "dtypep": "(I)", "access": "RD", "varp": "(R)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "condp": []}]}], "stmtsp": [{"type": "ASSIGNDLY", "name": "", "addr": "(TB)", "loc": "d,42:8,42:10", "dtypep": "(G)", "rhsp": [{"type": "VARREF", "name": "d", "addr": "(UB)", "loc": "d,42:11,42:12", "dtypep": "(G)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "q", "addr": "(VB)", "loc": "d,42:6,42:7", "dtypep": "(G)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}]}], "activesp": []}], "filesp": [], "miscsp": [{"type": "TYPETABLE", "name": "", "addr": "(C)", "loc": "a,0:0,0:0", "constraintRefp": "UNLINKED", "emptyQueuep": "UNLINKED", "queueIndexp": "UNLINKED", "streamp": "UNLINKED", "voidp": "UNLINKED", "typesp": [{"type": "BASICDTYPE", "name": "logic", "addr": "(I)", "loc": "d,34:24,34:27", "dtypep": "(I)", "keyword": "logic", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(G)", "loc": "d,15:16,15:17", "dtypep": "(G)", "keyword": "logic", "range": "3:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(LB)", "loc": "d,19:18,19:19", "dtypep": "(LB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}]}, {"type": "CONSTPOOL", "name": "", "addr": "(D)", "loc": "a,0:0,0:0", "modulep": [{"type": "MODULE", "name": "@CONST-POOL@", "addr": "(WB)", "loc": "a,0:0,0:0", "origName": "@CONST-POOL@", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "NONE", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "@CONST-POOL@", "addr": "(XB)", "loc": "a,0:0,0:0", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(WB)", "varsp": [], "blocksp": [], "inlinesp": []}], "activesp": []}]}]}