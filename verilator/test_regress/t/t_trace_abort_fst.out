$date
	Wed Feb 23 00:00:18 2022

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$var wire 1 ! clk $end
$scope module t $end
$var wire 1 ! clk $end
$var logic 3 " cyc [2:0] $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b000 "
0!
$end
#10
1!
b001 "
#15
0!
#20
1!
b010 "
#25
0!
#30
1!
b011 "
#35
0!
#40
1!
b100 "
#45
0!
#50
1!
b101 "
#55
0!
#60
1!
b110 "
#65
0!
#70
1!
b111 "
#75
0!
