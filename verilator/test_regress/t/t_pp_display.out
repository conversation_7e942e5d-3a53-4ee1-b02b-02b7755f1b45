pre thrupre thrumid thrupost post: "right side"
left side: "right side"
left side: "right side"
left_side: "right_side"
na: "right_side"
prep ( midp1 left_side midp2 ( outp ) ): "right_side"
na: "nana"
left_side right_side: "left_side right_side"
left side: "right side"
: ""
left side: "right side"
left side: "right side"
standalone
twoline: "first   second"
Line 50 File "t/t_pp_display.v"
*-* All Finished *-*
