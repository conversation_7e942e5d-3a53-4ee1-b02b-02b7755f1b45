%Warning-CASEOVERLAP: t/t_priority_case.v:34:7: Case item ignored: every matching value is covered by an earlier condition
   34 |       2'b ?1: out1 = 3'd1;
      |       ^~~~~~
                      t/t_priority_case.v:33:7: ... Location of previous condition
   33 |       2'b ?1: out1 = 3'd0;
      |       ^~~~~~
                      ... For warning description see https://verilator.org/warn/CASEOVERLAP?v=latest
                      ... Use "/* verilator lint_off CASEOVERLAP */" and lint_on around source to disable this message.
%Warning-CASEOVERLAP: t/t_priority_case.v:44:7: Case item ignored: every matching value is covered by an earlier condition
   44 |       2'b ?1: out1 = 3'd1;
      |       ^~~~~~
                      t/t_priority_case.v:43:7: ... Location of previous condition
   43 |       2'b ?1: out1 = 3'd0;
      |       ^~~~~~
%Error: Exiting due to
