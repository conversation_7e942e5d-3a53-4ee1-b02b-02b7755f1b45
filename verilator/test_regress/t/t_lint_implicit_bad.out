%Warning-IMPLICIT: t/t_lint_implicit.v:11:11: Signal definition not found, creating implicitly: 'b'
   11 |    assign b = 1'b1;
      |           ^
                   ... For warning description see https://verilator.org/warn/IMPLICIT?v=latest
                   ... Use "/* verilator lint_off IMPLICIT */" and lint_on around source to disable this message.
%Warning-IMPLICIT: t/t_lint_implicit.v:13:14: Signal definition not found, creating implicitly: 'nt0'
   13 |    or   OR0 (nt0, a, b);
      |              ^~~
%Warning-IMPLICIT: t/t_lint_implicit.v:16:12: Signal definition not found, creating implicitly: 'dummy1'
                                            : ... Suggested alternative: 'dummy_ip'
   16 |    assign {dummy1, dummy2} = dummy_ip;
      |            ^~~~~~
%Warning-IMPLICIT: t/t_lint_implicit.v:16:20: Signal definition not found, creating implicitly: 'dummy2'
                                            : ... Suggested alternative: 'dummy1'
   16 |    assign {dummy1, dummy2} = dummy_ip;
      |                    ^~~~~~
%Error: Exiting due to
