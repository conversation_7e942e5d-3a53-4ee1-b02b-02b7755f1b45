%Warning-CMPCONST: t/t_lint_cmpconst_bad.v:13:15: Comparison is constant due to limited range
                                                : ... note: In instance 't'
   13 |       if (uns > 3'b111) $stop;
      |               ^
                   ... For warning description see https://verilator.org/warn/CMPCONST?v=latest
                   ... Use "/* verilator lint_off CMPCONST */" and lint_on around source to disable this message.
%Error: Exiting due to
