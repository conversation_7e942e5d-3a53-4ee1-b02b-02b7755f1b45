// -*- mode: C++; c-file-style: "cc-mode" -*-
//*************************************************************************
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2023 by Shupei Fan.
// SPDX-License-Identifier: CC0-1.0
//
//*************************************************************************

#include "Vt_flag_lib_dpi__Dpi.h"
#include "svdpi.h"

#include <iostream>
void write_all_finished() { std::cout << "*-* All Finished *-*" << std::endl; }
