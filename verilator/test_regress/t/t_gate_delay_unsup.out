%Warning-RISEFALLDLY: t/t_gate_basic.v:26:12: Unsupported: rising/falling/turn-off delays. Using the first delay
   26 |    nand  #(2,3)   ND0 (nd0, a[0], b[0], b[1]);
      |            ^
                      ... For warning description see https://verilator.org/warn/RISEFALLDLY?v=latest
                      ... Use "/* verilator lint_off RISEFALLDLY */" and lint_on around source to disable this message.
%Error: Exiting due to
