$version Generated by VerilatedVcd $end
$timescale 1ps $end

 $scope module top $end
  $scope module $unit $end
   $var wire 32 + ID_MSB [31:0] $end
  $upscope $end
  $var wire 1 # clk $end
  $var wire 1 $ res $end
  $var wire 8 % res8 [7:0] $end
  $var wire 16 & res16 [15:0] $end
  $scope module t $end
   $var wire 1 # clk $end
   $var wire 1 $ res $end
   $var wire 8 % res8 [7:0] $end
   $var wire 16 & res16 [15:0] $end
   $var wire 8 ' clkSet [7:0] $end
   $var wire 1 # clk_1 $end
   $var wire 3 ( clk_3 [2:0] $end
   $var wire 4 ) clk_4 [3:0] $end
   $var wire 1 # clk_final $end
   $var wire 8 * count [7:0] $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
0$
b00000000 %
b0000000000000000 &
b00000000 '
b000 (
b0000 )
b00000000 *
b00000000000000000000000000000001 +
#10
1#
1$
b11101111 %
b0000000111111111 &
b11111111 '
b111 (
b1111 )
b00000001 *
#15
0#
0$
b00000000 %
b0000001000000000 &
b00000000 '
b000 (
b0000 )
b00000010 *
#20
1#
1$
b11101111 %
b0000001111111111 &
b11111111 '
b111 (
b1111 )
b00000011 *
#25
0#
0$
b00000000 %
b0000010000000000 &
b00000000 '
b000 (
b0000 )
b00000100 *
#30
1#
1$
b11101111 %
b0000010111111111 &
b11111111 '
b111 (
b1111 )
b00000101 *
#35
0#
0$
b00000000 %
b0000011000000000 &
b00000000 '
b000 (
b0000 )
b00000110 *
#40
1#
1$
b11101111 %
b0000011111111111 &
b11111111 '
b111 (
b1111 )
b00000111 *
#45
0#
0$
b00000000 %
b0000100000000000 &
b00000000 '
b000 (
b0000 )
b00001000 *
#50
1#
1$
b11101111 %
b0000100111111111 &
b11111111 '
b111 (
b1111 )
b00001001 *
#55
0#
0$
b00000000 %
b0000101000000000 &
b00000000 '
b000 (
b0000 )
b00001010 *
#60
1#
1$
b11101111 %
b0000101111111111 &
b11111111 '
b111 (
b1111 )
b00001011 *
#65
0#
0$
b00000000 %
b0000110000000000 &
b00000000 '
b000 (
b0000 )
b00001100 *
#70
1#
1$
b11101111 %
b0000110111111111 &
b11111111 '
b111 (
b1111 )
b00001101 *
#75
0#
0$
b00000000 %
b0000111000000000 &
b00000000 '
b000 (
b0000 )
b00001110 *
#80
1#
1$
b11101111 %
b0000111111111111 &
b11111111 '
b111 (
b1111 )
b00001111 *
