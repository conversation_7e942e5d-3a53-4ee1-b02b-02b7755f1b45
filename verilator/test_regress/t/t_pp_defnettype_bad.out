%Error-UNSUPPORTED: t/t_pp_defnettype_bad.v:7:1: Unsupported: `default_nettype of other than none or wire: '`default_nettype bad'
    7 | `default_nettype bad_none_such
      | ^~~~~~~~~~~~~~~~~~~~
                    ... For error description see https://verilator.org/warn/UNSUPPORTED?v=latest
%Error-UNSUPPORTED: t/t_pp_defnettype_bad.v:9:1: Unsupported: Verilog optional directive not implemented: '`default_trireg_strength this_is_optional'
    9 | `default_trireg_strength this_is_optional
      | ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
%Error: t/t_pp_defnettype_bad.v:7:21: syntax error, unexpected IDENTIFIER
    7 | `default_nettype bad_none_such
      |                     ^~~~~~~~~~
%Error: Exiting due to
