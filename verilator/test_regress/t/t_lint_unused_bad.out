%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:17:15: Bits of signal are not used: 'assunu1'[5:1]
                                                  : ... note: In instance 't.sub'
   17 |    wire [5:0] assunu1 = 0;   
      |               ^~~~~~~
                       ... For warning description see https://verilator.org/warn/UNUSEDSIGNAL?v=latest
                       ... Use "/* verilator lint_off UNUSEDSIGNAL */" and lint_on around source to disable this message.
%Warning-UNDRIVEN: t/t_lint_unused_bad.v:21:17: Bits of signal are not driven: 'udrb2'[14:13,11]
                                              : ... note: In instance 't.sub'
   21 |    wire [15:10] udrb2;   
      |                 ^~~~~
%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:26:15: Signal is not driven, nor used: 'unu3'
                                                  : ... note: In instance 't.sub'
   26 |    wire       unu3;   
      |               ^~~~
%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:28:15: Bits of signal are not driven, nor used: 'mixed'[3]
                                                  : ... note: In instance 't.sub'
   28 |    wire [3:0] mixed;   
      |               ^~~~~
%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:28:15: Bits of signal are not used: 'mixed'[2]
                                                  : ... note: In instance 't.sub'
   28 |    wire [3:0] mixed;   
      |               ^~~~~
%Warning-UNDRIVEN: t/t_lint_unused_bad.v:28:15: Bits of signal are not driven: 'mixed'[1]
                                              : ... note: In instance 't.sub'
   28 |    wire [3:0] mixed;   
      |               ^~~~~
%Warning-UNUSEDPARAM: t/t_lint_unused_bad.v:37:14: Parameter is not used: 'UNUSED_P'
                                                 : ... note: In instance 't.sub'
   37 |    parameter UNUSED_P = 1;
      |              ^~~~~~~~
%Warning-UNUSEDPARAM: t/t_lint_unused_bad.v:38:15: Parameter is not used: 'UNUSED_LP'
                                                 : ... note: In instance 't.sub'
   38 |    localparam UNUSED_LP = 2;
      |               ^~~~~~~~~
%Warning-UNUSEDGENVAR: t/t_lint_unused_bad.v:40:15: Genvar is not used: 'unused_gv'
                                                  : ... note: In instance 't.sub'
   40 |    genvar     unused_gv;
      |               ^~~~~~~~~
%Warning-UNUSEDPARAM: t/t_lint_unused_bad.v:45:15: Parameter is not used: 'linter_param1'
                                                 : ... note: In instance 't.sub'
   45 |    localparam linter_param1 = 1;
      |               ^~~~~~~~~~~~~
%Warning-UNUSEDGENVAR: t/t_lint_unused_bad.v:46:11: Genvar is not used: 'linter_genvar1'
                                                  : ... note: In instance 't.sub'
   46 |    genvar linter_genvar1;
      |           ^~~~~~~~~~~~~~
%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:50:9: Signal is not driven, nor used: 'linter_sig2'
                                                 : ... note: In instance 't.sub'
   50 |    wire linter_sig2;
      |         ^~~~~~~~~~~
%Warning-UNUSEDGENVAR: t/t_lint_unused_bad.v:52:11: Genvar is not used: 'linter_genvar2'
                                                  : ... note: In instance 't.sub'
   52 |    genvar linter_genvar2;
      |           ^~~~~~~~~~~~~~
%Warning-UNUSEDSIGNAL: t/t_lint_unused_bad.v:56:9: Signal is not driven, nor used: 'linter_sig3'
                                                 : ... note: In instance 't.sub'
   56 |    wire linter_sig3;
      |         ^~~~~~~~~~~
%Warning-UNUSEDPARAM: t/t_lint_unused_bad.v:57:15: Parameter is not used: 'linter_param3'
                                                 : ... note: In instance 't.sub'
   57 |    localparam linter_param3 = 3;
      |               ^~~~~~~~~~~~~
%Error: Exiting due to
