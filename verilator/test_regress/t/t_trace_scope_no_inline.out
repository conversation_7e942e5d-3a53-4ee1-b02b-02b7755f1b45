$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module top $end
  $scope module t $end
   $scope module mid_a $end
   $upscope $end
   $scope module mid_b $end
    $var wire 1 ' clk $end
    $var wire 32 # cnt [31:0] $end
    $scope module sub_a $end
     $var wire 1 ' clk $end
     $var wire 32 $ cnt [31:0] $end
    $upscope $end
    $scope module sub_b $end
     $var wire 1 ' clk $end
     $var wire 32 % cnt [31:0] $end
    $upscope $end
    $scope module sub_c $end
     $var wire 1 ' clk $end
     $var wire 32 & cnt [31:0] $end
    $upscope $end
   $upscope $end
   $scope module mid_c $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
b00000000000000000000000000000000 #
b00000000000000000000000000000000 $
b00000000000000000000000000000000 %
b00000000000000000000000000000000 &
0'
#10
b00000000000000000000000000000001 #
b00000000000000000000000000000010 $
b00000000000000000000000000000010 %
b00000000000000000000000000000010 &
1'
#15
0'
#20
b00000000000000000000000000000010 #
b00000000000000000000000000000100 $
b00000000000000000000000000000100 %
b00000000000000000000000000000100 &
1'
#25
0'
#30
b00000000000000000000000000000011 #
b00000000000000000000000000000110 $
b00000000000000000000000000000110 %
b00000000000000000000000000000110 &
1'
#35
0'
#40
b00000000000000000000000000000100 #
b00000000000000000000000000001000 $
b00000000000000000000000000001000 %
b00000000000000000000000000001000 &
1'
#45
0'
#50
b00000000000000000000000000000101 #
b00000000000000000000000000001010 $
b00000000000000000000000000001010 %
b00000000000000000000000000001010 &
1'
#55
0'
#60
b00000000000000000000000000000110 #
b00000000000000000000000000001100 $
b00000000000000000000000000001100 %
b00000000000000000000000000001100 &
1'
