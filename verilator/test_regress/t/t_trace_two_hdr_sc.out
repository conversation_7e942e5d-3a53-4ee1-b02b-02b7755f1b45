$version Generated by VerilatedVcd $end
$timescale 1ps $end

 $scope module topa $end
  $scope module t $end
   $var wire 1 # clk $end
   $var wire 32 $ cyc [31:0] $end
   $var wire 32 % c_trace_on [31:0] $end
   $scope module sub $end
    $var wire 32 & inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
 $scope module topb $end
  $scope module t $end
   $var wire 1 ( clk $end
   $var wire 32 + cyc [31:0] $end
   $var wire 32 , c_trace_on [31:0] $end
   $var real 64 ) r $end
   $scope module sub $end
    $var wire 32 - inside_sub_a [31:0] $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
0#
b00000000000000000000000000000001 $
b00000000000000000000000000000000 %
0(
r0 )
b00000000000000000000000000000001 &
b00000000000000000000000000000001 +
b00000000000000000000000000000000 ,
b00000000000000000000000000000010 -
#10000
1#
b00000000000000000000000000000010 $
b00000000000000000000000000000011 %
1(
r0.1 )
#15000
0#
0(
#20000
1#
b00000000000000000000000000000011 $
b00000000000000000000000000000100 %
1(
r0.2 )
#25000
0#
0(
#30000
1#
b00000000000000000000000000000100 $
b00000000000000000000000000000101 %
1(
r0.3 )
#35000
0#
0(
#40000
1#
b00000000000000000000000000000101 $
b00000000000000000000000000000110 %
1(
r0.4 )
#45000
0#
0(
#50000
1#
b00000000000000000000000000000110 $
b00000000000000000000000000000111 %
1(
r0.5 )
#55000
0#
0(
#60000
1#
b00000000000000000000000000000111 $
b00000000000000000000000000001000 %
1(
r0.6 )
#65000
0#
0(
#70000
1#
b00000000000000000000000000001000 $
b00000000000000000000000000001001 %
1(
r0.7 )
#75000
0#
0(
#80000
1#
b00000000000000000000000000001001 $
b00000000000000000000000000001010 %
1(
r0.7999999999999999 )
#85000
0#
0(
#90000
1#
b00000000000000000000000000001010 $
b00000000000000000000000000001011 %
1(
r0.8999999999999999 )
#95000
0#
0(
#100000
1#
b00000000000000000000000000001011 $
b00000000000000000000000000001100 %
1(
r0.9999999999999999 )
