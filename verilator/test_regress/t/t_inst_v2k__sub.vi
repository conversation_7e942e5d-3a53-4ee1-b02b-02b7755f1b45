// -*- Verilog -*-
// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2003 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

// This file is named .vi to test +libext+ flags.
module t_inst_v2k__sub
  (
   output reg [7:0] osizedreg,
   output wire oonewire /*verilator public*/,
   input [7:0] isizedwire,
   input wire ionewire,
   output reg [1:0] tied = 2'b10
   );

   assign oonewire = ionewire;

   always @* begin
      osizedreg = isizedwire;
   end

endmodule
