// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Prototypes for DPI import and export functions.
//
// Verilator includes this file in all generated .cpp files that use DPI functions.
// Manually include this file where DPI .c import functions are declared to ensure
// the C functions match the expectations of the DPI imports.

#ifndef VERILATED_VT_DPI_ARG_INOUT_TYPE__DPI_H_
#define VERILATED_VT_DPI_ARG_INOUT_TYPE__DPI_H_  // guard

#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif


// DPI EXPORTS
extern void e_array_2_state_1(svBitVecVal* x);
extern void e_array_2_state_128(svBitVecVal* x);
extern void e_array_2_state_32(svBitVecVal* x);
extern void e_array_2_state_33(svBitVecVal* x);
extern void e_array_2_state_64(svBitVecVal* x);
extern void e_array_2_state_65(svBitVecVal* x);
extern void e_array_4_state_1(svLogicVecVal* x);
extern void e_array_4_state_128(svLogicVecVal* x);
extern void e_array_4_state_32(svLogicVecVal* x);
extern void e_array_4_state_33(svLogicVecVal* x);
extern void e_array_4_state_64(svLogicVecVal* x);
extern void e_array_4_state_65(svLogicVecVal* x);
extern void e_bit(svBit* x);
extern void e_bit_t(svBit* x);
extern void e_byte(char* x);
extern void e_byte_t(char* x);
extern void e_byte_unsigned(unsigned char* x);
extern void e_byte_unsigned_t(unsigned char* x);
extern void e_chandle(void** x);
extern void e_chandle_t(void** x);
extern void e_int(int* x);
extern void e_int_t(int* x);
extern void e_int_unsigned(unsigned int* x);
extern void e_int_unsigned_t(unsigned int* x);
extern void e_integer(svLogicVecVal* x);
extern void e_integer_t(svLogicVecVal* x);
extern void e_logic(svLogic* x);
extern void e_logic_t(svLogic* x);
extern void e_longint(long long* x);
extern void e_longint_t(long long* x);
extern void e_longint_unsigned(unsigned long long* x);
extern void e_longint_unsigned_t(unsigned long long* x);
extern void e_real(double* x);
extern void e_real_t(double* x);
extern void e_shortint(short* x);
extern void e_shortint_t(short* x);
extern void e_shortint_unsigned(unsigned short* x);
extern void e_shortint_unsigned_t(unsigned short* x);
extern void e_string(const char** x);
extern void e_string_t(const char** x);
extern void e_struct_2_state_1(svBitVecVal* x);
extern void e_struct_2_state_128(svBitVecVal* x);
extern void e_struct_2_state_32(svBitVecVal* x);
extern void e_struct_2_state_33(svBitVecVal* x);
extern void e_struct_2_state_64(svBitVecVal* x);
extern void e_struct_2_state_65(svBitVecVal* x);
extern void e_struct_4_state_1(svLogicVecVal* x);
extern void e_struct_4_state_128(svLogicVecVal* x);
extern void e_struct_4_state_32(svLogicVecVal* x);
extern void e_struct_4_state_33(svLogicVecVal* x);
extern void e_struct_4_state_64(svLogicVecVal* x);
extern void e_struct_4_state_65(svLogicVecVal* x);
extern void e_time(svLogicVecVal* x);
extern void e_time_t(svLogicVecVal* x);
extern void e_union_2_state_1(svBitVecVal* x);
extern void e_union_2_state_128(svBitVecVal* x);
extern void e_union_2_state_32(svBitVecVal* x);
extern void e_union_2_state_33(svBitVecVal* x);
extern void e_union_2_state_64(svBitVecVal* x);
extern void e_union_2_state_65(svBitVecVal* x);
extern void e_union_4_state_1(svLogicVecVal* x);
extern void e_union_4_state_128(svLogicVecVal* x);
extern void e_union_4_state_32(svLogicVecVal* x);
extern void e_union_4_state_33(svLogicVecVal* x);
extern void e_union_4_state_64(svLogicVecVal* x);
extern void e_union_4_state_65(svLogicVecVal* x);

// DPI IMPORTS
extern void check_exports();
extern void i_array_2_state_1(svBitVecVal* x);
extern void i_array_2_state_128(svBitVecVal* x);
extern void i_array_2_state_32(svBitVecVal* x);
extern void i_array_2_state_33(svBitVecVal* x);
extern void i_array_2_state_64(svBitVecVal* x);
extern void i_array_2_state_65(svBitVecVal* x);
extern void i_array_4_state_1(svLogicVecVal* x);
extern void i_array_4_state_128(svLogicVecVal* x);
extern void i_array_4_state_32(svLogicVecVal* x);
extern void i_array_4_state_33(svLogicVecVal* x);
extern void i_array_4_state_64(svLogicVecVal* x);
extern void i_array_4_state_65(svLogicVecVal* x);
extern void i_bit(svBit* x);
extern void i_bit_t(svBit* x);
extern void i_byte(char* x);
extern void i_byte_t(char* x);
extern void i_byte_unsigned(unsigned char* x);
extern void i_byte_unsigned_t(unsigned char* x);
extern void i_chandle(void** x);
extern void i_chandle_t(void** x);
extern void i_int(int* x);
extern void i_int_t(int* x);
extern void i_int_unsigned(unsigned int* x);
extern void i_int_unsigned_t(unsigned int* x);
extern void i_integer(svLogicVecVal* x);
extern void i_integer_t(svLogicVecVal* x);
extern void i_logic(svLogic* x);
extern void i_logic_t(svLogic* x);
extern void i_longint(long long* x);
extern void i_longint_t(long long* x);
extern void i_longint_unsigned(unsigned long long* x);
extern void i_longint_unsigned_t(unsigned long long* x);
extern void i_real(double* x);
extern void i_real_t(double* x);
extern void i_shortint(short* x);
extern void i_shortint_t(short* x);
extern void i_shortint_unsigned(unsigned short* x);
extern void i_shortint_unsigned_t(unsigned short* x);
extern void i_string(const char** x);
extern void i_string_t(const char** x);
extern void i_struct_2_state_1(svBitVecVal* x);
extern void i_struct_2_state_128(svBitVecVal* x);
extern void i_struct_2_state_32(svBitVecVal* x);
extern void i_struct_2_state_33(svBitVecVal* x);
extern void i_struct_2_state_64(svBitVecVal* x);
extern void i_struct_2_state_65(svBitVecVal* x);
extern void i_struct_4_state_1(svLogicVecVal* x);
extern void i_struct_4_state_128(svLogicVecVal* x);
extern void i_struct_4_state_32(svLogicVecVal* x);
extern void i_struct_4_state_33(svLogicVecVal* x);
extern void i_struct_4_state_64(svLogicVecVal* x);
extern void i_struct_4_state_65(svLogicVecVal* x);
extern void i_time(svLogicVecVal* x);
extern void i_time_t(svLogicVecVal* x);
extern void i_union_2_state_1(svBitVecVal* x);
extern void i_union_2_state_128(svBitVecVal* x);
extern void i_union_2_state_32(svBitVecVal* x);
extern void i_union_2_state_33(svBitVecVal* x);
extern void i_union_2_state_64(svBitVecVal* x);
extern void i_union_2_state_65(svBitVecVal* x);
extern void i_union_4_state_1(svLogicVecVal* x);
extern void i_union_4_state_128(svLogicVecVal* x);
extern void i_union_4_state_32(svLogicVecVal* x);
extern void i_union_4_state_33(svLogicVecVal* x);
extern void i_union_4_state_64(svLogicVecVal* x);
extern void i_union_4_state_65(svLogicVecVal* x);

#ifdef __cplusplus
}
#endif

#endif  // guard
