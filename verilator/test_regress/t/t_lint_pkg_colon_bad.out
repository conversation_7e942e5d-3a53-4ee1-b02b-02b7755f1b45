%Error-PKGNODECL: t/t_lint_pkg_colon_bad.v:7:17: Package/class 'mispkg' not found, and needs to be predeclared (IEEE 1800-2023 26.3)
    7 | module t (input mispkg::foo_t a);
      |                 ^~~~~~
                  ... For error description see https://verilator.org/warn/PKGNODECL?v=latest
%Error: t/t_lint_pkg_colon_bad.v:7:25: syntax error, unexpected IDENTIFIER, expecting TYPE-IDENTIFIER
    7 | module t (input mispkg::foo_t a);
      |                         ^~~~~
%Error: Exiting due to
