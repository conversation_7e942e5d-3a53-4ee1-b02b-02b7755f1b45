Verilator Tree Dump (format 0x3900) from <e0> to <e663>
     NETLIST 0x55d6994da000 <e1#> {a0aa}  $root [1ps/1ps]
    1: MODULE 0x55d6994e4120 <e661#> {d19ai}  t  L2 [1ps]
    1:2: PORT 0x55d6994ea0d0 <e8#> {d21ae}  clk
    1:2: VAR 0x55d6994e2180 <e572#> {d23ak} @dt=0@  clkmod INPUT PORT
    1:2:1: BASICDTYPE 0x55d6994ea1a0 <e12#> {d23ak} @dt=this@(nw0)  LOGIC_IMPLICIT kwd=LOGIC_IMPLICIT
    3: TYPETABLE 0x55d6994e0000 <e2#> {a0aa}
                   logic  -> BASICDTYPE 0x55d699595a00 <e426#> {d55ap} @dt=this@(G/nw1)  logic [GENERIC] kwd=logic
    3: CONSTPOOL 0x55d6994e2000 <e6#> {a0aa}
    3:1: MODU<PERSON> 0x55d6994e4000 <e4#> {a0aa}  @CONST-POOL@  L0 [NONE]
    3:1:2: SCOPE 0x55d6994da0f0 <e5#> {a0aa}  @CONST-POOL@ [abovep=0] [cellp=0] [modp=0x55d6994e4000]
