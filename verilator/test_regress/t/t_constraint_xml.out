<?xml version="1.0" ?>
<!-- DESCRIPTION: Verilator output: XML representation of netlist -->
<verilator_xml>
  <files>
    <file id="a" filename="&lt;built-in&gt;" language="1800-2023"/>
    <file id="b" filename="&lt;command-line&gt;" language="1800-2023"/>
    <file id="c" filename="input.vc" language="1800-2023"/>
    <file id="d" filename="t/t_constraint_xml.v" language="1800-2023"/>
  </files>
  <module_files>
    <file id="d" filename="t/t_constraint_xml.v" language="1800-2023"/>
  </module_files>
  <cells>
    <cell loc="d,67,8,67,9" name="t" submodname="t" hier="t"/>
  </cells>
  <netlist>
    <module loc="d,67,8,67,9" name="t" origName="t">
      <var loc="d,69,11,69,12" name="p" dtype_id="1" vartype="Packet" origName="p"/>
      <initial loc="d,71,4,71,11">
        <begin loc="d,71,12,71,17">
          <display loc="d,73,7,73,13" displaytype="$write">
            <sformatf loc="d,73,7,73,13" name="*-* All Finished *-*&#10;" dtype_id="2"/>
          </display>
          <finish loc="d,74,7,74,14"/>
        </begin>
      </initial>
    </module>
    <package loc="a,0,0,0,0" name="$unit" origName="__024unit">
      <class loc="d,7,1,7,6" name="Packet" origName="Packet">
        <var loc="d,8,13,8,19" name="header" dtype_id="3" vartype="int" origName="header"/>
        <var loc="d,9,13,9,19" name="length" dtype_id="3" vartype="int" origName="length"/>
        <var loc="d,10,13,10,22" name="sublength" dtype_id="3" vartype="int" origName="sublength"/>
        <var loc="d,11,13,11,17" name="if_4" dtype_id="4" vartype="bit" origName="if_4"/>
        <var loc="d,12,13,12,20" name="iff_5_6" dtype_id="4" vartype="bit" origName="iff_5_6"/>
        <var loc="d,13,13,13,24" name="if_state_ok" dtype_id="4" vartype="bit" origName="if_state_ok"/>
        <var loc="d,15,13,15,18" name="array" dtype_id="5" vartype="" origName="array"/>
        <var loc="d,17,11,17,16" name="state" dtype_id="2" vartype="string" origName="state"/>
        <func loc="d,61,17,61,30" name="strings_equal" dtype_id="4">
          <var loc="d,61,17,61,30" name="strings_equal" dtype_id="4" dir="output" vartype="bit" origName="strings_equal"/>
          <var loc="d,61,38,61,39" name="a" dtype_id="2" dir="input" vartype="string" origName="a"/>
          <var loc="d,61,48,61,49" name="b" dtype_id="2" dir="input" vartype="string" origName="b"/>
          <assign loc="d,62,7,62,13" dtype_id="4">
            <eqn loc="d,62,16,62,18" dtype_id="6">
              <varref loc="d,62,14,62,15" name="a" dtype_id="2"/>
              <varref loc="d,62,19,62,20" name="b" dtype_id="2"/>
            </eqn>
            <varref loc="d,62,7,62,13" name="strings_equal" dtype_id="4"/>
          </assign>
        </func>
        <func loc="d,7,1,7,6" name="new" dtype_id="7"/>
        <var loc="d,7,1,7,6" name="constraint" dtype_id="8" vartype="VlRandomizer" origName="constraint"/>
      </class>
    </package>
    <typetable loc="a,0,0,0,0">
      <basicdtype loc="d,22,14,22,15" id="6" name="logic"/>
      <basicdtype loc="d,25,21,25,22" id="9" name="logic" left="31" right="0"/>
      <basicdtype loc="d,73,7,73,13" id="2" name="string"/>
      <basicdtype loc="d,8,9,8,12" id="3" name="int" left="31" right="0" signed="true"/>
      <basicdtype loc="d,11,9,11,12" id="4" name="bit"/>
      <unpackarraydtype loc="d,15,18,15,19" id="5" sub_dtype_id="3">
        <range loc="d,15,18,15,19">
          <const loc="d,15,19,15,20" name="32&apos;h0" dtype_id="9"/>
          <const loc="d,15,19,15,20" name="32&apos;h1" dtype_id="9"/>
        </range>
      </unpackarraydtype>
      <voiddtype loc="d,7,1,7,6" id="7"/>
      <classrefdtype loc="d,69,4,69,10" id="1" name="Packet"/>
      <basicdtype loc="d,7,1,7,6" id="8" name="VlRandomizer"/>
    </typetable>
  </netlist>
</verilator_xml>
