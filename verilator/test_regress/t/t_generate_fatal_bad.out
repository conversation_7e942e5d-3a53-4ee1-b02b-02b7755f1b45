%Warning-USERFATAL: "boom"
                    ... For warning description see https://verilator.org/warn/USERFATAL?v=latest
                    ... Use "/* verilator lint_off USERFATAL */" and lint_on around source to disable this message.
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.nested_loop[10].foo2_inst.foo2_loop[1].foo_in_foo2_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = ?32?h0
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.genloop[1].foo_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = ?32?h1
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.gen_l1[2].gen_l2[0].foo_inst2'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h2
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.gen_l1[2].gen_l2[1].foo_inst2'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h4
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.gen_l1[3].gen_l2[0].foo_inst2'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h3
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.gen_l1[3].gen_l2[1].foo_inst2'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h5
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.cond_true.foo_inst3'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = ?32?h6
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.genblk4.foo_inst4'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = ?32?h7
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.nested_loop[8].foo2_inst.foo2_loop[0].foo_in_foo2_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h8
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.nested_loop[8].foo2_inst.foo2_loop[1].foo_in_foo2_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'h9
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.nested_loop[10].foo2_inst.foo2_loop[0].foo_in_foo2_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'ha
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: t/t_generate_fatal_bad.v:13:29: Expecting expression to be constant, but can't determine constant for FUNCREF 'get_baz'
                                      : ... note: In instance 't.nested_loop[10].foo2_inst.foo2_loop[1].foo_in_foo2_inst'
        t/t_generate_fatal_bad.v:9:4: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_generate_fatal_bad.v:13:29: ... Called from 'get_baz()' with parameters:
           bar = 32'hb
   13 |    localparam integer BAZ = get_baz(BAR);
      |                             ^~~~~~~
%Error: Exiting due to
