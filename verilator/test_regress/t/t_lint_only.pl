#!/usr/bin/env perl
if (!$::Driver) { use FindBin; exec("$FindBin::Bin/bootstrap.pl", @ARGV, $0); die; }
# DESCRIPTION: Verilator: Verilog Test driver/expect definition
#
# Copyright 2003-2009 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

scenarios(vlt => 1);

lint();

foreach my $file (glob("$Self->{obj_dir}/*")) {
    next if $file =~ /\.log/;  # Made by driver.pl, not Verilator sources
    next if $file =~ /\.status/;  # Made by driver.pl, not Verilator sources
    next if $file =~ /\.gcda/;  # Made by gcov, not Verilator sources
    error("%Error: Created $file, but --lint-only shouldn't create files");
}

ok(1);
1;
