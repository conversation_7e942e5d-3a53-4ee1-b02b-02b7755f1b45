%Warning-WIDTHEXPAND: t/t_inst_overwide.v:23:14: Output port connection 'outy_w92' expects 92 bits on the pin connection, but pin connection's VARREF 'outc_w30' generates 30 bits.
                                               : ... note: In instance 't'
   23 |             .outy_w92   (outc_w30),      
      |              ^~~~~~~~
                      ... For warning description see https://verilator.org/warn/WIDTHEXPAND?v=latest
                      ... Use "/* verilator lint_off WIDTHEXPAND */" and lint_on around source to disable this message.
%Warning-WIDTHTRUNC: t/t_inst_overwide.v:24:14: Output port connection 'outz_w22' expects 22 bits on the pin connection, but pin connection's VARREF 'outd_w73' generates 73 bits.
                                              : ... note: In instance 't'
   24 |             .outz_w22   (outd_w73),      
      |              ^~~~~~~~
%Warning-WIDTHEXPAND: t/t_inst_overwide.v:27:14: Input port connection 'inw_w31' expects 31 bits on the pin connection, but pin connection's VARREF 'ina_w1' generates 1 bits.
                                               : ... note: In instance 't'
   27 |             .inw_w31    (ina_w1),        
      |              ^~~~~~~
%Warning-WIDTHTRUNC: t/t_inst_overwide.v:28:14: Input port connection 'inx_w11' expects 11 bits on the pin connection, but pin connection's VARREF 'inb_w61' generates 61 bits.
                                              : ... note: In instance 't'
   28 |             .inx_w11    (inb_w61)        
      |              ^~~~~~~
%Error: Exiting due to
