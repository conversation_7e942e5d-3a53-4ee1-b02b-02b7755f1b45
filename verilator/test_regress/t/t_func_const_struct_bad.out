%Warning-USERFATAL: "f_add = 15"
                    ... For warning description see https://verilator.org/warn/USERFATAL?v=latest
                    ... Use "/* verilator lint_off USERFATAL */" and lint_on around source to disable this message.
%Error: t/t_func_const_struct_bad.v:17:21: Expecting expression to be constant, but can't determine constant for FUNCREF 'f_add2'
                                         : ... note: In instance 't'
        t/t_func_const_struct_bad.v:28:9: ... Location of non-constant STOP: $stop executed during function constification; maybe indicates assertion firing
        t/t_func_const_struct_bad.v:38:16: ... Called from 'f_add()' with parameters:
           params = '{a: 32'h7, b: 32'h8}
        t/t_func_const_struct_bad.v:17:21: ... Called from 'f_add2()' with parameters:
           a = ?32?h7
           b = ?32?h8
           c = ?32?h9
   17 |    localparam P24 = f_add2(7, 8, 9);
      |                     ^~~~~~
%Error: Exiting due to
