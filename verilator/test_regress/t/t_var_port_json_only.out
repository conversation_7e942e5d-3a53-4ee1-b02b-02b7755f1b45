{"type": "NETLIST", "name": "$root", "addr": "(B)", "loc": "a,0:0,0:0", "timeunit": "1ps", "timeprecision": "1ps", "typeTablep": "(C)", "constPoolp": "(D)", "dollarUnitPkgp": "UNLINKED", "stdPackagep": "UNLINKED", "evalp": "UNLINKED", "evalNbap": "UNLINKED", "dpiExportTriggerp": "UNLINKED", "delaySchedulerp": "UNLINKED", "nbaEventp": "UNLINKED", "nbaEventTriggerp": "UNLINKED", "topScopep": "UNLINKED", "modulesp": [{"type": "MODULE", "name": "mh2", "addr": "(E)", "loc": "d,18:8,18:11", "origName": "mh2", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_inout_wire_integer", "addr": "(F)", "loc": "d,18:27,18:47", "dtypep": "(G)", "origName": "x_inout_wire_integer", "isSc": false, "isPrimaryIO": false, "direction": "INOUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "integer", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh5", "addr": "(H)", "loc": "d,24:8,24:11", "origName": "mh5", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_input_wire_logic", "addr": "(I)", "loc": "d,24:19,24:37", "dtypep": "(J)", "origName": "x_input_wire_logic", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh6", "addr": "(K)", "loc": "d,26:8,26:11", "origName": "mh6", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_input_var_logic", "addr": "(L)", "loc": "d,26:23,26:40", "dtypep": "(J)", "origName": "x_input_var_logic", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh7", "addr": "(M)", "loc": "d,28:8,28:11", "origName": "mh7", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_input_var_integer", "addr": "(N)", "loc": "d,28:31,28:50", "dtypep": "(G)", "origName": "x_input_var_integer", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "integer", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh8", "addr": "(O)", "loc": "d,30:8,30:11", "origName": "mh8", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_wire_logic", "addr": "(P)", "loc": "d,30:20,30:39", "dtypep": "(J)", "origName": "x_output_wire_logic", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh9", "addr": "(Q)", "loc": "d,32:8,32:11", "origName": "mh9", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_var_logic", "addr": "(R)", "loc": "d,32:24,32:42", "dtypep": "(J)", "origName": "x_output_var_logic", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh10", "addr": "(S)", "loc": "d,34:8,34:12", "origName": "mh10", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_wire_logic_signed_p6", "addr": "(T)", "loc": "d,34:33,34:62", "dtypep": "(U)", "origName": "x_output_wire_logic_signed_p6", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh11", "addr": "(V)", "loc": "d,36:8,36:12", "origName": "mh11", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_var_integer", "addr": "(W)", "loc": "d,36:28,36:48", "dtypep": "(G)", "origName": "x_output_var_integer", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "integer", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh12", "addr": "(X)", "loc": "d,38:8,38:12", "origName": "mh12", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_ref_logic_p6", "addr": "(Y)", "loc": "d,38:23,38:37", "dtypep": "(Z)", "origName": "x_ref_logic_p6", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh13", "addr": "(AB)", "loc": "d,40:8,40:12", "origName": "mh13", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_ref_var_logic_u6", "addr": "(BB)", "loc": "d,40:17,40:35", "dtypep": "(CB)", "origName": "x_ref_var_logic_u6", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh17", "addr": "(DB)", "loc": "d,50:8,50:12", "origName": "mh17", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_input_var_integer", "addr": "(EB)", "loc": "d,50:31,50:50", "dtypep": "(G)", "origName": "x_input_var_integer", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "integer", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "y_input_wire_logic", "addr": "(FB)", "loc": "d,50:57,50:75", "dtypep": "(J)", "origName": "y_input_wire_logic", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "WIRE", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh18", "addr": "(GB)", "loc": "d,52:8,52:12", "origName": "mh18", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_var_logic", "addr": "(HB)", "loc": "d,52:24,52:42", "dtypep": "(J)", "origName": "x_output_var_logic", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "y_input_wire_logic", "addr": "(IB)", "loc": "d,52:50,52:68", "dtypep": "(J)", "origName": "y_input_wire_logic", "isSc": false, "isPrimaryIO": false, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh19", "addr": "(JB)", "loc": "d,54:8,54:12", "origName": "mh19", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_output_wire_logic_signed_p6", "addr": "(KB)", "loc": "d,54:33,54:62", "dtypep": "(U)", "origName": "x_output_wire_logic_signed_p6", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "y_output_var_integer", "addr": "(LB)", "loc": "d,54:72,54:92", "dtypep": "(G)", "origName": "y_output_var_integer", "isSc": false, "isPrimaryIO": false, "direction": "OUTPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "integer", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh20", "addr": "(MB)", "loc": "d,56:8,56:12", "origName": "mh20", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "x_ref_var_logic_p6", "addr": "(NB)", "loc": "d,56:23,56:41", "dtypep": "(Z)", "origName": "x_ref_var_logic_p6", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "y_ref_var_logic_p6", "addr": "(OB)", "loc": "d,56:43,56:61", "dtypep": "(Z)", "origName": "y_ref_var_logic_p6", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}, {"type": "MODULE", "name": "mh21", "addr": "(PB)", "loc": "d,58:8,58:12", "origName": "mh21", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "ref_var_logic_u6", "addr": "(QB)", "loc": "d,58:17,58:33", "dtypep": "(RB)", "origName": "ref_var_logic_u6", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "y_ref_var_logic", "addr": "(SB)", "loc": "d,58:41,58:56", "dtypep": "(J)", "origName": "y_ref_var_logic", "isSc": false, "isPrimaryIO": false, "direction": "REF", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "PORT", "dtypeName": "logic", "isSigUserRdPublic": false, "isSigUserRWPublic": false, "isGParam": false, "isParam": false, "attrScBv": false, "attrSFormat": false, "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "activesp": []}], "filesp": [], "miscsp": [{"type": "TYPETABLE", "name": "", "addr": "(C)", "loc": "a,0:0,0:0", "constraintRefp": "UNLINKED", "emptyQueuep": "UNLINKED", "queueIndexp": "UNLINKED", "streamp": "UNLINKED", "voidp": "UNLINKED", "typesp": [{"type": "UNPACKARRAYDTYPE", "name": "", "addr": "(RB)", "loc": "d,58:34,58:35", "dtypep": "(RB)", "isCompound": false, "declRange": "[5:0]", "generic": false, "refDTypep": "(J)", "childDTypep": [], "rangep": [{"type": "RANGE", "name": "", "addr": "(TB)", "loc": "d,58:34,58:35", "ascending": false, "leftp": [{"type": "CONST", "name": "32'sh5", "addr": "(UB)", "loc": "d,58:35,58:36", "dtypep": "(VB)"}], "rightp": [{"type": "CONST", "name": "32'sh0", "addr": "(WB)", "loc": "d,58:37,58:38", "dtypep": "(VB)"}]}]}, {"type": "BASICDTYPE", "name": "logic", "addr": "(J)", "loc": "d,58:41,58:56", "dtypep": "(J)", "keyword": "logic", "generic": true, "rangep": []}, {"type": "UNPACKARRAYDTYPE", "name": "", "addr": "(CB)", "loc": "d,40:36,40:37", "dtypep": "(CB)", "isCompound": false, "declRange": "[5:0]", "generic": false, "refDTypep": "(J)", "childDTypep": [], "rangep": [{"type": "RANGE", "name": "", "addr": "(XB)", "loc": "d,40:36,40:37", "ascending": false, "leftp": [{"type": "CONST", "name": "32'sh5", "addr": "(YB)", "loc": "d,40:37,40:38", "dtypep": "(VB)"}], "rightp": [{"type": "CONST", "name": "32'sh0", "addr": "(ZB)", "loc": "d,40:39,40:40", "dtypep": "(VB)"}]}]}, {"type": "BASICDTYPE", "name": "logic", "addr": "(Z)", "loc": "d,38:17,38:18", "dtypep": "(Z)", "keyword": "logic", "range": "5:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(U)", "loc": "d,34:27,34:28", "dtypep": "(U)", "keyword": "logic", "range": "5:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "integer", "addr": "(G)", "loc": "d,18:19,18:26", "dtypep": "(G)", "keyword": "integer", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(VB)", "loc": "d,40:37,40:38", "dtypep": "(VB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}]}, {"type": "CONSTPOOL", "name": "", "addr": "(D)", "loc": "a,0:0,0:0", "modulep": [{"type": "MODULE", "name": "@CONST-POOL@", "addr": "(AC)", "loc": "a,0:0,0:0", "origName": "@CONST-POOL@", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "NONE", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "@CONST-POOL@", "addr": "(BC)", "loc": "a,0:0,0:0", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(AC)", "varsp": [], "blocksp": [], "inlinesp": []}], "activesp": []}]}]}