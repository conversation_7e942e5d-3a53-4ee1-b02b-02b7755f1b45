%Error-UNSUPPORTED: t/t_randomize_method_types_unsup.v:19:25: Unsupported: random member variable with type 'int$[]'
   19 |    constraint dynsize { dynarr.size < 20; }
      |                         ^~~~~~
                    ... For error description see https://verilator.org/warn/UNSUPPORTED?v=latest
%Error-UNSUPPORTED: t/t_randomize_method_types_unsup.v:12:13: Unsupported: random member variable with type 'int$[string]'
                                                            : ... note: In instance 't'
   12 |    rand int assocarr[string];
      |             ^~~~~~~~
%Error-UNSUPPORTED: t/t_randomize_method_types_unsup.v:14:13: Unsupported: random member variable with type 'int$[0:4]'
                                                            : ... note: In instance 't'
   14 |    rand int unpackarr[5];
      |             ^~~~~~~~~
%Error-UNSUPPORTED: t/t_randomize_method_types_unsup.v:15:15: Unsupported: random member variable with type 'union{}$unit::Union'
                                                            : ... note: In instance 't'
   15 |    rand Union uni;
      |               ^~~
%Error-UNSUPPORTED: t/t_randomize_method_types_unsup.v:16:13: Unsupported: random member variable with the type of the containing class
                                                            : ... note: In instance 't'
   16 |    rand Cls cls;
      |             ^~~
%Error: Exiting due to
