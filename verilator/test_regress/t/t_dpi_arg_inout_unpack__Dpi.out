// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Prototypes for DPI import and export functions.
//
// Verilator includes this file in all generated .cpp files that use DPI functions.
// Manually include this file where DPI .c import functions are declared to ensure
// the C functions match the expectations of the DPI imports.

#ifndef VERILATED_VT_DPI_ARG_INOUT_UNPACK__DPI_H_
#define VERILATED_VT_DPI_ARG_INOUT_UNPACK__DPI_H_  // guard

#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif


// DPI EXPORTS
extern void e_bit121_0d(svBitVecVal* val);
extern void e_bit121_1d(svBitVecVal* val);
extern void e_bit121_2d(svBitVecVal* val);
extern void e_bit121_3d(svBitVecVal* val);
extern void e_bit1_0d(svBit* val);
extern void e_bit1_1d(svBit* val);
extern void e_bit1_2d(svBit* val);
extern void e_bit1_3d(svBit* val);
extern void e_bit7_0d(svBitVecVal* val);
extern void e_bit7_1d(svBitVecVal* val);
extern void e_bit7_2d(svBitVecVal* val);
extern void e_bit7_3d(svBitVecVal* val);
extern void e_byte_0d(char* val);
extern void e_byte_1d(char* val);
extern void e_byte_2d(char* val);
extern void e_byte_3d(char* val);
extern void e_byte_unsigned_0d(unsigned char* val);
extern void e_byte_unsigned_1d(unsigned char* val);
extern void e_byte_unsigned_2d(unsigned char* val);
extern void e_byte_unsigned_3d(unsigned char* val);
extern void e_chandle_0d(void** val);
extern void e_chandle_1d(void** val);
extern void e_chandle_2d(void** val);
extern void e_chandle_3d(void** val);
extern void e_int_0d(int* val);
extern void e_int_1d(int* val);
extern void e_int_2d(int* val);
extern void e_int_3d(int* val);
extern void e_int_unsigned_0d(unsigned int* val);
extern void e_int_unsigned_1d(unsigned int* val);
extern void e_int_unsigned_2d(unsigned int* val);
extern void e_int_unsigned_3d(unsigned int* val);
extern void e_integer_0d(svLogicVecVal* val);
extern void e_integer_1d(svLogicVecVal* val);
extern void e_integer_2d(svLogicVecVal* val);
extern void e_integer_3d(svLogicVecVal* val);
extern void e_logic121_0d(svLogicVecVal* val);
extern void e_logic121_1d(svLogicVecVal* val);
extern void e_logic121_2d(svLogicVecVal* val);
extern void e_logic121_3d(svLogicVecVal* val);
extern void e_logic1_0d(svLogic* val);
extern void e_logic1_1d(svLogic* val);
extern void e_logic1_2d(svLogic* val);
extern void e_logic1_3d(svLogic* val);
extern void e_logic7_0d(svLogicVecVal* val);
extern void e_logic7_1d(svLogicVecVal* val);
extern void e_logic7_2d(svLogicVecVal* val);
extern void e_logic7_3d(svLogicVecVal* val);
extern void e_longint_0d(long long* val);
extern void e_longint_1d(long long* val);
extern void e_longint_2d(long long* val);
extern void e_longint_3d(long long* val);
extern void e_longint_unsigned_0d(unsigned long long* val);
extern void e_longint_unsigned_1d(unsigned long long* val);
extern void e_longint_unsigned_2d(unsigned long long* val);
extern void e_longint_unsigned_3d(unsigned long long* val);
extern void e_pack_struct_0d(svLogicVecVal* val);
extern void e_pack_struct_1d(svLogicVecVal* val);
extern void e_pack_struct_2d(svLogicVecVal* val);
extern void e_pack_struct_3d(svLogicVecVal* val);
extern void e_real_0d(double* val);
extern void e_real_1d(double* val);
extern void e_real_2d(double* val);
extern void e_real_3d(double* val);
extern void e_shortint_0d(short* val);
extern void e_shortint_1d(short* val);
extern void e_shortint_2d(short* val);
extern void e_shortint_3d(short* val);
extern void e_shortint_unsigned_0d(unsigned short* val);
extern void e_shortint_unsigned_1d(unsigned short* val);
extern void e_shortint_unsigned_2d(unsigned short* val);
extern void e_shortint_unsigned_3d(unsigned short* val);
extern void e_string_0d(const char** val);
extern void e_string_1d(const char** val);
extern void e_string_2d(const char** val);
extern void e_string_3d(const char** val);
extern void e_time_0d(svLogicVecVal* val);
extern void e_time_1d(svLogicVecVal* val);
extern void e_time_2d(svLogicVecVal* val);
extern void e_time_3d(svLogicVecVal* val);

// DPI IMPORTS
extern void check_exports();
extern void* get_non_null();
extern void i_bit121_0d(svBitVecVal* val);
extern void i_bit121_1d(svBitVecVal* val);
extern void i_bit121_2d(svBitVecVal* val);
extern void i_bit121_3d(svBitVecVal* val);
extern void i_bit1_0d(svBit* val);
extern void i_bit1_1d(svBit* val);
extern void i_bit1_2d(svBit* val);
extern void i_bit1_3d(svBit* val);
extern void i_bit7_0d(svBitVecVal* val);
extern void i_bit7_1d(svBitVecVal* val);
extern void i_bit7_2d(svBitVecVal* val);
extern void i_bit7_3d(svBitVecVal* val);
extern void i_byte_0d(char* val);
extern void i_byte_1d(char* val);
extern void i_byte_2d(char* val);
extern void i_byte_3d(char* val);
extern void i_byte_unsigned_0d(unsigned char* val);
extern void i_byte_unsigned_1d(unsigned char* val);
extern void i_byte_unsigned_2d(unsigned char* val);
extern void i_byte_unsigned_3d(unsigned char* val);
extern void i_chandle_0d(void** val);
extern void i_chandle_1d(void** val);
extern void i_chandle_2d(void** val);
extern void i_chandle_3d(void** val);
extern void i_int_0d(int* val);
extern void i_int_1d(int* val);
extern void i_int_2d(int* val);
extern void i_int_3d(int* val);
extern void i_int_unsigned_0d(unsigned int* val);
extern void i_int_unsigned_1d(unsigned int* val);
extern void i_int_unsigned_2d(unsigned int* val);
extern void i_int_unsigned_3d(unsigned int* val);
extern void i_integer_0d(svLogicVecVal* val);
extern void i_integer_1d(svLogicVecVal* val);
extern void i_integer_2d(svLogicVecVal* val);
extern void i_integer_3d(svLogicVecVal* val);
extern void i_logic121_0d(svLogicVecVal* val);
extern void i_logic121_1d(svLogicVecVal* val);
extern void i_logic121_2d(svLogicVecVal* val);
extern void i_logic121_3d(svLogicVecVal* val);
extern void i_logic1_0d(svLogic* val);
extern void i_logic1_1d(svLogic* val);
extern void i_logic1_2d(svLogic* val);
extern void i_logic1_3d(svLogic* val);
extern void i_logic7_0d(svLogicVecVal* val);
extern void i_logic7_1d(svLogicVecVal* val);
extern void i_logic7_2d(svLogicVecVal* val);
extern void i_logic7_3d(svLogicVecVal* val);
extern void i_longint_0d(long long* val);
extern void i_longint_1d(long long* val);
extern void i_longint_2d(long long* val);
extern void i_longint_3d(long long* val);
extern void i_longint_unsigned_0d(unsigned long long* val);
extern void i_longint_unsigned_1d(unsigned long long* val);
extern void i_longint_unsigned_2d(unsigned long long* val);
extern void i_longint_unsigned_3d(unsigned long long* val);
extern void i_pack_struct_0d(svLogicVecVal* val);
extern void i_pack_struct_1d(svLogicVecVal* val);
extern void i_pack_struct_2d(svLogicVecVal* val);
extern void i_pack_struct_3d(svLogicVecVal* val);
extern void i_real_0d(double* val);
extern void i_real_1d(double* val);
extern void i_real_2d(double* val);
extern void i_real_3d(double* val);
extern void i_shortint_0d(short* val);
extern void i_shortint_1d(short* val);
extern void i_shortint_2d(short* val);
extern void i_shortint_3d(short* val);
extern void i_shortint_unsigned_0d(unsigned short* val);
extern void i_shortint_unsigned_1d(unsigned short* val);
extern void i_shortint_unsigned_2d(unsigned short* val);
extern void i_shortint_unsigned_3d(unsigned short* val);
extern void i_string_0d(const char** val);
extern void i_string_1d(const char** val);
extern void i_string_2d(const char** val);
extern void i_string_3d(const char** val);
extern void i_time_0d(svLogicVecVal* val);
extern void i_time_1d(svLogicVecVal* val);
extern void i_time_2d(svLogicVecVal* val);
extern void i_time_3d(svLogicVecVal* val);

#ifdef __cplusplus
}
#endif

#endif  // guard
