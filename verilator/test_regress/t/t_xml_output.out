<?xml version="1.0" ?>
<!-- DESCRIPTION: Verilator output: XML representation of netlist -->
<verilator_xml>
  <files>
    <file id="a" filename="&lt;built-in&gt;" language="1800-2023"/>
    <file id="b" filename="&lt;command-line&gt;" language="1800-2023"/>
    <file id="c" filename="input.vc" language="1800-2023"/>
    <file id="d" filename="t/t_xml_output.v" language="1800-2023"/>
  </files>
  <module_files>
    <file id="d" filename="t/t_xml_output.v" language="1800-2023"/>
  </module_files>
  <cells>
    <cell loc="d,7,8,7,9" name="m" submodname="m" hier="m"/>
  </cells>
  <netlist>
    <module loc="d,7,8,7,9" name="m" origName="m">
      <var loc="d,8,10,8,13" name="clk" tag="foo_op" dtype_id="1" dir="input" pinIndex="1" vartype="logic" origName="clk"/>
    </module>
    <typetable loc="a,0,0,0,0">
      <basicdtype loc="d,8,10,8,13" id="1" name="logic"/>
    </typetable>
  </netlist>
</verilator_xml>
