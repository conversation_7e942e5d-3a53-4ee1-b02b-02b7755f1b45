%Warning-PINNOCONNECT: t/t_inst_missing_bad.v:13:17: Cell pin is not connected: '__pinNumber2'
   13 |    sub sub (ok, , nc);
      |                 ^
                       ... For warning description see https://verilator.org/warn/PINNOCONNECT?v=latest
                       ... Use "/* verilator lint_off PINNOCONNECT */" and lint_on around source to disable this message.
%Warning-PINMISSING: t/t_inst_missing_bad.v:13:8: Cell has missing pin: 'missing'
   13 |    sub sub (ok, , nc);
      |        ^~~
                     t/t_inst_missing_bad.v:16:51: ... Location of port declaration
   16 | module sub (input ok, input none, input nc, input missing);
      |                                                   ^~~~~~~
%Error: Exiting due to
