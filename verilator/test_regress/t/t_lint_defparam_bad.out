%Warning-DEFPARAM: t/t_lint_defparam.v:10:19: defparam is deprecated (IEEE 1800-2023 C.4.1)
                                            : ... Suggest use instantiation with #(.P(...etc...))
   10 |    defparam sub.P = 2;
      |                   ^
                   ... For warning description see https://verilator.org/warn/DEFPARAM?v=latest
                   ... Use "/* verilator lint_off DEFPARAM */" and lint_on around source to disable this message.
%Error: Exiting due to
