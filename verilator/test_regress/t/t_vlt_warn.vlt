// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2010 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

`verilator_config

lint_off -rule DEPRECATED     -file "t/t_vlt_warn.vlt" -lines 14
lint_off -rule CASEINCOMPLETE -file "t/t_vlt_warn.v"
lint_off -rule WIDTH          -file "t/t_vlt_warn.v" -lines 19
lint_off -rule DECLFILENAME   -file "*/t_vlt_warn.v"
// Test wildcard filenames
lint_off -rule WIDTH          -file "*/t_vlt_warn.v" -lines 20-20
// Test global disables
lint_off                      -file "*/t_vlt_warn.v" -lines 21-21
// Test match
lint_off -rule UNUSED         -file "*/t_vlt_warn.v" -match "Signal is not used: 'width_warn*'"

coverage_off -file "t/t_vlt_warn.v"
// Test --flag is also accepted
tracing_off --file "t/t_vlt_warn.v"
