%Error: t/t_select_bad_range4.v:17:8: Width of bit range is huge; vector of over 1 billion bits: 0x20000001
                                    : ... note: In instance 't'
   17 |    reg [1<<29 : 0] hugerange;
      |        ^
%Error: t/t_select_bad_range4.v:20:16: Width of :+ or :- is < 0: 32'hffffffff
                                     : ... note: In instance 't'
   20 |       sel2 = mi[44 +: -1];
      |                ^
%Error: t/t_select_bad_range4.v:20:16: Width of bit extract must be positive (IEEE 1800-2023 11.5.1)
                                     : ... note: In instance 't'
   20 |       sel2 = mi[44 +: -1];
      |                ^
%Warning-WIDTHEXPAND: t/t_select_bad_range4.v:20:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 1 bits.
                                                   : ... note: In instance 't'
   20 |       sel2 = mi[44 +: -1];
      |            ^
                      ... For warning description see https://verilator.org/warn/WIDTHEXPAND?v=latest
                      ... Use "/* verilator lint_off WIDTHEXPAND */" and lint_on around source to disable this message.
%Error: t/t_select_bad_range4.v:21:16: Width of :+ or :- is huge; vector of over 1 billion bits: 32'h20000000
                                     : ... note: In instance 't'
   21 |       sel2 = mi[44 +: 1<<29];
      |                ^
%Warning-SELRANGE: t/t_select_bad_range4.v:21:16: Extracting 536870912 bits from only 6 bit number
                                                : ... note: In instance 't'
   21 |       sel2 = mi[44 +: 1<<29];
      |                ^
%Warning-SELRANGE: t/t_select_bad_range4.v:21:16: Selection index out of range: 536870915:4 outside 45:40
                                                : ... note: In instance 't'
   21 |       sel2 = mi[44 +: 1<<29];
      |                ^
%Warning-WIDTHTRUNC: t/t_select_bad_range4.v:21:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 536870912 bits.
                                                  : ... note: In instance 't'
   21 |       sel2 = mi[44 +: 1<<29];
      |            ^
%Error: t/t_select_bad_range4.v:22:23: Expecting expression to be constant, but variable isn't const: 'nonconst'
                                     : ... note: In instance 't'
   22 |       sel2 = mi[44 +: nonconst];
      |                       ^~~~~~~~
%Error: t/t_select_bad_range4.v:22:23: Width of :+ or :- bit slice range isn't a constant
                                     : ... note: In instance 't'
   22 |       sel2 = mi[44 +: nonconst];
      |                       ^~~~~~~~
%Warning-WIDTHEXPAND: t/t_select_bad_range4.v:22:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 1 bits.
                                                   : ... note: In instance 't'
   22 |       sel2 = mi[44 +: nonconst];
      |            ^
%Warning-WIDTHEXPAND: t/t_select_bad_range4.v:23:17: Operator SUB expects 32 or 6 bits on the LHS, but LHS's VARREF 'nonconst' generates 1 bits.
                                                   : ... note: In instance 't'
   23 |       sel2 = mi[nonconst];
      |                 ^~~~~~~~
%Warning-WIDTHEXPAND: t/t_select_bad_range4.v:23:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 1 bits.
                                                   : ... note: In instance 't'
   23 |       sel2 = mi[nonconst];
      |            ^
%Error: t/t_select_bad_range4.v:24:17: First value of [a:b] isn't a constant, maybe you want +: or -:
                                     : ... note: In instance 't'
   24 |       sel2 = mi[nonconst : nonconst];
      |                 ^~~~~~~~
%Error: t/t_select_bad_range4.v:24:28: Second value of [a:b] isn't a constant, maybe you want +: or -:
                                     : ... note: In instance 't'
   24 |       sel2 = mi[nonconst : nonconst];
      |                            ^~~~~~~~
%Warning-WIDTHEXPAND: t/t_select_bad_range4.v:24:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 1 bits.
                                                   : ... note: In instance 't'
   24 |       sel2 = mi[nonconst : nonconst];
      |            ^
%Warning-SELRANGE: t/t_select_bad_range4.v:25:16: Extracting 536870913 bits from only 6 bit number
                                                : ... note: In instance 't'
   25 |       sel2 = mi[1<<29 : 0];
      |                ^
%Warning-SELRANGE: t/t_select_bad_range4.v:25:16: Selection index out of range: 536870872:-40 outside 45:40
                                                : ... note: In instance 't'
   25 |       sel2 = mi[1<<29 : 0];
      |                ^
%Warning-SELRANGE: t/t_select_bad_range4.v:25:16: Extracting 536870913 bits from only 536870873 bit number
                                                : ... note: In instance 't'
   25 |       sel2 = mi[1<<29 : 0];
      |                ^
%Warning-WIDTHTRUNC: t/t_select_bad_range4.v:25:12: Operator ASSIGN expects 4 bits on the Assign RHS, but Assign RHS's SEL generates 536870913 bits.
                                                  : ... note: In instance 't'
   25 |       sel2 = mi[1<<29 : 0];
      |            ^
%Error: Exiting due to
