%Error: t/t_func_bad.v:9:11: Missing argument on non-defaulted argument 'from2' in function call to FUNC 'add'
                           : ... note: In instance 't'
    9 |       if (add(3'd1) != 0) $stop;   
      |           ^~~
%Error: t/t_func_bad.v:10:27: Too many arguments in function call to FUNC 'add'
                            : ... note: In instance 't'
   10 |       if (add(3'd1, 3'd2, 3'd3) != 0) $stop;     
      |                           ^~~~
%Error: t/t_func_bad.v:11:7: Missing argument on non-defaulted argument 'y' in function call to TASK 'x'
                           : ... note: In instance 't'
   11 |       x;  
      |       ^
%Warning-WIDTHTRUNC: t/t_func_bad.v:11:7: Function output argument 'y' requires 1 bits, but connection's CONST '?32?h0' generates 32 bits.
                                        : ... note: In instance 't'
   11 |       x;  
      |       ^
                     ... For warning description see https://verilator.org/warn/WIDTHTRUNC?v=latest
                     ... Use "/* verilator lint_off WIDTHTRUNC */" and lint_on around source to disable this message.
%Error: t/t_func_bad.v:14:17: No such argument 'no_such' in function call to FUNC 'f'
                            : ... note: In instance 't'
   14 |       f(.j(1), .no_such(2));  
      |                 ^~~~~~~
%Error: t/t_func_bad.v:15:19: Duplicate argument 'dup' in function call to FUNC 'f'
                            : ... note: In instance 't'
   15 |       f(.dup(1), .dup(3));  
      |                   ^~~
%Error: t/t_func_bad.v:16:13: Too many arguments in function call to FUNC 'f'
                            : ... note: In instance 't'
   16 |       f(1,2,3);  
      |             ^
%Error: Exiting due to
