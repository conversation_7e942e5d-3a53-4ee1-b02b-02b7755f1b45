{"type": "NETLIST", "name": "$root", "addr": "(B)", "loc": "a,0:0,0:0", "timeunit": "1ps", "timeprecision": "1ps", "typeTablep": "(C)", "constPoolp": "(D)", "dollarUnitPkgp": "(E)", "stdPackagep": "UNLINKED", "evalp": "(F)", "evalNbap": "(G)", "dpiExportTriggerp": "UNLINKED", "delaySchedulerp": "UNLINKED", "nbaEventp": "UNLINKED", "nbaEventTriggerp": "UNLINKED", "topScopep": "(H)", "modulesp": [{"type": "MODULE", "name": "$root", "addr": "(I)", "loc": "e,7:8,7:9", "origName": "$root", "level": 1, "modPublic": true, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "VAR", "name": "clk", "addr": "(J)", "loc": "e,22:10,22:13", "dtypep": "(K)", "origName": "clk", "isSc": false, "isPrimaryIO": true, "direction": "INPUT", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": true, "isSigPublic": true, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "clker", "lifetime": "NONE", "varType": "PORT", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(L)", "loc": "e,7:8,7:9", "dtypep": "(K)", "origName": "__Vtrigprevexpr___TOP__clk__0", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactContinue", "addr": "(M)", "loc": "e,7:8,7:9", "dtypep": "(N)", "origName": "__VactContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "t.cyc", "addr": "(O)", "loc": "e,23:12,23:15", "dtypep": "(P)", "origName": "cyc", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "VSTATIC", "varType": "VAR", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactIterCount", "addr": "(Q)", "loc": "e,7:8,7:9", "dtypep": "(R)", "origName": "__VactIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactTriggered", "addr": "(S)", "loc": "e,7:8,7:9", "dtypep": "(T)", "origName": "__VactTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaTriggered", "addr": "(U)", "loc": "e,7:8,7:9", "dtypep": "(T)", "origName": "__VnbaTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": false, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CELL", "name": "t.ma0.mb0", "addr": "(V)", "loc": "e,87:12,87:15", "origName": "mb0", "recursive": false, "modp": "(W)", "pinsp": [], "paramsp": [], "rangep": [], "intfRefsp": []}, {"type": "TOPSCOPE", "name": "", "addr": "(H)", "loc": "e,7:8,7:9", "senTreesp": [], "scopep": [{"type": "SCOPE", "name": "TOP", "addr": "(X)", "loc": "e,7:8,7:9", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(I)", "varsp": [], "blocksp": []}]}, {"type": "CFUNC", "name": "_eval_static", "addr": "(Y)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(Z)", "loc": "e,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(AB)", "loc": "e,7:8,7:9", "dtypep": "(BB)", "funcName": "_eval_static__TOP", "funcp": "(CB)", "argsp": []}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_static__TOP", "addr": "(CB)", "loc": "e,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(DB)", "loc": "e,23:16,23:17", "dtypep": "(P)", "rhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(EB)", "loc": "e,23:16,23:17", "dtypep": "(FB)"}], "lhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(GB)", "loc": "e,23:16,23:17", "dtypep": "(P)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial", "addr": "(HB)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(IB)", "loc": "e,7:8,7:9", "exprp": [{"type": "CCALL", "name": "", "addr": "(JB)", "loc": "e,7:8,7:9", "dtypep": "(BB)", "funcName": "_eval_initial__TOP", "funcp": "(KB)", "argsp": []}]}, {"type": "STMTEXPR", "name": "", "addr": "(LB)", "loc": "e,87:12,87:15", "exprp": [{"type": "CCALL", "name": "", "addr": "(MB)", "loc": "e,87:12,87:15", "dtypep": "(BB)", "funcName": "_eval_initial__TOP__t__DOT__ma0__DOT__mb0", "funcp": "(NB)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(OB)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "rhsp": [{"type": "VARREF", "name": "clk", "addr": "(QB)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(RB)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_initial__TOP", "addr": "(KB)", "loc": "e,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "COMMENT", "name": "Function: f", "addr": "(SB)", "loc": "e,16:25,16:26"}, {"type": "DISPLAY", "name": "", "addr": "(TB)", "loc": "e,154:7,154:15", "fmtp": [{"type": "SFORMATF", "name": "%m", "addr": "(UB)", "loc": "e,154:7,154:15", "dtypep": "(VB)", "exprsp": [], "scopeNamep": [{"type": "SCOPENAME", "name": "", "addr": "(WB)", "loc": "e,154:7,154:15", "dtypep": "(XB)", "dpiExport": false, "forFormat": true, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(YB)", "loc": "e,154:7,154:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(ZB)", "loc": "e,154:7,154:15", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(AC)", "loc": "e,154:7,154:15", "shortText": "__DOT__ma0"}, {"type": "TEXT", "name": "", "addr": "(BC)", "loc": "e,154:7,154:15", "shortText": "__DOT__u_b__BRA__0__KET__"}, {"type": "TEXT", "name": "", "addr": "(CC)", "loc": "e,154:7,154:15", "shortText": "__DOT__f"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(DC)", "loc": "e,154:7,154:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(EC)", "loc": "e,154:7,154:15", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(FC)", "loc": "e,154:7,154:15", "shortText": "__DOT__ma0"}, {"type": "TEXT", "name": "", "addr": "(GC)", "loc": "e,154:7,154:15", "shortText": "__DOT__u_b__BRA__0__KET__"}]}]}], "filep": []}, {"type": "COMMENT", "name": "Function: f", "addr": "(HC)", "loc": "e,17:25,17:26"}, {"type": "DISPLAY", "name": "", "addr": "(IC)", "loc": "e,154:7,154:15", "fmtp": [{"type": "SFORMATF", "name": "%m", "addr": "(JC)", "loc": "e,154:7,154:15", "dtypep": "(VB)", "exprsp": [], "scopeNamep": [{"type": "SCOPENAME", "name": "", "addr": "(KC)", "loc": "e,154:7,154:15", "dtypep": "(XB)", "dpiExport": false, "forFormat": true, "scopeAttrp": [{"type": "TEXT", "name": "", "addr": "(LC)", "loc": "e,154:7,154:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(MC)", "loc": "e,154:7,154:15", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(NC)", "loc": "e,154:7,154:15", "shortText": "__DOT__ma0"}, {"type": "TEXT", "name": "", "addr": "(OC)", "loc": "e,154:7,154:15", "shortText": "__DOT__u_b__BRA__0__KET__"}, {"type": "TEXT", "name": "", "addr": "(PC)", "loc": "e,154:7,154:15", "shortText": "__DOT__f"}], "scopeEntrp": [{"type": "TEXT", "name": "", "addr": "(QC)", "loc": "e,154:7,154:15", "shortText": "__DOT__TOP"}, {"type": "TEXT", "name": "", "addr": "(RC)", "loc": "e,154:7,154:15", "shortText": "__DOT__t"}, {"type": "TEXT", "name": "", "addr": "(SC)", "loc": "e,154:7,154:15", "shortText": "__DOT__ma0"}, {"type": "TEXT", "name": "", "addr": "(TC)", "loc": "e,154:7,154:15", "shortText": "__DOT__u_b__BRA__0__KET__"}]}]}], "filep": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_final", "addr": "(UC)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_eval_settle", "addr": "(VC)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_eval_triggers__act", "addr": "(WC)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(XC)", "loc": "e,7:8,7:9", "exprp": [{"type": "CMETHODHARD", "name": "set", "addr": "(YC)", "loc": "e,7:8,7:9", "dtypep": "(BB)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(ZC)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(AD)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}, {"type": "AND", "name": "", "addr": "(CD)", "loc": "e,27:14,27:21", "dtypep": "(PB)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(DD)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "size": 32, "lhsp": [{"type": "VARREF", "name": "clk", "addr": "(ED)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "rhsp": [{"type": "NOT", "name": "", "addr": "(FD)", "loc": "e,27:14,27:21", "dtypep": "(PB)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(GD)", "loc": "e,27:14,27:21", "dtypep": "(PB)", "size": 32, "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(HD)", "loc": "e,27:14,27:21", "dtypep": "(PB)", "access": "RD", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}]}]}]}, {"type": "ASSIGN", "name": "", "addr": "(ID)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "rhsp": [{"type": "VARREF", "name": "clk", "addr": "(JD)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(KD)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "TEXTBLOCK", "name": "", "addr": "(LD)", "loc": "e,7:8,7:9", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(MD)", "loc": "e,7:8,7:9", "shortText": "#ifdef VL_DEBUG..."}, {"type": "TEXT", "name": "", "addr": "(ND)", "loc": "e,7:8,7:9", "shortText": "if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {..."}, {"type": "STMTEXPR", "name": "", "addr": "(OD)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(PD)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_dump_triggers__act", "funcp": "(QD)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(RD)", "loc": "e,7:8,7:9", "shortText": "}..."}, {"type": "TEXT", "name": "", "addr": "(SD)", "loc": "e,7:8,7:9", "shortText": "#endif..."}]}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__act", "addr": "(QD)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(TD)", "loc": "e,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(UD)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(VD)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(WD)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(XD)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(YD)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(ZD)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "RD", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(AE)", "loc": "e,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(BE)", "loc": "e,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(CE)", "loc": "e,7:8,7:9", "dtypep": "(DE)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(EE)", "loc": "e,7:8,7:9", "dtypep": "(DE)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(FE)", "loc": "e,7:8,7:9", "dtypep": "(XB)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(GE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "RD", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(HE)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(IE)", "loc": "e,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         'act' region trigger index 0 is active: @(posedge clk)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_dump_triggers__nba", "addr": "(JE)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(KE)", "loc": "e,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(LE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(ME)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}], "rhsp": [{"type": "NOT", "name": "", "addr": "(NE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(OE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "size": 32, "lhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(PE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(QE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(RE)", "loc": "e,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         No triggers active\\n\");..."}], "elsesp": []}, {"type": "IF", "name": "", "addr": "(SE)", "loc": "e,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(TE)", "loc": "e,7:8,7:9", "dtypep": "(DE)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(UE)", "loc": "e,7:8,7:9", "dtypep": "(DE)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(VE)", "loc": "e,7:8,7:9", "dtypep": "(XB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(WE)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(XE)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}]}]}], "thensp": [{"type": "TEXT", "name": "", "addr": "(YE)", "loc": "e,7:8,7:9", "shortText": "VL_DBG_MSGF(\"         'nba' region trigger index 0 is active: @(posedge clk)\\n\");..."}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_act", "addr": "(ZE)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}, {"type": "CFUNC", "name": "_nba_sequent__TOP__0", "addr": "(AF)", "loc": "e,28:7,28:10", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [{"type": "VAR", "name": "__Vdly__t.cyc", "addr": "(BF)", "loc": "e,23:12,23:15", "dtypep": "(P)", "origName": "__Vdly__t__DOT__cyc", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "BLOCKTEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "CRESET", "name": "", "addr": "(CF)", "loc": "e,23:12,23:15", "varrefp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(DF)", "loc": "e,23:12,23:15", "dtypep": "(P)", "access": "WR", "varp": "(BF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "stmtsp": [{"type": "ASSIGNPRE", "name": "", "addr": "(EF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(FF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "access": "RD", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(GF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "access": "WR", "varp": "(BF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGNDLY", "name": "", "addr": "(HF)", "loc": "e,28:11,28:13", "dtypep": "(P)", "rhsp": [{"type": "ADD", "name": "", "addr": "(IF)", "loc": "e,28:18,28:19", "dtypep": "(P)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(JF)", "loc": "e,28:20,28:21", "dtypep": "(BD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'sh1", "addr": "(KF)", "loc": "e,28:20,28:21", "dtypep": "(FB)"}]}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(LF)", "loc": "e,28:14,28:17", "dtypep": "(P)", "access": "RD", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(MF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "access": "WR", "varp": "(BF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(NF)", "loc": "e,48:7,48:9", "condp": [{"type": "EQ", "name": "", "addr": "(OF)", "loc": "e,48:14,48:16", "dtypep": "(PB)", "lhsp": [{"type": "CONST", "name": "32'sh9", "addr": "(PF)", "loc": "e,48:16,48:17", "dtypep": "(FB)"}], "rhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(QF)", "loc": "e,48:11,48:14", "dtypep": "(P)", "access": "RD", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "DISPLAY", "name": "", "addr": "(RF)", "loc": "e,49:10,49:16", "fmtp": [{"type": "SFORMATF", "name": "*-* All Finished *-*\\n", "addr": "(SF)", "loc": "e,49:10,49:16", "dtypep": "(VB)", "exprsp": [], "scopeNamep": []}], "filep": []}, {"type": "FINISH", "name": "", "addr": "(TF)", "loc": "e,50:10,50:17"}], "elsesp": []}, {"type": "ASSIGNPOST", "name": "", "addr": "(UF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "rhsp": [{"type": "VARREF", "name": "__Vdly__t.cyc", "addr": "(VF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "access": "RD", "varp": "(BF)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "lhsp": [{"type": "VARREF", "name": "t.cyc", "addr": "(WF)", "loc": "e,28:7,28:10", "dtypep": "(P)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_nba", "addr": "(G)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(XF)", "loc": "e,7:8,7:9", "condp": [{"type": "AND", "name": "", "addr": "(YF)", "loc": "e,7:8,7:9", "dtypep": "(DE)", "lhsp": [{"type": "CONST", "name": "64'h1", "addr": "(ZF)", "loc": "e,7:8,7:9", "dtypep": "(DE)"}], "rhsp": [{"type": "CMETHODHARD", "name": "word", "addr": "(AG)", "loc": "e,7:8,7:9", "dtypep": "(XB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(BG)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "CONST", "name": "32'h0", "addr": "(CG)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}]}]}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(DG)", "loc": "e,28:7,28:10", "exprp": [{"type": "CCALL", "name": "", "addr": "(EG)", "loc": "e,28:7,28:10", "dtypep": "(BB)", "funcName": "_nba_sequent__TOP__0", "funcp": "(AF)", "argsp": []}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__act", "addr": "(FG)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VpreTriggered", "addr": "(GG)", "loc": "e,7:8,7:9", "dtypep": "(T)", "origName": "__VpreTriggered", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": false, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VactExecute", "addr": "(HG)", "loc": "e,7:8,7:9", "dtypep": "(N)", "origName": "__VactExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "STMTEXPR", "name": "", "addr": "(IG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(JG)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_eval_triggers__act", "funcp": "(WC)", "argsp": []}]}, {"type": "ASSIGN", "name": "", "addr": "(KG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(LG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "fromp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(MG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(NG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "WR", "varp": "(HG)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(OG)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(PG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(HG)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(QG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "andNot", "addr": "(RG)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "fromp": [{"type": "VARREF", "name": "__VpreTriggered", "addr": "(SG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "WR", "varp": "(GG)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(TG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}, {"type": "VARREF", "name": "__VnbaTriggered", "addr": "(UG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(VG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "thisOr", "addr": "(WG)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(XG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "WR", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": [{"type": "VARREF", "name": "__VactTriggered", "addr": "(YG)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(S)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}]}, {"type": "STMTEXPR", "name": "", "addr": "(ZG)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(AH)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_eval_act", "funcp": "(ZE)", "argsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(BH)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VactExecute", "addr": "(CH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(HG)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_phase__nba", "addr": "(DH)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaExecute", "addr": "(EH)", "loc": "e,7:8,7:9", "dtypep": "(N)", "origName": "__VnbaExecute", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(FH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "rhsp": [{"type": "CMETHODHARD", "name": "any", "addr": "(GH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(HH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}], "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(IH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "WR", "varp": "(EH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(JH)", "loc": "a,0:0,0:0", "condp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(KH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(EH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "thensp": [{"type": "STMTEXPR", "name": "", "addr": "(LH)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(MH)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_eval_nba", "funcp": "(G)", "argsp": []}]}, {"type": "STMTEXPR", "name": "", "addr": "(NH)", "loc": "a,0:0,0:0", "exprp": [{"type": "CMETHODHARD", "name": "clear", "addr": "(OH)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "fromp": [{"type": "VARREF", "name": "__VnbaTriggered", "addr": "(PH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "WR", "varp": "(U)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "pinsp": []}]}], "elsesp": []}, {"type": "CRETURN", "name": "", "addr": "(QH)", "loc": "a,0:0,0:0", "lhsp": [{"type": "VARREF", "name": "__VnbaExecute", "addr": "(RH)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(EH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CFUNC", "name": "_eval", "addr": "(F)", "loc": "a,0:0,0:0", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(X)", "argsp": [], "initsp": [{"type": "VAR", "name": "__VnbaIterCount", "addr": "(SH)", "loc": "e,7:8,7:9", "dtypep": "(R)", "origName": "__VnbaIterCount", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}, {"type": "VAR", "name": "__VnbaContinue", "addr": "(TH)", "loc": "e,7:8,7:9", "dtypep": "(N)", "origName": "__VnbaContinue", "isSc": false, "isPrimaryIO": false, "direction": "NONE", "isConst": false, "isPullup": false, "isPulldown": false, "isUsedClock": false, "isSigPublic": false, "isLatched": false, "isUsedLoopIdx": false, "noReset": true, "attrIsolateAssign": false, "attrFileDescr": false, "isDpiOpenArray": false, "isFuncReturn": false, "isFuncLocal": true, "attrClocker": "UNKNOWN", "lifetime": "NONE", "varType": "MODULETEMP", "sensIfacep": "UNLINKED", "childDTypep": [], "delayp": [], "valuep": [], "attrsp": []}], "stmtsp": [{"type": "ASSIGN", "name": "", "addr": "(UH)", "loc": "e,7:8,7:9", "dtypep": "(R)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(VH)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(WH)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "WR", "varp": "(SH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(XH)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(YH)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(ZH)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(TH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(AI)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(BI)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(TH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(CI)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(DI)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(EI)", "loc": "a,0:0,0:0", "dtypep": "(BD)"}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(FI)", "loc": "a,0:0,0:0", "dtypep": "(R)", "access": "RD", "varp": "(SH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(GI)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(HI)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(II)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(JI)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_dump_triggers__nba", "funcp": "(JE)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(KI)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(LI)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_func_dotted.v\", 7, \"\", "}, {"type": "TEXT", "name": "", "addr": "(MI)", "loc": "a,0:0,0:0", "shortText": "\"NBA region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(NI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "rhsp": [{"type": "ADD", "name": "", "addr": "(OI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(PI)", "loc": "e,7:8,7:9", "dtypep": "(BD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(QI)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(RI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "RD", "varp": "(SH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VnbaIterCount", "addr": "(SI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "WR", "varp": "(SH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(TI)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(UI)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(VI)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(TH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(WI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "rhsp": [{"type": "CONST", "name": "32'h0", "addr": "(XI)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(YI)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "WR", "varp": "(Q)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(ZI)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(AJ)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(BJ)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(M)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "WHILE", "name": "", "addr": "(CJ)", "loc": "a,0:0,0:0", "precondsp": [], "condp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(DJ)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "access": "RD", "varp": "(M)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "stmtsp": [{"type": "IF", "name": "", "addr": "(EJ)", "loc": "a,0:0,0:0", "condp": [{"type": "LT", "name": "", "addr": "(FJ)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "lhsp": [{"type": "CONST", "name": "32'h64", "addr": "(GJ)", "loc": "a,0:0,0:0", "dtypep": "(BD)"}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(HJ)", "loc": "a,0:0,0:0", "dtypep": "(R)", "access": "RD", "varp": "(Q)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "thensp": [{"type": "TEXTBLOCK", "name": "", "addr": "(IJ)", "loc": "a,0:0,0:0", "shortText": "", "nodesp": [{"type": "TEXT", "name": "", "addr": "(JJ)", "loc": "a,0:0,0:0", "shortText": "#ifdef VL_DEBUG..."}, {"type": "STMTEXPR", "name": "", "addr": "(KJ)", "loc": "a,0:0,0:0", "exprp": [{"type": "CCALL", "name": "", "addr": "(LJ)", "loc": "a,0:0,0:0", "dtypep": "(BB)", "funcName": "_dump_triggers__act", "funcp": "(QD)", "argsp": []}]}, {"type": "TEXT", "name": "", "addr": "(MJ)", "loc": "a,0:0,0:0", "shortText": "#endif..."}, {"type": "TEXT", "name": "", "addr": "(NJ)", "loc": "a,0:0,0:0", "shortText": "VL_FATAL_MT(\"t/t_func_dotted.v\", 7, \"\", "}, {"type": "TEXT", "name": "", "addr": "(OJ)", "loc": "a,0:0,0:0", "shortText": "\"Active region did not converge.\");..."}]}], "elsesp": []}, {"type": "ASSIGN", "name": "", "addr": "(PJ)", "loc": "e,7:8,7:9", "dtypep": "(R)", "rhsp": [{"type": "ADD", "name": "", "addr": "(QJ)", "loc": "e,7:8,7:9", "dtypep": "(R)", "lhsp": [{"type": "CCAST", "name": "", "addr": "(RJ)", "loc": "e,7:8,7:9", "dtypep": "(BD)", "size": 32, "lhsp": [{"type": "CONST", "name": "32'h1", "addr": "(SJ)", "loc": "e,7:8,7:9", "dtypep": "(BD)"}]}], "rhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(TJ)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "RD", "varp": "(Q)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "lhsp": [{"type": "VARREF", "name": "__VactIterCount", "addr": "(UJ)", "loc": "e,7:8,7:9", "dtypep": "(R)", "access": "WR", "varp": "(Q)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "ASSIGN", "name": "", "addr": "(VJ)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h0", "addr": "(WJ)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(XJ)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(M)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}, {"type": "IF", "name": "", "addr": "(YJ)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(ZJ)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "funcName": "_eval_phase__act", "funcp": "(FG)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(AK)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(BK)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VactContinue", "addr": "(CK)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(M)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}, {"type": "IF", "name": "", "addr": "(DK)", "loc": "a,0:0,0:0", "condp": [{"type": "CCALL", "name": "", "addr": "(EK)", "loc": "a,0:0,0:0", "dtypep": "(PB)", "funcName": "_eval_phase__nba", "funcp": "(DH)", "argsp": []}], "thensp": [{"type": "ASSIGN", "name": "", "addr": "(FK)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "rhsp": [{"type": "CONST", "name": "1'h1", "addr": "(GK)", "loc": "e,7:8,7:9", "dtypep": "(PB)"}], "lhsp": [{"type": "VARREF", "name": "__VnbaContinue", "addr": "(HK)", "loc": "e,7:8,7:9", "dtypep": "(PB)", "access": "WR", "varp": "(TH)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "timingControlp": []}], "elsesp": []}], "incsp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_eval_debug_assertions", "addr": "(IK)", "loc": "e,7:8,7:9", "slow": false, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "IF", "name": "", "addr": "(JK)", "loc": "e,22:10,22:13", "condp": [{"type": "AND", "name": "", "addr": "(KK)", "loc": "e,22:10,22:13", "dtypep": "(K)", "lhsp": [{"type": "VARREF", "name": "clk", "addr": "(LK)", "loc": "e,22:10,22:13", "dtypep": "(K)", "access": "RD", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}], "rhsp": [{"type": "CONST", "name": "8'hfe", "addr": "(MK)", "loc": "e,22:10,22:13", "dtypep": "(NK)"}]}], "thensp": [{"type": "CSTMT", "name": "", "addr": "(OK)", "loc": "e,22:10,22:13", "exprsp": [{"type": "TEXT", "name": "", "addr": "(PK)", "loc": "e,22:10,22:13", "shortText": "Verilated::overWidthError(\"clk\");"}]}], "elsesp": []}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(QK)", "loc": "e,7:8,7:9", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [{"type": "CRESET", "name": "", "addr": "(RK)", "loc": "e,22:10,22:13", "varrefp": [{"type": "VARREF", "name": "clk", "addr": "(SK)", "loc": "e,22:10,22:13", "dtypep": "(K)", "access": "WR", "varp": "(J)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(TK)", "loc": "e,23:12,23:15", "varrefp": [{"type": "VARREF", "name": "t.cyc", "addr": "(UK)", "loc": "e,23:12,23:15", "dtypep": "(P)", "access": "WR", "varp": "(O)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}, {"type": "CRESET", "name": "", "addr": "(VK)", "loc": "e,7:8,7:9", "varrefp": [{"type": "VARREF", "name": "__Vtrigprevexpr___TOP__clk__0", "addr": "(WK)", "loc": "e,7:8,7:9", "dtypep": "(K)", "access": "WR", "varp": "(L)", "varScopep": "UNLINKED", "classOrPackagep": "UNLINKED"}]}], "finalsp": []}, {"type": "CUSE", "name": "mb", "addr": "(XK)", "loc": "e,87:12,87:15", "useType": "INT_FWD"}], "activesp": []}, {"type": "PACKAGE", "name": "$unit", "addr": "(E)", "loc": "a,0:0,0:0", "origName": "__024unit", "level": 3, "modPublic": false, "inLibrary": true, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(YK)", "loc": "a,0:0,0:0", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}], "activesp": []}, {"type": "MODULE", "name": "mb", "addr": "(W)", "loc": "e,99:8,99:10", "origName": "mb", "level": 4, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "1ps", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "t.ma0.mb0", "addr": "(ZK)", "loc": "e,87:12,87:15", "aboveScopep": "(X)", "aboveCellp": "(V)", "modp": "(W)", "varsp": [], "blocksp": []}, {"type": "CFUNC", "name": "_eval_initial__TOP__t.ma0.mb0", "addr": "(NB)", "loc": "e,87:12,87:15", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "(ZK)", "argsp": [], "initsp": [], "stmtsp": [{"type": "COMMENT", "name": "Function: checkName", "addr": "(AL)", "loc": "e,118:11,118:20"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(BL)", "loc": "e,119:8,119:17"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(CL)", "loc": "e,120:11,120:20"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(DL)", "loc": "e,121:11,121:20"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(EL)", "loc": "e,121:26,121:33"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(FL)", "loc": "e,122:8,122:17"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(GL)", "loc": "e,122:23,122:30"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(HL)", "loc": "e,123:11,123:20"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(IL)", "loc": "e,123:26,123:33"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(JL)", "loc": "e,142:10,142:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(KL)", "loc": "e,143:10,143:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(LL)", "loc": "e,144:10,144:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(ML)", "loc": "e,145:10,145:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(NL)", "loc": "e,145:24,145:31"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(OL)", "loc": "e,146:10,146:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(PL)", "loc": "e,146:24,146:31"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(QL)", "loc": "e,147:10,147:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(RL)", "loc": "e,147:24,147:31"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(SL)", "loc": "e,142:10,142:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(TL)", "loc": "e,143:10,143:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(UL)", "loc": "e,144:10,144:19"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(VL)", "loc": "e,145:10,145:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(WL)", "loc": "e,145:24,145:31"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(XL)", "loc": "e,146:10,146:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(YL)", "loc": "e,146:24,146:31"}, {"type": "COMMENT", "name": "Function: checkName", "addr": "(ZL)", "loc": "e,147:10,147:19"}, {"type": "COMMENT", "name": "Function: get<PERSON><PERSON>", "addr": "(AM)", "loc": "e,147:24,147:31"}], "finalsp": []}, {"type": "CFUNC", "name": "_ctor_var_reset", "addr": "(BM)", "loc": "e,99:8,99:10", "slow": true, "isStatic": false, "dpiExportDispatcher": false, "dpiExportImpl": false, "dpiImportPrototype": false, "dpiImportWrapper": false, "dpiContext": false, "isConstructor": false, "isDestructor": false, "isVirtual": false, "isCoroutine": false, "needProcess": false, "scopep": "UNLINKED", "argsp": [], "initsp": [], "stmtsp": [], "finalsp": []}], "activesp": []}], "filesp": [{"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2__Syms.cpp", "addr": "(CM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2__Syms.h", "addr": "(DM)", "loc": "a,0:0,0:0", "source": false, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2.h", "addr": "(EM)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2.cpp", "addr": "(FM)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root.h", "addr": "(GM)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$unit.h", "addr": "(HM)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_mb.h", "addr": "(IM)", "loc": "a,0:0,0:0", "source": false, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root__Slow.cpp", "addr": "(JM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root__DepSet_hfe3fbcf1__0__Slow.cpp", "addr": "(KM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root__DepSet_hc7543dab__0__Slow.cpp", "addr": "(LM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root__DepSet_hfe3fbcf1__0.cpp", "addr": "(MM)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$root__DepSet_hc7543dab__0.cpp", "addr": "(NM)", "loc": "a,0:0,0:0", "source": true, "slow": false, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$unit__Slow.cpp", "addr": "(OM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_$unit__DepSet_hf1a5ce9e__0__Slow.cpp", "addr": "(PM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_mb__Slow.cpp", "addr": "(QM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}, {"type": "CFILE", "name": "obj_vlt/t_func_dotted_inl2/Vt_func_dotted_inl2_mb__DepSet_h340bfa86__0__Slow.cpp", "addr": "(RM)", "loc": "a,0:0,0:0", "source": true, "slow": true, "tblockp": []}], "miscsp": [{"type": "TYPETABLE", "name": "", "addr": "(C)", "loc": "a,0:0,0:0", "constraintRefp": "UNLINKED", "emptyQueuep": "UNLINKED", "queueIndexp": "UNLINKED", "streamp": "UNLINKED", "voidp": "(BB)", "typesp": [{"type": "BASICDTYPE", "name": "logic", "addr": "(K)", "loc": "d,50:22,50:24", "dtypep": "(K)", "keyword": "logic", "generic": true, "rangep": []}, {"type": "VOIDDTYPE", "name": "", "addr": "(BB)", "loc": "d,51:21,51:30", "dtypep": "(BB)", "generic": false}, {"type": "BASICDTYPE", "name": "string", "addr": "(VB)", "loc": "d,156:10,156:16", "dtypep": "(VB)", "keyword": "string", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "QData", "addr": "(XB)", "loc": "e,154:7,154:15", "dtypep": "(XB)", "keyword": "QData", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "integer", "addr": "(P)", "loc": "e,23:4,23:11", "dtypep": "(P)", "keyword": "integer", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(BD)", "loc": "e,25:20,25:27", "dtypep": "(BD)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(FB)", "loc": "e,23:16,23:17", "dtypep": "(FB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "VlTriggerVec", "addr": "(T)", "loc": "e,7:8,7:9", "dtypep": "(T)", "keyword": "VlTriggerVec", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(DE)", "loc": "e,7:8,7:9", "dtypep": "(DE)", "keyword": "logic", "range": "63:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(N)", "loc": "e,7:8,7:9", "dtypep": "(N)", "keyword": "bit", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "bit", "addr": "(R)", "loc": "e,7:8,7:9", "dtypep": "(R)", "keyword": "bit", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(PB)", "loc": "e,27:22,27:25", "dtypep": "(PB)", "keyword": "logic", "range": "31:0", "generic": true, "rangep": []}, {"type": "BASICDTYPE", "name": "logic", "addr": "(NK)", "loc": "e,22:10,22:13", "dtypep": "(NK)", "keyword": "logic", "range": "7:0", "generic": true, "rangep": []}]}, {"type": "CONSTPOOL", "name": "", "addr": "(D)", "loc": "a,0:0,0:0", "modulep": [{"type": "MODULE", "name": "@CONST-POOL@", "addr": "(SM)", "loc": "a,0:0,0:0", "origName": "@CONST-POOL@", "level": 0, "modPublic": false, "inLibrary": false, "dead": false, "recursiveClone": false, "recursive": false, "timeunit": "NONE", "inlinesp": [], "stmtsp": [{"type": "SCOPE", "name": "TOP", "addr": "(TM)", "loc": "a,0:0,0:0", "aboveScopep": "UNLINKED", "aboveCellp": "UNLINKED", "modp": "(SM)", "varsp": [], "blocksp": []}], "activesp": []}]}]}