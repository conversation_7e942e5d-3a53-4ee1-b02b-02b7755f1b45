%Error: t/t_hier_block0_bad.v:21:11: 'sub0' has hier_block metacomment, hierarchical Verilation supports only integer/floating point/string and type param parameters
                                   : ... note: In instance 't'
   21 |    sub0 #(UNPACKED) i_sub0(.clk(clk), .in(8'(count)), .out(out0));
      |           ^~~~~~~~
%Error: t/t_hier_block0_bad.v:26:42: Cannot access inside hierarchical block
   26 |       $display("%d %d %d", count, i_sub0.ff, out1);
      |                                          ^~
%Error: Exiting due to
