//      // verilator_coverage annotation
        // DESCRIPTION: Verilator: Verilog Test module
        //
        // This file ONLY is placed under the Creative Commons Public Domain, for
        // any use, without warranty, 2008 by <PERSON>.
        // SPDX-License-Identifier: CC0-1.0
        
        module t (/*AUTOARG*/
           // Inputs
           clk
           );
        
           input clk;
        
           reg   toggle;
%000001    initial toggle=0;
-000001  point: comment=block
        
           integer cyc;
%000001    initial cyc=1;
-000001  point: comment=block
        
           wire [7:0] cyc_copy = cyc[7:0];
        
           alpha a1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
           alpha a2 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
           beta  b1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
           beta  b2 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
           tsk   t1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
           off   o1 (/*AUTOINST*/
                     // Inputs
                     .clk                       (clk),
                     .toggle                    (toggle));
        
 000010    always @ (posedge clk) begin
+000010  point: comment=block
%000000       if (cyc!=0) begin
+000010  point: comment=if
-000000  point: comment=else
 000010          cyc <= cyc + 1;
+000010  point: comment=if
 000010          toggle <= '0;
+000010  point: comment=if
                 // Single and multiline if
%000001          if (cyc==3) $write("");
-000001  point: comment=if
-000009  point: comment=else
%000001          if (cyc==3)
-000001  point: comment=if
-000009  point: comment=else
%000001            begin
-000001  point: comment=if
%000001               $write("");
-000001  point: comment=if
                   end
                 // Single and multiline else
%000001          if (cyc==3) ; else $write("");
-000001  point: comment=if
-000009  point: comment=else
%000001          if (cyc==3) ;
-000001  point: comment=if
-000009  point: comment=else
                 else
%000009            begin
-000009  point: comment=else
%000009               $write("");
-000009  point: comment=else
                   end
                 // Single and multiline if else
%000001          if (cyc==3) $write(""); else $write("");
-000001  point: comment=if
-000009  point: comment=else
%000001          if (cyc==3)
-000001  point: comment=if
-000009  point: comment=else
%000001            begin
-000001  point: comment=if
%000001               $write("");
-000001  point: comment=if
                   end
                 else
%000009            begin
-000009  point: comment=else
%000009               $write("");
-000009  point: comment=else
                   end
                 //  multiline elseif
%000001          if (cyc==3)
-000001  point: comment=elsif
%000001            begin
-000001  point: comment=elsif
%000001               $write("");
-000001  point: comment=elsif
                   end
%000001          else if (cyc==4)
-000001  point: comment=elsif
%000001            begin
-000001  point: comment=elsif
%000001               $write("");
-000001  point: comment=elsif
                   end
%000001          else if (cyc==5)
-000001  point: comment=if
-000007  point: comment=else
%000001            begin
-000001  point: comment=if
%000001               $write("");
-000001  point: comment=if
                   end
                 else
%000007            begin
-000007  point: comment=else
%000007               $write("");
-000007  point: comment=else
                   end
                 // Single and multiline while
%000000          while (0);
-000000  point: comment=block
%000000          while (0) begin
-000000  point: comment=block
%000000             $write("");
-000000  point: comment=block
                 end
%000000          do ; while (0);
-000000  point: comment=block
%000000          do begin
+000010  point: comment=if
-000000  point: comment=block
%000000             $write("");
+000010  point: comment=if
-000000  point: comment=block
%000000          end while (0);
-000000  point: comment=block
                 //===
                 // Task and complicated
%000001          if (cyc==3) begin
-000001  point: comment=elsif
%000001             toggle <= '1;
-000001  point: comment=elsif
                 end
%000001          else if (cyc==5) begin
-000001  point: comment=elsif
        `ifdef VERILATOR
%000001             $c("this->call_task();");
-000001  point: comment=elsif
        `else
                    call_task();
        `endif
                 end
%000001          else if (cyc==10) begin
-000001  point: comment=if
-000007  point: comment=else
%000001             $write("*-* All Finished *-*\n");
-000001  point: comment=if
%000001             $finish;
-000001  point: comment=if
                 end
              end
           end
        
%000001    task call_task;
-000001  point: comment=block
              /* verilator public */
%000001       t1.center_task(1'b1);
-000001  point: comment=block
           endtask
        
        endmodule
        
        module alpha (/*AUTOARG*/
           // Inputs
           clk, toggle
           );
           input clk;
           input toggle;
 000020    always @ (posedge clk) begin
+000020  point: comment=block
%000002       if (toggle) begin  // CHECK_COVER(0,"top.t.a*",2)
-000002  point: comment=if
+000018  point: comment=else
%000002          $write("");
-000002  point: comment=if
                 // t.a1 and t.a2 collapse to a count of 2
              end
 000018       if (toggle) begin
+000018  point: comment=else
                 $write("");  // CHECK_COVER_MISSING(0)
                 // This doesn't even get added
        `ifdef ATTRIBUTE
                 // verilator coverage_block_off
        `endif
              end
           end
        endmodule
        
        module beta (/*AUTOARG*/
           // Inputs
           clk, toggle
           );
           input clk;
           input toggle;
        
           /* verilator public_module */
        
 000020    always @ (posedge clk) begin
+000020  point: comment=block
 000020       $write("");  // Always covered
+000020  point: comment=block
%000000       if (0) begin  // CHECK_COVER(0,"top.t.b*",0)
-000000  point: comment=if
+000020  point: comment=else
                 // Make sure that we don't optimize away zero buckets
%000000          $write("");
-000000  point: comment=if
              end
%000002       if (toggle) begin  // CHECK_COVER(0,"top.t.b*",2)
-000002  point: comment=if
+000018  point: comment=else
                 // t.b1 and t.b2 collapse to a count of 2
%000002          $write("");
-000002  point: comment=if
              end
 000018       if (toggle) begin : block
+000018  point: comment=else
                 // This doesn't
        `ifdef ATTRIBUTE
                 // verilator coverage_block_off
        `endif
                 begin end  // Needed for .vlt to attach coverage_block_off
                 if (1) begin end  // CHECK_COVER_MISSING(0)
                 $write("");  // CHECK_COVER_MISSING(0)
              end
           end
        endmodule
        
        class Cls;
           bit m_toggle;
%000001    function new(bit toggle);
-000001  point: comment=block
%000001       m_toggle = toggle;
-000001  point: comment=block
%000000       if (m_toggle) begin  // CHECK_COVER(0,"top.$unit::Cls",1)
-000001  point: comment=if
-000000  point: comment=else
%000001          $write("");
-000001  point: comment=if
              end
           endfunction
 000011    static function void fstatic(bit toggle);
+000011  point: comment=block
%000000       if (1) begin  // CHECK_COVER(0,"top.$unit::Cls",1)
+000011  point: comment=if
-000000  point: comment=else
 000011          $write("");
+000011  point: comment=if
              end
           endfunction
 000011    function void fauto();
+000011  point: comment=block
%000000       if (m_toggle) begin  // CHECK_COVER(0,"top.$unit::Cls",1)
+000011  point: comment=if
-000000  point: comment=else
 000011          $write("");
+000011  point: comment=if
              end
           endfunction
        endclass
        
        module tsk (/*AUTOARG*/
           // Inputs
           clk, toggle
           );
           input clk;
           input toggle;
        
           /* verilator public_module */
        
 000010    always @ (posedge clk) begin
+000010  point: comment=block
 000010       center_task(1'b0);
+000010  point: comment=block
           end
        
 000011    task center_task;
+000011  point: comment=block
              input external;
 000011       begin
+000011  point: comment=block
%000001          if (toggle) begin  // CHECK_COVER(0,"top.t.t1",1)
-000001  point: comment=if
+000010  point: comment=else
%000001             $write("");
-000001  point: comment=if
                 end
%000001          if (external) begin  // CHECK_COVER(0,"top.t.t1",1)
-000001  point: comment=if
+000010  point: comment=else
%000001             $write("[%0t] Got external pulse\n", $time);
-000001  point: comment=if
                 end
              end
 000011       begin
+000011  point: comment=block
%000001          Cls c = new(1'b1);
-000001  point: comment=block
 000011          c.fauto();
+000011  point: comment=block
 000011          Cls::fstatic(1'b1);
+000011  point: comment=block
              end
           endtask
        
        endmodule
        
        module off (/*AUTOARG*/
           // Inputs
           clk, toggle
           );
           input clk;
           input toggle;
        
           // verilator coverage_off
           always @ (posedge clk) begin
              if (toggle) begin
                 $write("");  // CHECK_COVER_MISSING(0)
                 // because under coverage_module_off
              end
           end
           // verilator coverage_on
 000010    always @ (posedge clk) begin
+000010  point: comment=block
%000001       if (toggle) begin
-000001  point: comment=if
-000009  point: comment=else
                 // because under coverage_module_off
%000001          $write("");
-000001  point: comment=if
%000000          if (0) ;  // CHECK_COVER(0,"top.t.o1",1)
-000000  point: comment=if
-000001  point: comment=else
              end
           end
        
        endmodule
        
