%Error: "TestClass::cm_ae_NO_ANNOTATION(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    []                                   TestClass::cm_ae_NO_ANNOTATION(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure]                      TestClass::cm_ae_NO_ANNOTATION(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                            TestClass::cm_ae_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, acquire]             TestClass::cm_ae_VL_ACQUIRE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                            TestClass::cm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, acquire]             TestClass::cm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                           TestClass::cm_ae_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, excludes]            TestClass::cm_ae_VL_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe]                            TestClass::cm_ae_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure]                      TestClass::cm_ae_VL_MT_SAFE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                           TestClass::cm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, excludes]            TestClass::cm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe_postinit]                   TestClass::cm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, mt_safe_postinit, pure]    TestClass::cm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [mt_start]                           TestClass::cm_ae_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_start, mt_safe, pure]            TestClass::cm_ae_VL_MT_START(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                          TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, mt_unsafe, pure]           TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                      TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, mt_unsafe_one, pure]       TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [pure]                               TestClass::cm_ae_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure]                      TestClass::cm_ae_VL_PURE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [release]                            TestClass::cm_ae_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, release]             TestClass::cm_ae_VL_RELEASE(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [release]                            TestClass::cm_ae_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, release]             TestClass::cm_ae_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "TestClass::cm_ae_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:204:    [requires]                           TestClass::cm_ae_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:146:  [mt_safe, pure, requires]            TestClass::cm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:155:  [mt_safe]                            TestClass::cm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe, pure]             TestClass::cm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe_one, pure]         TestClass::cm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:155:  [mt_start]                           TestClass::cm_test_caller_smethod_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe, pure]             TestClass::cm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe_one, pure]         TestClass::cm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:155:  [pure]                               TestClass::cm_test_caller_smethod_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    []                                     TestClass::cm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [acquire]                              TestClass::cm_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [acquire]                              TestClass::cm_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [excludes]                             TestClass::cm_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_safe]                              TestClass::cm_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [excludes]                             TestClass::cm_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_safe_postinit]                     TestClass::cm_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_start]                             TestClass::cm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [release]                              TestClass::cm_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [release]                              TestClass::cm_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [requires]                             TestClass::cm_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    []                                     TestClass::cm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [acquire]                              TestClass::cm_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [acquire]                              TestClass::cm_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [excludes]                             TestClass::cm_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_safe]                              TestClass::cm_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [excludes]                             TestClass::cm_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_safe_postinit]                     TestClass::cm_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_start]                             TestClass::cm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [release]                              TestClass::cm_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [release]                              TestClass::cm_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [requires]                             TestClass::cm_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    []                                     TestClass::cm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                              TestClass::cm_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                              TestClass::cm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                             TestClass::cm_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe]                              TestClass::cm_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                             TestClass::cm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe_postinit]                     TestClass::cm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_start]                             TestClass::cm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [release]                              TestClass::cm_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [release]                              TestClass::cm_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [requires]                             TestClass::cm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:212:    [mt_safe]                            TestClass::cm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe, pure]             TestClass::cm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe_one, pure]         TestClass::cm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:212:    [mt_start]                           TestClass::cm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe, pure]             TestClass::cm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:209:    [mt_safe, mt_unsafe_one, pure]         TestClass::cm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:212:    [pure]                               TestClass::cm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    []                                     TestClass::cm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [acquire]                              TestClass::cm_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [acquire]                              TestClass::cm_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [excludes]                             TestClass::cm_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_safe]                              TestClass::cm_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [excludes]                             TestClass::cm_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_safe_postinit]                     TestClass::cm_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_start]                             TestClass::cm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe]                            TestClass::cm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [mt_unsafe_one]                        TestClass::cm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [release]                              TestClass::cm_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [release]                              TestClass::cm_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:191:    [requires]                             TestClass::cm_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:195:    []                                     TestClass::cm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    []                                     TestClass::cm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [acquire]                              TestClass::cm_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [acquire]                              TestClass::cm_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [excludes]                             TestClass::cm_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_safe]                              TestClass::cm_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [excludes]                             TestClass::cm_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_safe_postinit]                     TestClass::cm_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_start]                             TestClass::cm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe]                            TestClass::cm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [mt_unsafe_one]                        TestClass::cm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [release]                              TestClass::cm_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [release]                              TestClass::cm_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:199:    [requires]                             TestClass::cm_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    []                                     TestClass::cm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                              TestClass::cm_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [acquire]                              TestClass::cm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                             TestClass::cm_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe]                              TestClass::cm_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [excludes]                             TestClass::cm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_safe_postinit]                     TestClass::cm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_start]                             TestClass::cm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe]                            TestClass::cm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [mt_unsafe_one]                        TestClass::cm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [release]                              TestClass::cm_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [release]                              TestClass::cm_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:204:    [requires]                             TestClass::cm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [acquire]                            TestClass::cm_ua_VL_ACQUIRE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [acquire]                            TestClass::cm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [excludes]                           TestClass::cm_ua_VL_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [mt_safe]                            TestClass::cm_ua_VL_MT_SAFE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [excludes]                           TestClass::cm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [mt_safe_postinit]                   TestClass::cm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [mt_start]                           TestClass::cm_ua_VL_MT_START(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [mt_unsafe]                          TestClass::cm_ua_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [mt_unsafe_one]                      TestClass::cm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [pure]                               TestClass::cm_ua_VL_PURE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [release]                            TestClass::cm_ua_VL_RELEASE(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [release]                            TestClass::cm_ua_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "TestClass::cm_ua_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:195:    []                                   TestClass::cm_ua_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:137:  [requires]                           TestClass::cm_ua_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::guarded_by_test_fail()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:269:    [mt_safe]                            TestClass::guarded_by_test_fail()
t/t_dist_attributes/mt_enabled.h:104:    []                                     GuardMe::safe_if_guarded_or_local()
t/t_dist_attributes/mt_enabled.h:106:    []                                     GuardMe::operator int()
t/t_dist_attributes/mt_enabled.h:108:    []                                     GuardMe::operator+=(int)

%Error: "TestClass::icm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:175:  [mt_safe]                            TestClass::icm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::icm_test_caller_smethod_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:175:  [mt_start]                           TestClass::icm_test_caller_smethod_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::icm_test_caller_smethod_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:175:  [pure]                               TestClass::icm_test_caller_smethod_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    []                                     TestClass::icm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [acquire]                              TestClass::icm_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [acquire]                              TestClass::icm_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [excludes]                             TestClass::icm_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_safe]                              TestClass::icm_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [excludes]                             TestClass::icm_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_safe_postinit]                     TestClass::icm_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_start]                             TestClass::icm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [release]                              TestClass::icm_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [release]                              TestClass::icm_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [requires]                             TestClass::icm_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::icm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:238:    [mt_safe]                            TestClass::icm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::icm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:238:    [mt_start]                           TestClass::icm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::icm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:238:    [pure]                               TestClass::icm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    []                                     TestClass::icm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [acquire]                              TestClass::icm_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [acquire]                              TestClass::icm_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [excludes]                             TestClass::icm_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_safe]                              TestClass::icm_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [excludes]                             TestClass::icm_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_safe_postinit]                     TestClass::icm_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_start]                             TestClass::icm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe]                            TestClass::icm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [mt_unsafe_one]                        TestClass::icm_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [release]                              TestClass::icm_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [release]                              TestClass::icm_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:235:    [requires]                             TestClass::icm_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:119:  [mt_safe]                            TestClass::iscm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:119:  [mt_start]                           TestClass::iscm_test_caller_smethod_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:119:  [pure]                               TestClass::iscm_test_caller_smethod_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [acquire]                              TestClass::iscm_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [acquire]                              TestClass::iscm_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [excludes]                             TestClass::iscm_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_safe]                              TestClass::iscm_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [excludes]                             TestClass::iscm_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_safe_postinit]                     TestClass::iscm_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [release]                              TestClass::iscm_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [release]                              TestClass::iscm_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [requires]                             TestClass::iscm_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:173:    [mt_safe]                            TestClass::iscm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:173:    [mt_start]                           TestClass::iscm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::iscm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:173:    [pure]                               TestClass::iscm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    []                                     TestClass::iscm_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [acquire]                              TestClass::iscm_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [acquire]                              TestClass::iscm_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [excludes]                             TestClass::iscm_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_safe]                              TestClass::iscm_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [excludes]                             TestClass::iscm_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_safe_postinit]                     TestClass::iscm_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_start]                             TestClass::iscm_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe]                            TestClass::iscm_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [mt_unsafe_one]                        TestClass::iscm_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [release]                              TestClass::iscm_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [release]                              TestClass::iscm_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:170:    [requires]                             TestClass::iscm_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    []                                   TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure]                      TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                            TestClass::scm_ae_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, acquire]             TestClass::scm_ae_VL_ACQUIRE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                            TestClass::scm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, acquire]             TestClass::scm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                           TestClass::scm_ae_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, excludes]            TestClass::scm_ae_VL_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe]                            TestClass::scm_ae_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure]                      TestClass::scm_ae_VL_MT_SAFE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                           TestClass::scm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, excludes]            TestClass::scm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe_postinit]                   TestClass::scm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, mt_safe_postinit, pure]    TestClass::scm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                           TestClass::scm_ae_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_start, mt_safe, pure]            TestClass::scm_ae_VL_MT_START(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                          TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, mt_unsafe, pure]           TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                      TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, mt_unsafe_one, pure]       TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [pure]                               TestClass::scm_ae_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure]                      TestClass::scm_ae_VL_PURE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [release]                            TestClass::scm_ae_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, release]             TestClass::scm_ae_VL_RELEASE(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [release]                            TestClass::scm_ae_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, release]             TestClass::scm_ae_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "TestClass::scm_ae_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:133:    [requires]                           TestClass::scm_ae_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:84:   [mt_safe, pure, requires]            TestClass::scm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:93:   [mt_safe]                            TestClass::scm_test_caller_smethod_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe, pure]             TestClass::scm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe_one, pure]         TestClass::scm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:93:   [mt_start]                           TestClass::scm_test_caller_smethod_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe, pure]             TestClass::scm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe_one, pure]         TestClass::scm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:93:   [pure]                               TestClass::scm_test_caller_smethod_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [acquire]                              TestClass::scm_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [acquire]                              TestClass::scm_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [excludes]                             TestClass::scm_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_safe]                              TestClass::scm_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [excludes]                             TestClass::scm_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_safe_postinit]                     TestClass::scm_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [release]                              TestClass::scm_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [release]                              TestClass::scm_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [requires]                             TestClass::scm_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [acquire]                              TestClass::scm_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [acquire]                              TestClass::scm_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [excludes]                             TestClass::scm_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_safe]                              TestClass::scm_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [excludes]                             TestClass::scm_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_safe_postinit]                     TestClass::scm_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [release]                              TestClass::scm_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [release]                              TestClass::scm_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [requires]                             TestClass::scm_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                              TestClass::scm_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                              TestClass::scm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                             TestClass::scm_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe]                              TestClass::scm_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                             TestClass::scm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe_postinit]                     TestClass::scm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [release]                              TestClass::scm_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [release]                              TestClass::scm_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [requires]                             TestClass::scm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:141:    [mt_safe]                            TestClass::scm_test_caller_smethod_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe, pure]             TestClass::scm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe_one, pure]         TestClass::scm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:141:    [mt_start]                           TestClass::scm_test_caller_smethod_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe, pure]             TestClass::scm_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:138:    [mt_safe, mt_unsafe_one, pure]         TestClass::scm_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:141:    [pure]                               TestClass::scm_test_caller_smethod_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    []                                     TestClass::scm_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [acquire]                              TestClass::scm_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [acquire]                              TestClass::scm_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [excludes]                             TestClass::scm_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_safe]                              TestClass::scm_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [excludes]                             TestClass::scm_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_safe_postinit]                     TestClass::scm_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_start]                             TestClass::scm_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe]                            TestClass::scm_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [mt_unsafe_one]                        TestClass::scm_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [release]                              TestClass::scm_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [release]                              TestClass::scm_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:120:    [requires]                             TestClass::scm_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:124:    []                                     TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    []                                     TestClass::scm_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [acquire]                              TestClass::scm_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [acquire]                              TestClass::scm_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [excludes]                             TestClass::scm_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_safe]                              TestClass::scm_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [excludes]                             TestClass::scm_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_safe_postinit]                     TestClass::scm_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_start]                             TestClass::scm_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe]                            TestClass::scm_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [mt_unsafe_one]                        TestClass::scm_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [release]                              TestClass::scm_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [release]                              TestClass::scm_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:128:    [requires]                             TestClass::scm_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    []                                     TestClass::scm_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                              TestClass::scm_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [acquire]                              TestClass::scm_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                             TestClass::scm_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe]                              TestClass::scm_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [excludes]                             TestClass::scm_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_safe_postinit]                     TestClass::scm_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_start]                             TestClass::scm_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe]                            TestClass::scm_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [mt_unsafe_one]                        TestClass::scm_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [release]                              TestClass::scm_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [release]                              TestClass::scm_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:133:    [requires]                             TestClass::scm_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [acquire]                            TestClass::scm_ua_VL_ACQUIRE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [acquire]                            TestClass::scm_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [excludes]                           TestClass::scm_ua_VL_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [mt_safe]                            TestClass::scm_ua_VL_MT_SAFE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [excludes]                           TestClass::scm_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [mt_safe_postinit]                   TestClass::scm_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [mt_start]                           TestClass::scm_ua_VL_MT_START(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [mt_unsafe]                          TestClass::scm_ua_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [mt_unsafe_one]                      TestClass::scm_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [pure]                               TestClass::scm_ua_VL_PURE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [release]                            TestClass::scm_ua_VL_RELEASE(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [release]                            TestClass::scm_ua_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:124:    []                                   TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:75:   [requires]                           TestClass::scm_ua_VL_REQUIRES(VerilatedMutex &)

%Error: "TestClassConstructor::safe_function_calls_constructor_global_object_bad()" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:405:    [stable_tree]                        TestClassConstructor::safe_function_calls_constructor_global_object_bad()
t/t_dist_attributes/mt_enabled.h:355:    []                                     DummyClass::dummy_function()

%Error: "TestClassConstructor::safe_function_calls_constructor_global_object_member_bad()" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:408:    [stable_tree]                        TestClassConstructor::safe_function_calls_constructor_global_object_member_bad()
t/t_dist_attributes/mt_enabled.h:350:    []                                     DummyClass2::dummy_function2()

%Error: "TestClassConstructor::safe_function_calls_constructor_local_calls_class_global_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:402:    [mt_safe]                            TestClassConstructor::safe_function_calls_constructor_local_calls_class_global_bad()
t/t_dist_attributes/mt_enabled.h:280:    []                                     StaticClass::static_class_function()

%Error: "TestClassConstructor::safe_function_calls_constructor_local_calls_global_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:399:    [mt_safe]                            TestClassConstructor::safe_function_calls_constructor_local_calls_global_bad()
t/t_dist_attributes/mt_enabled.h:276:    []                                     static_function()

%Error: "TestClassConstructor::safe_function_calls_constructor_with_unsafepointer_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:391:    [mt_safe]                            TestClassConstructor::safe_function_calls_constructor_with_unsafepointer_bad()
t/t_dist_attributes/mt_enabled.h:311:    [mt_unsafe]                            UnsafeFunction::unsafe_function()

%Error: "TestClassConstructor::safe_function_calls_constructor_with_unsafereference_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:395:    [mt_safe]                            TestClassConstructor::safe_function_calls_constructor_with_unsafereference_bad()
t/t_dist_attributes/mt_enabled.h:311:    [mt_unsafe]                            UnsafeFunction::unsafe_function()

%Error: "TestClassConstructor::safe_function_local_function_global_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:377:    [mt_safe]                            TestClassConstructor::safe_function_local_function_global_bad()
t/t_dist_attributes/mt_enabled.h:276:    []                                     static_function()

%Error: "TestClassConstructor::safe_function_static_constructor_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:374:    [mt_safe]                            TestClassConstructor::safe_function_static_constructor_bad()
t/t_dist_attributes/mt_enabled.h:276:    []                                     static_function()

%Error: "TestClassConstructor::safe_function_unsafe_constructor_bad()" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:371:    [mt_safe]                            TestClassConstructor::safe_function_unsafe_constructor_bad()
t/t_dist_attributes/mt_enabled.h:285:    [mt_unsafe]                            ConstructorCallsUnsafeLocalFunction::unsafe_function()

%Error: "UnannotatedMtDisabledClass::unannotatedMtDisabledMethodBad()" defined in a file marked as VL_MT_DISABLED_CODE_UNIT has declaration(s) without VL_MT_DISABLED annotation
t/t_dist_attributes/mt_disabled.cpp:23:  [mt_disabled, requires]              UnannotatedMtDisabledClass::unannotatedMtDisabledMethodBad()
t/t_dist_attributes/mt_disabled.h:29:    []                                     UnannotatedMtDisabledClass::unannotatedMtDisabledMethodBad()

%Error: "UnannotatedMtDisabledClass::unannotatedMtDisabledStaticMethodBad()" defined in a file marked as VL_MT_DISABLED_CODE_UNIT has declaration(s) without VL_MT_DISABLED annotation
t/t_dist_attributes/mt_disabled.cpp:26:  [mt_disabled, requires]              UnannotatedMtDisabledClass::unannotatedMtDisabledStaticMethodBad()
t/t_dist_attributes/mt_disabled.h:30:    []                                     UnannotatedMtDisabledClass::unannotatedMtDisabledStaticMethodBad()

%Error: "ifh_test_caller_func_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:53:   [mt_safe]                            ifh_test_caller_func_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "ifh_test_caller_func_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:53:   [mt_start]                           ifh_test_caller_func_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "ifh_test_caller_func_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:53:   [pure]                               ifh_test_caller_func_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [acquire]                              ifh_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [acquire]                              ifh_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [excludes]                             ifh_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_safe]                              ifh_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [excludes]                             ifh_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_safe_postinit]                     ifh_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [release]                              ifh_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [release]                              ifh_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [requires]                             ifh_VL_REQUIRES(VerilatedMutex &)

%Error: "ifh_test_caller_func_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:97:     [mt_safe]                            ifh_test_caller_func_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "ifh_test_caller_func_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:97:     [mt_start]                           ifh_test_caller_func_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "ifh_test_caller_func_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:97:     [pure]                               ifh_test_caller_func_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     []                                     ifh_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [acquire]                              ifh_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [acquire]                              ifh_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [excludes]                             ifh_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_safe]                              ifh_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [excludes]                             ifh_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_safe_postinit]                     ifh_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_start]                             ifh_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe]                            ifh_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [mt_unsafe_one]                        ifh_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [release]                              ifh_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [release]                              ifh_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:94:     [requires]                             ifh_VL_REQUIRES(VerilatedMutex &)

%Error: "nsf_ae_NO_ANNOTATION(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     []                                   nsf_ae_NO_ANNOTATION(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure]                      nsf_ae_NO_ANNOTATION(VerilatedMutex &)

%Error: "nsf_ae_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                            nsf_ae_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, acquire]             nsf_ae_VL_ACQUIRE(VerilatedMutex &)

%Error: "nsf_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                            nsf_ae_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, acquire]             nsf_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "nsf_ae_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                           nsf_ae_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, excludes]            nsf_ae_VL_EXCLUDES(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe]                            nsf_ae_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure]                      nsf_ae_VL_MT_SAFE(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                           nsf_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, excludes]            nsf_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe_postinit]                   nsf_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, mt_safe_postinit, pure]    nsf_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                           nsf_ae_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_start, mt_safe, pure]            nsf_ae_VL_MT_START(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                          nsf_ae_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, mt_unsafe, pure]           nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                      nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, mt_unsafe_one, pure]       nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_ae_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [pure]                               nsf_ae_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure]                      nsf_ae_VL_PURE(VerilatedMutex &)

%Error: "nsf_ae_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [release]                            nsf_ae_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, release]             nsf_ae_VL_RELEASE(VerilatedMutex &)

%Error: "nsf_ae_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [release]                            nsf_ae_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, release]             nsf_ae_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "nsf_ae_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:75:     [requires]                           nsf_ae_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:34:   [mt_safe, pure, requires]            nsf_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "nsf_test_caller_func_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:42:   [mt_safe]                            nsf_test_caller_func_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe, pure]             nsf_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe_one, pure]         nsf_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_test_caller_func_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:42:   [mt_start]                           nsf_test_caller_func_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe, pure]             nsf_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe_one, pure]         nsf_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_test_caller_func_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:42:   [pure]                               nsf_test_caller_func_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [acquire]                              nsf_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [acquire]                              nsf_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [excludes]                             nsf_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_safe]                              nsf_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [excludes]                             nsf_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_safe_postinit]                     nsf_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [release]                              nsf_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [release]                              nsf_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [requires]                             nsf_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [acquire]                              nsf_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [acquire]                              nsf_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [excludes]                             nsf_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_safe]                              nsf_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [excludes]                             nsf_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_safe_postinit]                     nsf_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [release]                              nsf_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [release]                              nsf_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [requires]                             nsf_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                              nsf_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                              nsf_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                             nsf_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe]                              nsf_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                             nsf_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe_postinit]                     nsf_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [release]                              nsf_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [release]                              nsf_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [requires]                             nsf_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "nsf_test_caller_func_hdr_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.h:83:     [mt_safe]                            nsf_test_caller_func_hdr_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe, pure]             nsf_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe_one, pure]         nsf_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_test_caller_func_hdr_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.h:83:     [mt_start]                           nsf_test_caller_func_hdr_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe, pure]             nsf_ea_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:80:     [mt_safe, mt_unsafe_one, pure]         nsf_ea_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_test_caller_func_hdr_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.h:83:     [pure]                               nsf_test_caller_func_hdr_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     []                                     nsf_au_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [acquire]                              nsf_au_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [acquire]                              nsf_au_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [excludes]                             nsf_au_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_safe]                              nsf_au_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [excludes]                             nsf_au_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_safe_postinit]                     nsf_au_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_start]                             nsf_au_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe]                            nsf_au_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [mt_unsafe_one]                        nsf_au_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [release]                              nsf_au_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [release]                              nsf_au_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:62:     [requires]                             nsf_au_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:66:     []                                     nsf_ua_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     []                                     nsf_aa_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [acquire]                              nsf_aa_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [acquire]                              nsf_aa_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [excludes]                             nsf_aa_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_safe]                              nsf_aa_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [excludes]                             nsf_aa_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_safe_postinit]                     nsf_aa_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_start]                             nsf_aa_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe]                            nsf_aa_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [mt_unsafe_one]                        nsf_aa_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [release]                              nsf_aa_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [release]                              nsf_aa_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:70:     [requires]                             nsf_aa_VL_REQUIRES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     []                                     nsf_ae_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                              nsf_ae_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [acquire]                              nsf_ae_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                             nsf_ae_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe]                              nsf_ae_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [excludes]                             nsf_ae_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_safe_postinit]                     nsf_ae_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_start]                             nsf_ae_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe]                            nsf_ae_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [mt_unsafe_one]                        nsf_ae_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [release]                              nsf_ae_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [release]                              nsf_ae_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.h:75:     [requires]                             nsf_ae_VL_REQUIRES(VerilatedMutex &)

%Error: "nsf_ua_VL_ACQUIRE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_ACQUIRE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [acquire]                            nsf_ua_VL_ACQUIRE(VerilatedMutex &)

%Error: "nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [acquire]                            nsf_ua_VL_ACQUIRE_SHARED(VerilatedMutex &)

%Error: "nsf_ua_VL_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [excludes]                           nsf_ua_VL_EXCLUDES(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_SAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_SAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [mt_safe]                            nsf_ua_VL_MT_SAFE(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [excludes]                           nsf_ua_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [mt_safe_postinit]                   nsf_ua_VL_MT_SAFE_POSTINIT(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_START(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_START(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [mt_start]                           nsf_ua_VL_MT_START(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_UNSAFE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [mt_unsafe]                          nsf_ua_VL_MT_UNSAFE(VerilatedMutex &)

%Error: "nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [mt_unsafe_one]                      nsf_ua_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "nsf_ua_VL_PURE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_PURE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [pure]                               nsf_ua_VL_PURE(VerilatedMutex &)

%Error: "nsf_ua_VL_RELEASE(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_RELEASE(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [release]                            nsf_ua_VL_RELEASE(VerilatedMutex &)

%Error: "nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [release]                            nsf_ua_VL_RELEASE_SHARED(VerilatedMutex &)

%Error: "nsf_ua_VL_REQUIRES(VerilatedMutex &)" declaration does not match definition
t/t_dist_attributes/mt_enabled.h:66:     []                                   nsf_ua_VL_REQUIRES(VerilatedMutex &) [declaration]
t/t_dist_attributes/mt_enabled.cpp:25:   [requires]                           nsf_ua_VL_REQUIRES(VerilatedMutex &)

%Error: "sfc_test_caller_func_VL_MT_SAFE(VerilatedMutex &)" is mtsafe but calls non-mtsafe function(s)
t/t_dist_attributes/mt_enabled.cpp:63:   [mt_safe]                            sfc_test_caller_func_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   []                                     sfc_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_start]                             sfc_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe]                            sfc_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe_one]                        sfc_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "sfc_test_caller_func_VL_MT_START(VerilatedMutex &)" is stable_tree but calls non-stable_tree or non-mtsafe
t/t_dist_attributes/mt_enabled.cpp:63:   [mt_start]                           sfc_test_caller_func_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   []                                     sfc_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_start]                             sfc_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe]                            sfc_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe_one]                        sfc_VL_MT_UNSAFE_ONE(VerilatedMutex &)

%Error: "sfc_test_caller_func_VL_PURE(VerilatedMutex &)" is pure but calls non-pure function(s)
t/t_dist_attributes/mt_enabled.cpp:63:   [pure]                               sfc_test_caller_func_VL_PURE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   []                                     sfc_NO_ANNOTATION(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [acquire]                              sfc_VL_ACQUIRE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [acquire]                              sfc_VL_ACQUIRE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [excludes]                             sfc_VL_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_safe]                              sfc_VL_MT_SAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [excludes]                             sfc_VL_MT_SAFE_EXCLUDES(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_safe_postinit]                     sfc_VL_MT_SAFE_POSTINIT(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_start]                             sfc_VL_MT_START(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe]                            sfc_VL_MT_UNSAFE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [mt_unsafe_one]                        sfc_VL_MT_UNSAFE_ONE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [release]                              sfc_VL_RELEASE(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [release]                              sfc_VL_RELEASE_SHARED(VerilatedMutex &)
t/t_dist_attributes/mt_enabled.cpp:60:   [requires]                             sfc_VL_REQUIRES(VerilatedMutex &)

%Error: "unannotatedMtDisabledFunctionBad()" defined in a file marked as VL_MT_DISABLED_CODE_UNIT has declaration(s) without VL_MT_DISABLED annotation
t/t_dist_attributes/mt_disabled.cpp:20:  [mt_disabled, requires]              unannotatedMtDisabledFunctionBad()
t/t_dist_attributes/mt_disabled.h:22:    []                                     unannotatedMtDisabledFunctionBad()
t/t_dist_attributes/mt_disabled.h:25:    []                                     unannotatedMtDisabledFunctionBad()
Number of functions reported unsafe: 229
