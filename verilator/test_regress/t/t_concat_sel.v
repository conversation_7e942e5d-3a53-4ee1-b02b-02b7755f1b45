// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed under the Creative Commons Public Domain, for
// any use, without warranty, 2019 by <PERSON>.
// SPDX-License-Identifier: CC0-1.0

module t (/*AUTOARG*/
   // Inputs
   clk
   );
   input clk;

   integer      cyc = 0;
   reg [63:0]   crc;
   reg [63:0]   sum;

   // Take CRC data and apply to testblock inputs
   wire [3:0]  a = crc[3:0];
   wire [3:0]  b = crc[19:16];

   // TEST
   wire [3:0]  out1 = {a,b}[2 +: 4];
   wire [3:0]  out2 = {a,b}[5 -: 4];
   wire [3:0]  out3 = {a,b}[5 : 2];
   wire [0:0]  out4 = {a,b}[2];

   // Aggregate outputs into a single result vector
   wire [63:0] result = {51'h0, out4, out3, out2, out1};

   initial begin
      if ({16'h1234}[0] != 1'b0) $stop;
      if ({16'h1234}[2] != 1'b1) $stop;
      if ({16'h1234}[11:4] != 8'h23) $stop;
      if ({16'h1234}[4+:8] != 8'h23) $stop;
      if ({16'h1234}[11-:8] != 8'h23) $stop;
      if ({8'h12, 8'h34}[0] != 1'b0) $stop;
      if ({8'h12, 8'h34}[2] != 1'b1) $stop;
      if ({8'h12, 8'h34}[11:4] != 8'h23) $stop;
      if ({8'h12, 8'h34}[4+:8] != 8'h23) $stop;
      if ({8'h12, 8'h34}[11-:8] != 8'h23) $stop;
      $write("*-* All Finished *-*\n");
      $finish;
   end

   // Test loop
   always @ (posedge clk) begin
`ifdef TEST_VERBOSE
      $write("[%0t] cyc==%0d crc=%x result=%x\n", $time, cyc, crc, result);
`endif
      cyc <= cyc + 1;
      crc <= {crc[62:0], crc[63] ^ crc[2] ^ crc[0]};
      sum <= result ^ {sum[62:0], sum[63] ^ sum[2] ^ sum[0]};
      if (cyc==0) begin
         // Setup
         crc <= 64'h5aef0c8d_d70a4497;
         sum <= '0;
      end
      else if (cyc<10) begin
         sum <= '0;
      end
      else if (cyc<90) begin
      end
      else if (cyc==99) begin
         $write("[%0t] cyc==%0d crc=%x sum=%x\n", $time, cyc, crc, sum);
         if (crc !== 64'hc77bb9b3784ea091) $stop;
         // What checksum will we end up with (above print should match)
`define EXPECTED_SUM 64'h4afe43fb79d7b71e
         if (sum !== `EXPECTED_SUM) $stop;
         $write("*-* All Finished *-*\n");
         $finish;
      end
   end

endmodule
