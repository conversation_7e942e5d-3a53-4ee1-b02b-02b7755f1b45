%Warning-CASEOVERLAP: t/t_case_overlap_bad.v:20:21: Case conditions overlap (example pattern 0x6)
   20 |             3'b11?, 3'b???: v++;
      |                     ^~~~~~
                      t/t_case_overlap_bad.v:20:13: ... Location of overlapping condition
   20 |             3'b11?, 3'b???: v++;
      |             ^~~~~~
                      ... For warning description see https://verilator.org/warn/CASEOVERLAP?v=latest
                      ... Use "/* verilator lint_off CASEOVERLAP */" and lint_on around source to disable this message.
%Warning-CASEOVERLAP: t/t_case_overlap_bad.v:25:13: Case conditions overlap
   25 |             3'b001, 3'b000: $stop;
      |             ^~~~~~
                      t/t_case_overlap_bad.v:24:13: ... Location of overlapping condition
   24 |             3'b00?: $stop;
      |             ^~~~~~
%Warning-CASEOVERLAP: t/t_case_overlap_bad.v:30:13: Case conditions overlap (example pattern 0x7)
   30 |             3'b11?: $stop;
      |             ^~~~~~
                      t/t_case_overlap_bad.v:29:13: ... Location of overlapping condition
   29 |             3'b111, 3'b0??: v++;
      |             ^~~~~~
%Warning-CASEOVERLAP: t/t_case_overlap_bad.v:35:13: Case conditions overlap
   35 |             3'b001: $stop;
      |             ^~~~~~
                      t/t_case_overlap_bad.v:34:21: ... Location of overlapping condition
   34 |             3'b000, 3'b001, 3'b010, 3'b011: v++;
      |                     ^~~~~~
%Warning-CASEOVERLAP: t/t_case_overlap_bad.v:40:13: Case conditions overlap
   40 |             3'b011: $stop;
      |             ^~~~~~
                      t/t_case_overlap_bad.v:39:37: ... Location of overlapping condition
   39 |             3'b000, 3'b001, 3'b010, 3'b011: v++;
      |                                     ^~~~~~
%Error: Exiting due to
