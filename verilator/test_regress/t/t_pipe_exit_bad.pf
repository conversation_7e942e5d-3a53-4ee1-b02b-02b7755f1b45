#!/usr/bin/env python3
# pylint: disable=C0114
#
# DESCRIPTION: Verilator: Verilog Test example --pipe-filter script
#
# Copyright 2010 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0

import sys

sys.exit("%Error: t_pipe_exit_bad.pf: Intentional bad exit status...")
