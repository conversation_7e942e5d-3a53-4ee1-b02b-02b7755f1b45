%Warning-COMBDLY: t/t_lint_latch_5.v:13:13: Non-blocking assignment '<=' in combinational logic process
                                          : ... This will be executed as a blocking assignment '='!
   13 |        z[0] <= a[0];
      |             ^~
                  ... For warning description see https://verilator.org/warn/COMBDLY?v=latest
                  ... Use "/* verilator lint_off COMBDLY */" and lint_on around source to disable this message.
                  *** See https://verilator.org/warn/COMBDLY before disabling this,
                  else you may end up with different sim results.
%Warning-COMBDLY: t/t_lint_latch_5.v:17:13: Non-blocking assignment '<=' in combinational logic process
                                          : ... This will be executed as a blocking assignment '='!
   17 |        z[1] <= a[1];
      |             ^~
%Error: Exiting due to
