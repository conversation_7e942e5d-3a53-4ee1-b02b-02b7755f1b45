%Warning-REDEFMACRO: t/t_pp_dupdef.v:11:20: Redefining existing define: 'DUP', with different value: 'barney'
                     t/t_pp_dupdef.v:11:20: ... Location of previous definition, with value: 'fred'
                     ... For warning description see https://verilator.org/warn/REDEFMACRO?v=latest
                     ... Use "/* verilator lint_off REDEFMACRO */" and lint_on around source to disable this message.
%Warning-REDEFMACRO: t/t_pp_dupdef.v:14:33: Redefining existing define: 'DUPP', with different value: 'paramed(x,z) (x*z)'
                     t/t_pp_dupdef.v:14:33: ... Location of previous definition, with value: 'paramed(x) (x)'
%Error: Exiting due to
