// DESCRIPTION: Verilator: Verilog Test module
//
// This file ONLY is placed into the Public Domain, for any use,
// without warranty, 2021 by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.
// SPDX-License-Identifier: CC0-1.0

`define stop $stop
`define checkd(gotv,expv) do if ((gotv) !== (expv)) begin $write("%%Error: %s:%0d:  got=%0d exp=%0d\n", `__FILE__,`__LINE__, (gotv), (expv)); `stop; end while(0);

package config_pkg;
   typedef struct packed {
      int UPPER0;
      int UPPER2;
      int USE_QUAD0;
      int USE_QUAD1;
      int USE_QUAD2;
   } config_struct;

endpackage : config_pkg

module t;
    import config_pkg::*;

    struct_submodule #(.MY_CONFIG('{
                                       UPPER0: 10,
                                       UPPER2: 20,
                                       USE_QUAD0: 4,
                                       USE_QUAD1: 5,
                                       USE_QUAD2: 6
                                    })) a_submodule_I ();
endmodule : t

module struct_submodule
  import config_pkg::*;
   #(parameter config_struct MY_CONFIG = '0);

   initial begin
      `checkd(MY_CONFIG.UPPER0, 10);
      `checkd(MY_CONFIG.USE_QUAD0, 4);
      `checkd(MY_CONFIG.USE_QUAD1, 5);
      `checkd(MY_CONFIG.USE_QUAD2, 6);
      `checkd(MY_CONFIG.UPPER2, 20);
      $write("*-* All Finished *-*\n");
      $finish;
   end
endmodule : struct_submodule
