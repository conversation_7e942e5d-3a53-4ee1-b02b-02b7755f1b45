$date
	Thu Jan 25 08:09:51 2024

$end
$version
	fstWriter
$end
$timescale
	1ps
$end
$scope module top $end
$attrbegin misc 07 t.enumed_t 4 ZERO ONE TWO THREE 00000000000000000000000000000000 00000000000000000000000000000001 00000000000000000000000000000010 00000000000000000000000000000011 1 $end
$attrbegin misc 07 t.enumb_t 4 BZERO BONE BTWO BTHREE 000 001 010 011 2 $end
$scope module $unit $end
$var bit 1 ! global_bit $end
$upscope $end
$var wire 1 " clk $end
$scope module t $end
$var wire 1 " clk $end
$var integer 32 # cyc [31:0] $end
$scope struct v_strp $end
$var bit 1 $ b1 $end
$var bit 1 % b0 $end
$upscope $end
$scope struct v_strp_strp $end
$scope struct x1 $end
$var bit 1 & b1 $end
$var bit 1 ' b0 $end
$upscope $end
$scope struct x0 $end
$var bit 1 ( b1 $end
$var bit 1 ) b0 $end
$upscope $end
$upscope $end
$scope union v_unip_strp $end
$scope struct x1 $end
$var bit 1 * b1 $end
$var bit 1 + b0 $end
$upscope $end
$scope struct x0 $end
$var bit 1 * b1 $end
$var bit 1 + b0 $end
$upscope $end
$upscope $end
$var bit 2 , v_arrp [2:1] $end
$var bit 2 - v_arrp_arrp[3] [2:1] $end
$var bit 2 . v_arrp_arrp[4] [2:1] $end
$scope struct v_arrp_strp[3] $end
$var bit 1 / b1 $end
$var bit 1 0 b0 $end
$upscope $end
$scope struct v_arrp_strp[4] $end
$var bit 1 1 b1 $end
$var bit 1 2 b0 $end
$upscope $end
$var bit 1 3 v_arru[1] $end
$var bit 1 4 v_arru[2] $end
$var bit 1 5 v_arru_arru[3][1] $end
$var bit 1 6 v_arru_arru[3][2] $end
$var bit 1 7 v_arru_arru[4][1] $end
$var bit 1 8 v_arru_arru[4][2] $end
$var bit 2 9 v_arru_arrp[3] [2:1] $end
$var bit 2 : v_arru_arrp[4] [2:1] $end
$scope struct v_arru_strp[3] $end
$var bit 1 ; b1 $end
$var bit 1 < b0 $end
$upscope $end
$scope struct v_arru_strp[4] $end
$var bit 1 = b1 $end
$var bit 1 > b0 $end
$upscope $end
$var real 64 ? v_real $end
$var real 64 @ v_arr_real[0] $end
$var real 64 A v_arr_real[1] $end
$var longint 64 B v_chandle [63:0] $end
$scope struct v_str32x2[0] $end
$var logic 32 C data [31:0] $end
$upscope $end
$scope struct v_str32x2[1] $end
$var logic 32 D data [31:0] $end
$upscope $end
$attrbegin misc 07 "" 1 $end
$var int 32 E v_enumed [31:0] $end
$attrbegin misc 07 "" 1 $end
$var int 32 F v_enumed2 [31:0] $end
$attrbegin misc 07 "" 2 $end
$var logic 3 G v_enumb [2:0] $end
$scope struct v_enumb2_str $end
$attrbegin misc 07 "" 2 $end
$var logic 3 H a [2:0] $end
$attrbegin misc 07 "" 2 $end
$var logic 3 I b [2:0] $end
$upscope $end
$var logic 8 J unpacked_array[-2] [7:0] $end
$var logic 8 K unpacked_array[-1] [7:0] $end
$var logic 8 L unpacked_array[0] [7:0] $end
$var bit 1 M LONGSTART_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_a_very_long_name_which_will_get_hashed_LONGEND $end
$scope module unnamedblk1 $end
$var integer 32 N b [31:0] $end
$scope module unnamedblk2 $end
$var integer 32 O a [31:0] $end
$upscope $end
$upscope $end
$upscope $end
$upscope $end
$enddefinitions $end
#0
$dumpvars
b00000000000000000000000000000000 O
b00000000000000000000000000000000 N
0M
b00000000 L
b00000000 K
b00000000 J
b000 I
b000 H
b000 G
b00000000000000000000000000000000 F
b00000000000000000000000000000000 E
b00000000000000000000000000000000 D
b00000000000000000000000011111111 C
b0000000000000000000000000000000000000000000000000000000000000000 B
r0 A
r0 @
r0 ?
0>
0=
0<
0;
b00 :
b00 9
08
07
06
05
04
03
02
01
00
0/
b00 .
b00 -
b00 ,
0+
0*
0)
0(
0'
0&
0%
0$
b00000000000000000000000000000000 #
0"
1!
$end
#10
1"
b00000000000000000000000000000001 #
1$
1%
1&
1'
1(
1)
1*
1+
b11 ,
b11 -
b11 .
1/
10
11
12
b11 9
b11 :
1;
1<
1=
1>
r0.1 ?
r0.2 @
r0.3 A
b00000000000000000000000011111110 C
b00000000000000000000000000000001 D
b00000000000000000000000000000001 E
b00000000000000000000000000000010 F
b111 G
b00000000000000000000000000000101 N
b00000000000000000000000000000101 O
#15
0"
#20
1"
b110 G
b00000000000000000000000000000100 F
b00000000000000000000000000000010 E
b00000000000000000000000000000010 D
b00000000000000000000000011111101 C
r0.6 A
r0.4 @
r0.2 ?
0>
0=
0<
0;
b00 :
b00 9
02
01
00
0/
b00 .
b00 -
b00 ,
0+
0*
0)
0(
0'
0&
0%
0$
b00000000000000000000000000000010 #
b111 H
b111 I
#25
0"
#30
1"
b110 I
b110 H
b00000000000000000000000000000011 #
1$
1%
1&
1'
1(
1)
1*
1+
b11 ,
b11 -
b11 .
1/
10
11
12
b11 9
b11 :
1;
1<
1=
1>
r0.3 ?
r0.6000000000000001 @
r0.8999999999999999 A
b00000000000000000000000011111100 C
b00000000000000000000000000000011 D
b00000000000000000000000000000011 E
b00000000000000000000000000000110 F
b101 G
#35
0"
#40
1"
b100 G
b00000000000000000000000000001000 F
b00000000000000000000000000000100 E
b00000000000000000000000000000100 D
b00000000000000000000000011111011 C
r1.2 A
r0.8 @
r0.4 ?
0>
0=
0<
0;
b00 :
b00 9
02
01
00
0/
b00 .
b00 -
b00 ,
0+
0*
0)
0(
0'
0&
0%
0$
b00000000000000000000000000000100 #
b101 H
b101 I
#45
0"
#50
1"
b100 I
b100 H
b00000000000000000000000000000101 #
1$
1%
1&
1'
1(
1)
1*
1+
b11 ,
b11 -
b11 .
1/
10
11
12
b11 9
b11 :
1;
1<
1=
1>
r0.5 ?
r1 @
r1.5 A
b00000000000000000000000011111010 C
b00000000000000000000000000000101 D
b00000000000000000000000000000101 E
b00000000000000000000000000001010 F
b011 G
#55
0"
#60
1"
b010 G
b00000000000000000000000000001100 F
b00000000000000000000000000000110 E
b00000000000000000000000000000110 D
b00000000000000000000000011111001 C
r1.8 A
r1.2 @
r0.6 ?
0>
0=
0<
0;
b00 :
b00 9
02
01
00
0/
b00 .
b00 -
b00 ,
0+
0*
0)
0(
0'
0&
0%
0$
b00000000000000000000000000000110 #
b011 H
b011 I
