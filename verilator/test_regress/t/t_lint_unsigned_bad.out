%Warning-UNSIGNED: t/t_lint_unsigned_bad.v:13:15: Comparison is constant due to unsigned arithmetic
                                                : ... note: In instance 't'
   13 |       if (uns < 0) $stop;
      |               ^
                   ... For warning description see https://verilator.org/warn/UNSIGNED?v=latest
                   ... Use "/* verilator lint_off UNSIGNED */" and lint_on around source to disable this message.
%Error: Exiting due to
