/* -*- C++ -*- */
/**************************************************************************
 * DESCRIPTION: Verilator: Flex input file
 *
 * Code available from: https://verilator.org
 *
 **************************************************************************
 *
 * Copyright 2003-2024 by <PERSON>.  Verilator is free software; you
 * can redistribute it and/or modify it under the terms of either the
 * GNU Lesser General Public License Version 3 or the Perl Artistic License
 * Version 2.0.
 * SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
 *
 *************************************************************************/
/* clang-format off */

%option interactive c++ stack noyywrap
%{
/* %option nodefault */
#ifdef NEVER_JUST_FOR_CLANG_FORMAT
 }
#endif
// clang-format on

#include "V3Number.h"
#include "V3ParseBison.h"  // Generated by bison
#include "V3ParseImp.h"  // Defines YYTYPE; before including bison header

#define STATE_VERILOG_RECENT S23  // State name for most recent Verilog Version

#define PARSEP V3ParseImp::parsep()

#define YY_INPUT(buf, result, max_size) \
    do { result = PARSEP->flexPpInputToLex(buf, max_size); } while (false)

//======================================================================

#define FL_FWD (PARSEP->lexFileline()->forwardToken(yytext, yyleng, true))
// Use this to break between tokens whereever not return'ing a token (e.g. skipping inside lexer)
#define FL_BRK (PARSEP->lexFileline()->startToken())

#define CRELINE() (PARSEP->lexFileline()->copyOrSameFileLineApplied())

#define FL \
    do { \
        FL_FWD; \
        yylval.fl = CRELINE(); \
    } while (false)

#define ERROR_RSVD_WORD(language) \
    do { \
        FL; \
        yylval.fl->v3warn(E_UNSUPPORTED, "Unsupported: " << language \
                                                         << " reserved word not implemented: '" \
                                                         << yytext << "'"); \
        FL_BRK; \
    } while (0)

//======================================================================

static double lexParseDouble(FileLine* fl, const char* textp, size_t length) {
    const string text = std::string{textp, length};
    bool success = false;
    const double d = VString::parseDouble(text, &success);
    if (!success) fl->v3error("Syntax error parsing real: '" << textp << "'");
    return d;
}

// clang-format off
/**********************************************************************/
%}

%e 2000
%p 5000
%n 2500
%k 1000
%a 15000
%o 25000

%s V95 V01NC V01C V05 S05 S09 S12 S17 S23
%s ATTRMODE QQQ STRING TABLE
%s VA5 SAX VLT
%s SYSCHDR SYSCINT SYSCIMP SYSCIMPH SYSCCTOR SYSCDTOR

ws      [ \t\f\r]+
wsnr    [ \t\f]+
crnl    [\r]*[\n]
id      [a-zA-Z_][a-zA-Z0-9_$]*
escid   \\[^ \t\f\r\n]+
word    [a-zA-Z0-9_]+
vnum1   [0-9]*?[''][sS]?[bcodhBCODH][ \t\n]*[A-Fa-f0-9xXzZ_?]*
vnum2   [0-9]*?[''][sS]?[01xXzZ]
vnum3   [0-9][_0-9]*[ \t\n]*[''][sS]?[bcodhBCODH]?[ \t\n]*[A-Fa-f0-9xXzZ_?]+
vnum4   [0-9][_0-9]*[ \t\n]*[''][sS]?[bcodhBCODH]
vnum5   [0-9][_0-9]*[ \t\n]*[''][sS]
vnum    {vnum1}|{vnum2}|{vnum3}|{vnum4}|{vnum5}

%%

<INITIAL>.|\n           { BEGIN STATE_VERILOG_RECENT; yyless(0); }

  /************************************************************************/
  /* Verilator control files */
<VLT>{
  {ws}                  { FL_FWD; FL_BRK; }  /* otherwise ignore white-space */
  {crnl}                { FL_FWD; FL_BRK; }  /* Count line numbers */

  "clock_enable"        { FL; return yVLT_CLOCK_ENABLE; }
  "clocker"             { FL; return yVLT_CLOCKER; }
  "coverage_block_off"  { FL; return yVLT_COVERAGE_BLOCK_OFF; }
  "coverage_off"        { FL; return yVLT_COVERAGE_OFF; }
  "coverage_on"         { FL; return yVLT_COVERAGE_ON; }
  "forceable"           { FL; return yVLT_FORCEABLE; }
  "full_case"           { FL; return yVLT_FULL_CASE; }
  "hier_block"          { FL; return yVLT_HIER_BLOCK; }
  "hier_params"         { FL; return yVLT_HIER_PARAMS; }
  "inline"              { FL; return yVLT_INLINE; }
  "isolate_assignments" { FL; return yVLT_ISOLATE_ASSIGNMENTS; }
  "lint_off"            { FL; return yVLT_LINT_OFF; }
  "lint_on"             { FL; return yVLT_LINT_ON; }
  "no_clocker"          { FL; return yVLT_NO_CLOCKER; }
  "no_inline"           { FL; return yVLT_NO_INLINE; }
  "parallel_case"       { FL; return yVLT_PARALLEL_CASE; }
  "profile_data"        { FL; return yVLT_PROFILE_DATA; }
  "public"              { FL; return yVLT_PUBLIC; }
  "public_flat"         { FL; return yVLT_PUBLIC_FLAT; }
  "public_flat_rd"      { FL; return yVLT_PUBLIC_FLAT_RD; }
  "public_flat_rw"      { FL; return yVLT_PUBLIC_FLAT_RW; }
  "public_module"       { FL; return yVLT_PUBLIC_MODULE; }
  "sc_bv"               { FL; return yVLT_SC_BV; }
  "sformat"             { FL; return yVLT_SFORMAT; }
  "split_var"           { FL; return yVLT_SPLIT_VAR; }
  "timing_off"          { FL; return yVLT_TIMING_OFF; }
  "timing_on"           { FL; return yVLT_TIMING_ON; }
  "tracing_off"         { FL; return yVLT_TRACING_OFF; }
  "tracing_on"          { FL; return yVLT_TRACING_ON; }

  -?"-block"            { FL; return yVLT_D_BLOCK; }
  -?"-cost"             { FL; return yVLT_D_COST; }
  -?"-file"             { FL; return yVLT_D_FILE; }
  -?"-function"         { FL; return yVLT_D_FUNCTION; }
  -?"-levels"           { FL; return yVLT_D_LEVELS; }
  -?"-lines"            { FL; return yVLT_D_LINES; }
  -?"-match"            { FL; return yVLT_D_MATCH; }
  -?"-model"            { FL; return yVLT_D_MODEL; }
  -?"-module"           { FL; return yVLT_D_MODULE; }
  -?"-mtask"            { FL; return yVLT_D_MTASK; }
  -?"-rule"             { FL; return yVLT_D_RULE; }
  -?"-scope"            { FL; return yVLT_D_SCOPE; }
  -?"-task"             { FL; return yVLT_D_TASK; }
  -?"-var"              { FL; return yVLT_D_VAR; }

  /* Reachable by attr_event_control */
  "edge"                { FL; return yEDGE; }
  "negedge"             { FL; return yNEGEDGE; }
  "or"                  { FL; return yOR; }
  "posedge"             { FL; return yPOSEDGE; }
}

  /************************************************************************/
  /* Verilog 1995 */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  {ws}                  { FL_FWD; FL_BRK; }  /* otherwise ignore white-space */
  {crnl}                { FL_FWD; FL_BRK; }  /* Count line numbers */
  /*     Extensions to Verilog set, some specified by PSL */
  "$c"[0-9]*            { FL; return yD_C; }  /*Verilator only*/
  /*     System Tasks */
  "$acos"               { FL; return yD_ACOS; }
  "$acosh"              { FL; return yD_ACOSH; }
  "$asin"               { FL; return yD_ASIN; }
  "$asinh"              { FL; return yD_ASINH; }
  "$atan"               { FL; return yD_ATAN; }
  "$atan2"              { FL; return yD_ATAN2; }
  "$atanh"              { FL; return yD_ATANH; }
  "$bitstoreal"         { FL; return yD_BITSTOREAL; }
  "$bitstoshortreal"    { FL; return yD_BITSTOSHORTREAL; }
  "$cast"               { FL; return yD_CAST; }
  "$ceil"               { FL; return yD_CEIL; }
  "$cos"                { FL; return yD_COS; }
  "$cosh"               { FL; return yD_COSH; }
  "$display"            { FL; return yD_DISPLAY; }
  "$displayb"           { FL; return yD_DISPLAYB; }
  "$displayh"           { FL; return yD_DISPLAYH; }
  "$displayo"           { FL; return yD_DISPLAYO; }
  "$dist_chi_square"    { FL; return yD_DIST_CHI_SQUARE; }
  "$dist_erlang"        { FL; return yD_DIST_ERLANG; }
  "$dist_exponential"   { FL; return yD_DIST_EXPONENTIAL; }
  "$dist_normal"        { FL; return yD_DIST_NORMAL; }
  "$dist_poisson"       { FL; return yD_DIST_POISSON; }
  "$dist_t"             { FL; return yD_DIST_T; }
  "$dist_uniform"       { FL; return yD_DIST_UNIFORM; }
  "$dumpall"            { FL; return yD_DUMPALL; }
  "$dumpfile"           { FL; return yD_DUMPFILE; }
  "$dumpflush"          { FL; return yD_DUMPFLUSH; }
  "$dumplimit"          { FL; return yD_DUMPLIMIT; }
  "$dumpoff"            { FL; return yD_DUMPOFF; }
  "$dumpon"             { FL; return yD_DUMPON; }
  "$dumpports"          { FL; return yD_DUMPPORTS; }
  "$dumpportsall"       { FL; return yD_DUMPALL; }
  "$dumpportsflush"     { FL; return yD_DUMPFLUSH; }
  "$dumpportslimit"     { FL; return yD_DUMPLIMIT; }
  "$dumpportsoff"       { FL; return yD_DUMPOFF; }
  "$dumpportson"        { FL; return yD_DUMPON; }
  "$dumpvars"           { FL; return yD_DUMPVARS; }
  "$exit"               { FL; return yD_EXIT; }
  "$exp"                { FL; return yD_EXP; }
  "$fclose"             { FL; return yD_FCLOSE; }
  "$fdisplay"           { FL; return yD_FDISPLAY; }
  "$fdisplayb"          { FL; return yD_FDISPLAYB; }
  "$fdisplayh"          { FL; return yD_FDISPLAYH; }
  "$fdisplayo"          { FL; return yD_FDISPLAYO; }
  "$feof"               { FL; return yD_FEOF; }
  "$ferror"             { FL; return yD_FERROR; }
  "$fflush"             { FL; return yD_FFLUSH; }
  "$fgetc"              { FL; return yD_FGETC; }
  "$fgets"              { FL; return yD_FGETS; }
  "$finish"             { FL; return yD_FINISH; }
  "$floor"              { FL; return yD_FLOOR; }
  "$fmonitor"           { FL; return yD_FMONITOR; }
  "$fmonitorb"          { FL; return yD_FMONITORB; }
  "$fmonitorh"          { FL; return yD_FMONITORH; }
  "$fmonitoro"          { FL; return yD_FMONITORO; }
  "$fopen"              { FL; return yD_FOPEN; }
  "$fread"              { FL; return yD_FREAD; }
  "$frewind"            { FL; return yD_FREWIND; }
  "$fscanf"             { FL; return yD_FSCANF; }
  "$fseek"              { FL; return yD_FSEEK; }
  "$fstrobe"            { FL; return yD_FSTROBE; }
  "$fstrobeb"           { FL; return yD_FSTROBEB; }
  "$fstrobeh"           { FL; return yD_FSTROBEH; }
  "$fstrobeo"           { FL; return yD_FSTROBEO; }
  "$ftell"              { FL; return yD_FTELL; }
  "$fullskew"           { FL; return yaTIMINGSPEC; }
  "$fwrite"             { FL; return yD_FWRITE; }
  "$fwriteb"            { FL; return yD_FWRITEB; }
  "$fwriteh"            { FL; return yD_FWRITEH; }
  "$fwriteo"            { FL; return yD_FWRITEO; }
  "$global_clock"       { FL; return yD_GLOBAL_CLOCK; }
  "$hold"               { FL; return yaTIMINGSPEC; }
  "$hypot"              { FL; return yD_HYPOT; }
  "$itor"               { FL; return yD_ITOR; }
  "$ln"                 { FL; return yD_LN; }
  "$log10"              { FL; return yD_LOG10; }
  "$monitor"            { FL; return yD_MONITOR; }
  "$monitorb"           { FL; return yD_MONITORB; }
  "$monitorh"           { FL; return yD_MONITORH; }
  "$monitoro"           { FL; return yD_MONITORO; }
  "$monitoroff"         { FL; return yD_MONITOROFF; }
  "$monitoron"          { FL; return yD_MONITORON; }
  "$nochange"           { FL; return yaTIMINGSPEC; }
  "$period"             { FL; return yaTIMINGSPEC; }
  "$pow"                { FL; return yD_POW; }
  "$printtimescale"     { FL; return yD_PRINTTIMESCALE; }
  "$psprintf"           { FL; yylval.fl->v3warn(NONSTD, "Non-standard system function '" << yytext << "';"
                                                " suggest use standard '$sformatf' (IEEE 1800-2023 21.3.3)");
                          return yD_SFORMATF; }
  "$random"             { FL; return yD_RANDOM; }
  "$readmemb"           { FL; return yD_READMEMB; }
  "$readmemh"           { FL; return yD_READMEMH; }
  "$realtime"           { FL; return yD_REALTIME; }
  "$realtobits"         { FL; return yD_REALTOBITS; }
  "$recovery"           { FL; return yaTIMINGSPEC; }
  "$recrem"             { FL; return yaTIMINGSPEC; }
  "$removal"            { FL; return yaTIMINGSPEC; }
  "$rewind"             { FL; return yD_REWIND; }
  "$rtoi"               { FL; return yD_RTOI; }
  "$sampled"            { FL; return yD_SAMPLED; }
  "$setup"              { FL; return yaTIMINGSPEC; }
  "$setuphold"          { FL; return yaTIMINGSPEC; }
  "$sformat"            { FL; return yD_SFORMAT; }
  "$sformatf"           { FL; return yD_SFORMATF; }
  "$shortrealtobits"    { FL; return yD_SHORTREALTOBITS; }
  "$sin"                { FL; return yD_SIN; }
  "$sinh"               { FL; return yD_SINH; }
  "$skew"               { FL; return yaTIMINGSPEC; }
  "$sqrt"               { FL; return yD_SQRT; }
  "$sscanf"             { FL; return yD_SSCANF; }
  "$stacktrace"         { FL; return yD_STACKTRACE; }
  "$stime"              { FL; return yD_STIME; }
  "$stop"               { FL; return yD_STOP; }
  "$strobe"             { FL; return yD_STROBE; }
  "$strobeb"            { FL; return yD_STROBEB; }
  "$strobeh"            { FL; return yD_STROBEH; }
  "$strobeo"            { FL; return yD_STROBEO; }
  "$swrite"             { FL; return yD_SWRITE; }
  "$swriteb"            { FL; return yD_SWRITEB; }
  "$swriteh"            { FL; return yD_SWRITEH; }
  "$swriteo"            { FL; return yD_SWRITEO; }
  "$system"             { FL; return yD_SYSTEM; }
  "$tan"                { FL; return yD_TAN; }
  "$tanh"               { FL; return yD_TANH; }
  "$test$plusargs"      { FL; return yD_TESTPLUSARGS; }
  "$time"               { FL; return yD_TIME; }
  "$timeformat"         { FL; return yD_TIMEFORMAT; }
  "$timeprecision"      { FL; return yD_TIMEPRECISION; }
  "$timeskew"           { FL; return yaTIMINGSPEC; }
  "$timeunit"           { FL; return yD_TIMEUNIT; }
  "$typename"           { FL; return yD_TYPENAME; }
  "$ungetc"             { FL; return yD_UNGETC; }
  "$urandom"            { FL; return yD_URANDOM; }
  "$urandom_range"      { FL; return yD_URANDOM_RANGE; }
  "$value$plusargs"     { FL; return yD_VALUEPLUSARGS; }
  "$width"              { FL; return yaTIMINGSPEC; }
  "$write"              { FL; return yD_WRITE; }
  "$writeb"             { FL; return yD_WRITEB; }
  "$writeh"             { FL; return yD_WRITEH; }
  "$writememb"          { FL; return yD_WRITEMEMB; }
  "$writememh"          { FL; return yD_WRITEMEMH; }
  "$writeo"             { FL; return yD_WRITEO; }
  /*     Keywords */
  "always"              { FL; return yALWAYS; }
  "and"                 { FL; return yAND; }
  "assign"              { FL; return yASSIGN; }
  "begin"               { FL; return yBEGIN; }
  "buf"                 { FL; return yBUF; }
  "bufif0"              { FL; return yBUFIF0; }
  "bufif1"              { FL; return yBUFIF1; }
  "case"                { FL; return yCASE; }
  "casex"               { FL; return yCASEX; }
  "casez"               { FL; return yCASEZ; }
  "cmos"                { FL; return yCMOS; }
  "deassign"            { FL; return yDEASSIGN; }
  "default"             { FL; return yDEFAULT; }
  "defparam"            { FL; return yDEFPARAM; }
  "disable"             { FL; return yDISABLE; }
  "edge"                { FL; return yEDGE; }
  "else"                { FL; return yELSE; }
  "end"                 { FL; return yEND; }
  "endcase"             { FL; return yENDCASE; }
  "endfunction"         { FL; return yENDFUNCTION; }
  "endmodule"           { FL; return yENDMODULE; }
  "endprimitive"        { FL; return yENDPRIMITIVE; }
  "endspecify"          { FL; return yENDSPECIFY; }
  "endtable"            { FL; yylval.fl->v3error("Syntax error: 'endtable' outside of 'table'"); FL_BRK; }
  "endtask"             { FL; return yENDTASK; }
  "event"               { FL; return yEVENT; }
  "for"                 { FL; return yFOR; }
  "force"               { FL; return yFORCE; }
  "foreach"             { FL; return yFOREACH; }
  "forever"             { FL; return yFOREVER; }
  "fork"                { FL; return yFORK; }
  "function"            { FL; return yFUNCTION; }
  "highz0"              { FL; return yHIGHZ0; }
  "highz1"              { FL; return yHIGHZ1; }
  "if"                  { FL; return yIF; }
  "initial"             { FL; return yINITIAL; }
  "inout"               { FL; return yINOUT; }
  "input"               { FL; return yINPUT; }
  "integer"             { FL; return yINTEGER; }
  "join"                { FL; return yJOIN; }
  "large"               { FL; return ygenSTRENGTH; }
  "macromodule"         { FL; return yMODULE; }
  "medium"              { FL; return ygenSTRENGTH; }
  "module"              { FL; return yMODULE; }
  "nand"                { FL; return yNAND; }
  "negedge"             { FL; return yNEGEDGE; }
  "nmos"                { FL; return yNMOS; }
  "nor"                 { FL; return yNOR; }
  "not"                 { FL; return yNOT; }
  "notif0"              { FL; return yNOTIF0; }
  "notif1"              { FL; return yNOTIF1; }
  "or"                  { FL; return yOR; }
  "output"              { FL; return yOUTPUT; }
  "parameter"           { FL; return yPARAMETER; }
  "pmos"                { FL; return yPMOS; }
  "posedge"             { FL; return yPOSEDGE; }
  "primitive"           { FL; return yPRIMITIVE; }
  "pull0"               { FL; return yPULL0; }
  "pull1"               { FL; return yPULL1; }
  "pulldown"            { FL; return yPULLDOWN; }
  "pullup"              { FL; return yPULLUP; }
  "rcmos"               { FL; return yRCMOS; }
  "real"                { FL; return yREAL; }
  "realtime"            { FL; return yREALTIME; }
  "reg"                 { FL; return yREG; }
  "release"             { FL; return yRELEASE; }
  "repeat"              { FL; return yREPEAT; }
  "rnmos"               { FL; return yRNMOS; }
  "rpmos"               { FL; return yRPMOS; }
  "rtran"               { FL; return yRTRAN; }
  "rtranif0"            { FL; return yRTRANIF0; }
  "rtranif1"            { FL; return yRTRANIF1; }
  "scalared"            { FL; return ySCALARED; }
  "small"               { FL; return ygenSTRENGTH; }
  "specify"             { FL; return ySPECIFY; }
  "specparam"           { FL; return ySPECPARAM; }
  "strong0"             { FL; return ySTRONG0; }
  "strong1"             { FL; return ySTRONG1; }
  "supply0"             { FL; return ySUPPLY0; }
  "supply1"             { FL; return ySUPPLY1; }
  "table"               { FL; yy_push_state(TABLE); return yTABLE; }
  "task"                { FL; return yTASK; }
  "time"                { FL; return yTIME; }
  "tran"                { FL; return yTRAN; }
  "tranif0"             { FL; return yTRANIF0; }
  "tranif1"             { FL; return yTRANIF1; }
  "tri"                 { FL; return yTRI; }
  "tri0"                { FL; return yTRI0; }
  "tri1"                { FL; return yTRI1; }
  "triand"              { FL; return yTRIAND; }
  "trior"               { FL; return yTRIOR; }
  "trireg"              { FL; return yTRIREG; }
  "vectored"            { FL; return yVECTORED; }
  "wait"                { FL; return yWAIT; }
  "wand"                { FL; return yWAND; }
  "weak0"               { FL; return yWEAK0; }
  "weak1"               { FL; return yWEAK1; }
  "while"               { FL; return yWHILE; }
  "wire"                { FL; return yWIRE; }
  "wor"                 { FL; return yWOR; }
  "xnor"                { FL; return yXNOR; }
  "xor"                 { FL; return yXOR; }
}

  /* Verilog 2001 */
<V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  /*     System Tasks */
  "$signed"             { FL; return yD_SIGNED; }
  "$unsigned"           { FL; return yD_UNSIGNED; }
  /*     Keywords */
  "automatic"           { FL; return yAUTOMATIC; }
  "endgenerate"         { FL; return yENDGENERATE; }
  "generate"            { FL; return yGENERATE; }
  "genvar"              { FL; return yGENVAR; }
  "ifnone"              { FL; return yaTIMINGSPEC; }
  "localparam"          { FL; return yLOCALPARAM; }
  "noshowcancelled"     { FL; return yaTIMINGSPEC; }
  "pulsestyle_ondetect" { FL; return yaTIMINGSPEC; }
  "pulsestyle_onevent"  { FL; return yaTIMINGSPEC; }
  "showcancelled"       { FL; return yaTIMINGSPEC; }
  "signed"              { FL; return ySIGNED; }
  "unsigned"            { FL; return yUNSIGNED; }
}

  /* Verilog 2001 Config */
<V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  /*     Generic unsupported keywords */
  "cell"                { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "config"              { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "design"              { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "endconfig"           { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "incdir"              { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "include"             { FL; yylval.fl->v3warn(E_UNSUPPORTED, "Unsupported: Verilog 2001-config reserved word not implemented;"
                                                " suggest you want `include instead: '" << yytext << "'");
                          FL_BRK; }
  "instance"            { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "liblist"             { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "library"             { ERROR_RSVD_WORD("Verilog 2001-config"); }
  "use"                 { ERROR_RSVD_WORD("Verilog 2001-config"); }
}

  /* Verilog 2005 */
<V05,S05,S09,S12,S17,S23,SAX>{
  /*     System Tasks */
  "$clog2"              { FL; return yD_CLOG2; }
  /*     Keywords */
  "uwire"               { FL; return yWIRE; }
}

  /* System Verilog 2005 */
<S05,S09,S12,S17,S23,SAX>{
  /*     System Tasks */
  "$bits"               { FL; return yD_BITS; }
  "$changed"            { FL; return yD_CHANGED; }
  "$changed_gclk"       { FL; return yD_CHANGED_GCLK; }
  "$countbits"          { FL; return yD_COUNTBITS; }
  "$countones"          { FL; return yD_COUNTONES; }
  "$dimensions"         { FL; return yD_DIMENSIONS; }
  "$error"              { FL; return yD_ERROR; }
  "$fatal"              { FL; return yD_FATAL; }
  "$fell"               { FL; return yD_FELL; }
  "$fell_gclk"          { FL; return yD_FELL_GCLK; }
  "$high"               { FL; return yD_HIGH; }
  "$increment"          { FL; return yD_INCREMENT; }
  "$info"               { FL; return yD_INFO; }
  "$isunbounded"        { FL; return yD_ISUNBOUNDED; }
  "$isunknown"          { FL; return yD_ISUNKNOWN; }
  "$left"               { FL; return yD_LEFT; }
  "$low"                { FL; return yD_LOW; }
  "$onehot"             { FL; return yD_ONEHOT; }
  "$onehot0"            { FL; return yD_ONEHOT0; }
  "$past"               { FL; return yD_PAST; }
  "$right"              { FL; return yD_RIGHT; }
  "$root"               { FL; return yD_ROOT; }
  "$rose"               { FL; return yD_ROSE; }
  "$rose_gclk"          { FL; return yD_ROSE_GCLK; }
  "$size"               { FL; return yD_SIZE; }
  "$stable"             { FL; return yD_STABLE; }
  "$stable_gclk"        { FL; return yD_STABLE_GCLK; }
  "$unpacked_dimensions" { FL; return yD_UNPACKED_DIMENSIONS; }
  "$warning"            { FL; return yD_WARNING; }
  /*     SV2005 Keywords */
  /* Note assert_strobe was in SystemVerilog 3.1, but removed for SystemVerilog 2005 */
  "$unit"               { FL; return yD_UNIT; }  /* Yes, a keyword, not task */
  "alias"               { FL; return yALIAS; }
  "always_comb"         { FL; return yALWAYS_COMB; }
  "always_ff"           { FL; return yALWAYS_FF; }
  "always_latch"        { FL; return yALWAYS_LATCH; }
  "assert"              { FL; return yASSERT; }
  "assume"              { FL; return yASSUME; }
  "before"              { FL; return yBEFORE; }
  "bind"                { FL; return yBIND; }
  "bins"                { FL; return yBINS; }
  "binsof"              { FL; return yBINSOF; }
  "bit"                 { FL; return yBIT; }
  "break"               { FL; return yBREAK; }
  "byte"                { FL; return yBYTE; }
  "chandle"             { FL; return yCHANDLE; }
  "class"               { FL; return yCLASS; }
  "clocking"            { FL; return yCLOCKING; }
  "const"               { FL; return yCONST__LEX; }
  "constraint"          { FL; return yCONSTRAINT; }
  "context"             { FL; return yCONTEXT; }
  "continue"            { FL; return yCONTINUE; }
  "cover"               { FL; return yCOVER; }
  "covergroup"          { FL; return yCOVERGROUP; }
  "coverpoint"          { FL; return yCOVERPOINT; }
  "cross"               { FL; return yCROSS; }
  "dist"                { FL; return yDIST; }
  "do"                  { FL; return yDO; }
  "endclass"            { FL; return yENDCLASS; }
  "endclocking"         { FL; return yENDCLOCKING; }
  "endgroup"            { FL; return yENDGROUP; }
  "endinterface"        { FL; return yENDINTERFACE; }
  "endpackage"          { FL; return yENDPACKAGE; }
  "endprogram"          { FL; return yENDPROGRAM; }
  "endproperty"         { FL; return yENDPROPERTY; }
  "endsequence"         { FL; return yENDSEQUENCE; }
  "enum"                { FL; return yENUM; }
  "expect"              { FL; return yEXPECT; }
  "export"              { FL; return yEXPORT; }
  "extends"             { FL; return yEXTENDS; }
  "extern"              { FL; return yEXTERN; }
  "final"               { FL; return yFINAL; }
  "first_match"         { FL; return yFIRST_MATCH; }
  "forkjoin"            { FL; return yFORKJOIN; }
  "iff"                 { FL; return yIFF; }
  "ignore_bins"         { FL; return yIGNORE_BINS; }
  "illegal_bins"        { FL; return yILLEGAL_BINS; }
  "import"              { FL; return yIMPORT; }
  "inside"              { FL; return yINSIDE; }
  "int"                 { FL; return yINT; }
  "interface"           { FL; return yINTERFACE; }
  "intersect"           { FL; return yINTERSECT; }
  "join_any"            { FL; return yJOIN_ANY; }
  "join_none"           { FL; return yJOIN_NONE; }
  "local"               { FL; return yLOCAL__LEX; }
  "logic"               { FL; return yLOGIC; }
  "longint"             { FL; return yLONGINT; }
  "matches"             { FL; return yMATCHES; }
  "modport"             { FL; return yMODPORT; }
  "new"                 { FL; return yNEW__LEX; }
  "null"                { FL; return yNULL; }
  "package"             { FL; return yPACKAGE; }
  "packed"              { FL; return yPACKED; }
  "priority"            { FL; return yPRIORITY; }
  "program"             { FL; return yPROGRAM; }
  "property"            { FL; return yPROPERTY; }
  "protected"           { FL; return yPROTECTED; }
  "pure"                { FL; return yPURE; }
  "rand"                { FL; return yRAND; }
  "randc"               { FL; return yRANDC; }
  "randcase"            { FL; return yRANDCASE; }
  "randomize"           { FL; return yRANDOMIZE; }
  "randsequence"        { FL; return yRANDSEQUENCE; }
  "ref"                 { FL; return yREF; }
  "restrict"            { FL; return yRESTRICT; }
  "return"              { FL; return yRETURN; }
  "sequence"            { FL; return ySEQUENCE; }
  "shortint"            { FL; return ySHORTINT; }
  "shortreal"           { FL; return ySHORTREAL; }
  "solve"               { FL; return ySOLVE; }
  "static"              { FL; return ySTATIC__LEX; }
  "string"              { FL; return ySTRING; }
  "struct"              { FL; return ySTRUCT; }
  "super"               { FL; return ySUPER; }
  "tagged"              { ERROR_RSVD_WORD("SystemVerilog 2005"); }
  "this"                { FL; return yTHIS; }
  "throughout"          { FL; return yTHROUGHOUT; }
  "timeprecision"       { FL; return yTIMEPRECISION; }
  "timeunit"            { FL; return yTIMEUNIT; }
  "type"                { FL; return yTYPE__LEX; }
  "typedef"             { FL; return yTYPEDEF; }
  "union"               { FL; return yUNION; }
  "unique"              { FL; return yUNIQUE; }
  "var"                 { FL; return yVAR; }
  "virtual"             { FL; return yVIRTUAL__LEX; }
  "void"                { FL; return yVOID; }
  "wait_order"          { FL; return yWAIT_ORDER; }
  "wildcard"            { FL; return yWILDCARD; }
  "with"                { FL; return yWITH__LEX; }
  "within"              { FL; return yWITHIN; }
}

  /* SystemVerilog 2009 */
<S09,S12,S17,S23,SAX>{
  /*     Keywords */
  "accept_on"           { FL; return yACCEPT_ON; }
  "checker"             { FL; return yCHECKER; }
  "endchecker"          { FL; return yENDCHECKER; }
  "eventually"          { FL; return yEVENTUALLY; }
  "global"              { FL; return yGLOBAL__LEX; }
  "implies"             { FL; return yIMPLIES; }
  "let"                 { FL; return yLET; }
  "nexttime"            { FL; return yNEXTTIME; }
  "reject_on"           { FL; return yREJECT_ON; }
  "s_always"            { FL; return yS_ALWAYS; }
  "s_eventually"        { FL; return yS_EVENTUALLY; }
  "s_nexttime"          { FL; return yS_NEXTTIME; }
  "s_until"             { FL; return yS_UNTIL; }
  "s_until_with"        { FL; return yS_UNTIL_WITH; }
  "strong"              { FL; return ySTRONG; }
  "sync_accept_on"      { FL; return ySYNC_ACCEPT_ON; }
  "sync_reject_on"      { FL; return ySYNC_REJECT_ON; }
  "unique0"             { FL; return yUNIQUE0; }
  "until"               { FL; return yUNTIL; }
  "until_with"          { FL; return yUNTIL_WITH; }
  "untyped"             { FL; return yUNTYPED; }
  "weak"                { FL; return yWEAK; }
}

  /* System Verilog 2012 */
<S12,S17,S23,SAX>{
  /*     System Tasks */
  "$assertcontrol"      { FL; return yD_ASSERTCTL; }
  "$assertkill"         { FL; return yD_ASSERTKILL; }
  "$assertoff"          { FL; return yD_ASSERTOFF; }
  "$asserton"           { FL; return yD_ASSERTON; }
  /*     Keywords */
  "implements"          { FL; return yIMPLEMENTS; }
  "interconnect"        { FL; return yINTERCONNECT; }
  "nettype"             { FL; return yNETTYPE; }
  "soft"                { FL; return ySOFT; }
}

  /* System Verilog 2017 */
  /* No new keywords */

  /* Default PLI rule */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  "$"[a-zA-Z0-9_$]+     { const string str (yytext, yyleng);
                                  yylval.strp = PARSEP->newString(AstNode::encodeName(str));
                                  FL; return yaD_PLI;
                                }
}

  /************************************************************************/
  /* AMS */

<VA5,SAX>{
  /*     Generic unsupported warnings */
   "1step"              { FL; return ya1STEP; }
   "above"              { ERROR_RSVD_WORD("AMS"); }
   "abs"                { ERROR_RSVD_WORD("AMS"); }
   "absdelay"           { ERROR_RSVD_WORD("AMS"); }
   "abstol"             { ERROR_RSVD_WORD("AMS"); }
   "ac_stim"            { ERROR_RSVD_WORD("AMS"); }
   "access"             { ERROR_RSVD_WORD("AMS"); }
   "acos"               { ERROR_RSVD_WORD("AMS"); }
   "acosh"              { ERROR_RSVD_WORD("AMS"); }
   "aliasparam"         { ERROR_RSVD_WORD("AMS"); }
   "analog"             { ERROR_RSVD_WORD("AMS"); }
   "analysis"           { ERROR_RSVD_WORD("AMS"); }
   "asin"               { FL; return yD_ASIN; }
   "asinh"              { FL; return yD_ASINH; }
   "assert"             { ERROR_RSVD_WORD("AMS"); }
   "atan"               { FL; return yD_ATAN; }
   "atan2"              { FL; return yD_ATAN2; }
   "atanh"              { FL; return yD_ATANH; }
   "branch"             { ERROR_RSVD_WORD("AMS"); }
   "ceil"               { FL; return yD_CEIL; }
   "connect"            { ERROR_RSVD_WORD("AMS"); }
   "connectmodule"      { ERROR_RSVD_WORD("AMS"); }
   "connectrules"       { ERROR_RSVD_WORD("AMS"); }
   "continuous"         { ERROR_RSVD_WORD("AMS"); }
   "cos"                { FL; return yD_COS; }
   "cosh"               { FL; return yD_COSH; }
   "cross"              { ERROR_RSVD_WORD("AMS"); }
   "ddt"                { ERROR_RSVD_WORD("AMS"); }
   "ddt_nature"         { ERROR_RSVD_WORD("AMS"); }
   "ddx"                { ERROR_RSVD_WORD("AMS"); }
   "discipline"         { ERROR_RSVD_WORD("AMS"); }
   "discrete"           { ERROR_RSVD_WORD("AMS"); }
   "domain"             { ERROR_RSVD_WORD("AMS"); }
   "driver_update"      { ERROR_RSVD_WORD("AMS"); }
   "endconnectrules"    { ERROR_RSVD_WORD("AMS"); }
   "enddiscipline"      { ERROR_RSVD_WORD("AMS"); }
   "endnature"          { ERROR_RSVD_WORD("AMS"); }
   "endparamset"        { ERROR_RSVD_WORD("AMS"); }
   "exclude"            { ERROR_RSVD_WORD("AMS"); }
   "exp"                { FL; return yD_EXP; }
   "final_step"         { ERROR_RSVD_WORD("AMS"); }
   "flicker_noise"      { ERROR_RSVD_WORD("AMS"); }
   "floor"              { FL; return yD_FLOOR; }
   "flow"               { ERROR_RSVD_WORD("AMS"); }
   "from"               { ERROR_RSVD_WORD("AMS"); }
   "ground"             { ERROR_RSVD_WORD("AMS"); }
   "hypot"              { FL; return yD_HYPOT; }
   "idt"                { ERROR_RSVD_WORD("AMS"); }
   "idt_nature"         { ERROR_RSVD_WORD("AMS"); }
   "idtmod"             { ERROR_RSVD_WORD("AMS"); }
   "inf"                { ERROR_RSVD_WORD("AMS"); }
   "initial_step"       { ERROR_RSVD_WORD("AMS"); }
   "laplace_nd"         { ERROR_RSVD_WORD("AMS"); }
   "laplace_np"         { ERROR_RSVD_WORD("AMS"); }
   "laplace_zd"         { ERROR_RSVD_WORD("AMS"); }
   "laplace_zp"         { ERROR_RSVD_WORD("AMS"); }
   "last_crossing"      { ERROR_RSVD_WORD("AMS"); }
   "limexp"             { ERROR_RSVD_WORD("AMS"); }
   "ln"                 { FL; return yD_LN; }
   "log"                { FL; return yD_LOG10; }
   "max"                { ERROR_RSVD_WORD("AMS"); }
   "merged"             { ERROR_RSVD_WORD("AMS"); }
   "min"                { ERROR_RSVD_WORD("AMS"); }
   "nature"             { ERROR_RSVD_WORD("AMS"); }
   "net_resolution"     { ERROR_RSVD_WORD("AMS"); }
   "noise_table"        { ERROR_RSVD_WORD("AMS"); }
   "paramset"           { ERROR_RSVD_WORD("AMS"); }
   "potential"          { ERROR_RSVD_WORD("AMS"); }
   "pow"                { FL; return yD_POW; }
   "resolveto"          { ERROR_RSVD_WORD("AMS"); }
   "sin"                { FL; return yD_SIN; }
   "sinh"               { FL; return yD_SINH; }
   "slew"               { ERROR_RSVD_WORD("AMS"); }
   "split"              { ERROR_RSVD_WORD("AMS"); }
   "sqrt"               { FL; return yD_SQRT; }
   "string"             { FL; return ySTRING; }
   "tan"                { FL; return yD_TAN; }
   "tanh"               { FL; return yD_TANH; }
   "timer"              { ERROR_RSVD_WORD("AMS"); }
   "transition"         { ERROR_RSVD_WORD("AMS"); }
   "units"              { ERROR_RSVD_WORD("AMS"); }
   "white_noise"        { ERROR_RSVD_WORD("AMS"); }
   "wreal"              { FL; return yWREAL; }
   "zi_nd"              { ERROR_RSVD_WORD("AMS"); }
   "zi_np"              { ERROR_RSVD_WORD("AMS"); }
   "zi_zd"              { ERROR_RSVD_WORD("AMS"); }
   "zi_zp"              { ERROR_RSVD_WORD("AMS"); }
}

  /************************************************************************/
  /* Meta comments */

  /* Converted from //{cmt}verilator ...{cmt} by preprocessor */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  "/*verilator"{ws}*"*/"                { FL_FWD; FL_BRK; }  /* Ignore empty comments, may be `endif // verilator */
  "/*verilator clock_enable*/"          { FL; return yVL_CLOCK_ENABLE; }
  "/*verilator clocker*/"               { FL; return yVL_CLOCKER; }
  "/*verilator coverage_block_off*/"    { FL; return yVL_COVERAGE_BLOCK_OFF; }
  "/*verilator coverage_off*/"          { FL_FWD; PARSEP->lexFileline()->coverageOn(false); FL_BRK; }
  "/*verilator coverage_on*/"           { FL_FWD; PARSEP->lexFileline()->coverageOn(true); FL_BRK; }
  "/*verilator forceable*/"             { FL; return yVL_FORCEABLE; }
  "/*verilator full_case*/"             { FL; return yVL_FULL_CASE; }
  "/*verilator hier_block*/"            { FL; return yVL_HIER_BLOCK; }
  "/*verilator inline_module*/"         { FL; return yVL_INLINE_MODULE; }
  "/*verilator isolate_assignments*/"   { FL; return yVL_ISOLATE_ASSIGNMENTS; }
  "/*verilator lint_off"[^*]*"*/"       { FL; PARSEP->lexVerilatorCmtLint(yylval.fl, yytext, true); FL_BRK; }
  "/*verilator lint_on"[^*]*"*/"        { FL; PARSEP->lexVerilatorCmtLint(yylval.fl, yytext, false); FL_BRK; }
  "/*verilator lint_restore*/"          { FL; PARSEP->lexVerilatorCmtLintRestore(PARSEP->lexFileline()); FL_BRK; }
  "/*verilator lint_save*/"             { FL; PARSEP->lexVerilatorCmtLintSave(PARSEP->lexFileline()); FL_BRK; }
  "/*verilator no_clocker*/"            { FL; return yVL_NO_CLOCKER; }
  "/*verilator no_inline_module*/"      { FL; return yVL_NO_INLINE_MODULE; }
  "/*verilator no_inline_task*/"        { FL; return yVL_NO_INLINE_TASK; }
  "/*verilator parallel_case*/"         { FL; return yVL_PARALLEL_CASE; }
  "/*verilator public*/"                { FL; return yVL_PUBLIC; }
  "/*verilator public_flat*/"           { FL; return yVL_PUBLIC_FLAT; }
  "/*verilator public_flat_on*/"        { FL; return yVL_PUBLIC_FLAT_ON; }
  "/*verilator public_flat_rd*/"        { FL; return yVL_PUBLIC_FLAT_RD; }
  "/*verilator public_flat_rd_on*/"     { FL; return yVL_PUBLIC_FLAT_RD_ON; }
  "/*verilator public_flat_rw*/"        { FL; return yVL_PUBLIC_FLAT_RW; }  // The @(edge) is converted by the preproc
  "/*verilator public_flat_rw_on*/"     { FL; return yVL_PUBLIC_FLAT_RW_ON; }
  "/*verilator public_flat_rw_on_sns*/" { FL; return yVL_PUBLIC_FLAT_RW_ON_SNS; }
  "/*verilator public_module*/"         { FL; return yVL_PUBLIC_MODULE; }
  "/*verilator public_on*/"             { FL; return yVL_PUBLIC_ON; }
  "/*verilator public_off*/"            { FL; return yVL_PUBLIC_OFF; }  // terminates previous 'verilator public*_on'
  "/*verilator sc_bv*/"                 { FL; return yVL_SC_BV; }
  "/*verilator sc_clock*/"              { FL; yylval.fl->v3warn(DEPRECATED, "sc_clock is ignored"); FL_BRK; }
  "/*verilator sformat*/"               { FL; return yVL_SFORMAT; }
  "/*verilator split_var*/"             { FL; return yVL_SPLIT_VAR; }
  "/*verilator tag"[^*]*"*/"            { FL; yylval.strp = PARSEP->newString(V3ParseImp::lexParseTag(yytext));
                                          return yVL_TAG; }
  "/*verilator timing_off*/"            { FL_FWD; PARSEP->lexFileline()->timingOn(false); FL_BRK; }
  "/*verilator timing_on*/"             { FL_FWD; PARSEP->lexFileline()->timingOn(true); FL_BRK; }
  "/*verilator tracing_off*/"           { FL_FWD; PARSEP->lexFileline()->tracingOn(false); FL_BRK; }
  "/*verilator tracing_on*/"            { FL_FWD; PARSEP->lexFileline()->tracingOn(true); FL_BRK; }
  "/*verilator unroll_disable*/"        { FL; return yVL_UNROLL_DISABLE; }
  "/*verilator unroll_full*/"           { FL; return yVL_UNROLL_FULL; }

  "/**/"                                { FL_FWD; FL_BRK; }
  "/*"[^*]+"*/"                         { FL; V3ParseImp::lexVerilatorCmtBad(yylval.fl, yytext); FL_BRK; }
}

  /************************************************************************/

  /* Single character operator thingies */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  "{"                   { FL; return yytext[0]; }
  "}"                   { FL; return yytext[0]; }
  "!"                   { FL; return yytext[0]; }
  "#"                   { FL; return yytext[0]; }
  "$"                   { FL; return yytext[0]; }
  "%"                   { FL; return yytext[0]; }
  "&"                   { FL; return yytext[0]; }
  "("                   { FL; return yytext[0]; }
  ")"                   { FL; return yytext[0]; }
  "*"                   { FL; return yytext[0]; }
  "+"                   { FL; return yytext[0]; }
  ","                   { FL; return yytext[0]; }
  "-"                   { FL; return yytext[0]; }
  "."                   { FL; return yytext[0]; }
  "/"                   { FL; return yytext[0]; }
  ":"                   { FL; return yytext[0]; }
  ";"                   { FL; return yytext[0]; }
  "<"                   { FL; return yytext[0]; }
  "="                   { FL; return yytext[0]; }
  ">"                   { FL; return yytext[0]; }
  "?"                   { FL; return yytext[0]; }
  "@"                   { FL; return yytext[0]; }
  "["                   { FL; return yytext[0]; }
  "]"                   { FL; return yytext[0]; }
  "^"                   { FL; return yytext[0]; }
  "|"                   { FL; return yytext[0]; }
  "~"                   { FL; return yytext[0]; }
}

  /************************************************************************/
  /* Operators and multi-character symbols */

  /* Verilog 1995 Operators */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  "&&"                  { FL; return yP_ANDAND; }
  "||"                  { FL; return yP_OROR; }
  "<="                  { FL; return yP_LTE; }
  ">="                  { FL; return yP_GTE; }
  "<<"                  { FL; return yP_SLEFT; }
  ">>"                  { FL; return yP_SRIGHT; }
  "=="                  { FL; return yP_EQUAL; }
  "!="                  { FL; return yP_NOTEQUAL; }
  "==="                 { FL; return yP_CASEEQUAL; }
  "!=="                 { FL; return yP_CASENOTEQUAL; }
  "^~"                  { FL; return yP_XNOR; }
  "~^"                  { FL; return yP_XNOR; }
  "~&"                  { FL; return yP_NAND; }
  "~|"                  { FL; return yP_NOR; }
  "->"                  { FL; return yP_MINUSGT; }
  "=>"                  { FL; return yP_EQGT; }
  "*>"                  { FL; return yP_ASTGT; }
  "&&&"                 { FL; return yP_ANDANDAND; }
}

  /* Verilog 2001 Operators */
<V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
  "<<<"                 { FL; return yP_SLEFT; }
  ">>>"                 { FL; return yP_SSRIGHT; }
  "**"                  { FL; return yP_POW; }
  "+:"                  { FL; return yP_PLUSCOLON; }
  "-:"                  { FL; return yP_MINUSCOLON; }
  ".*"                  { FL; return yP_DOTSTAR; }
  ":+"                  { FL; yyless(1);
                          yylval.fl->v3warn(COLONPLUS, "Perhaps instead of ':+' the intent was '+:'?");
                          return ':'; }
}

  /* SystemVerilog Operators */
<S05,S09,S12,S17,S23,SAX>{
  "'"                   { FL; return yP_TICK; }
  "'{"                  { FL; return yP_TICKBRA; }
  "==?"                 { FL; return yP_WILDEQUAL; }
  "!=?"                 { FL; return yP_WILDNOTEQUAL; }
  "++"                  { FL; return yP_PLUSPLUS; }
  "--"                  { FL; return yP_MINUSMINUS; }
  "+="                  { FL; return yP_PLUSEQ; }
  "-="                  { FL; return yP_MINUSEQ; }
  "*="                  { FL; return yP_TIMESEQ; }
  "/="                  { FL; return yP_DIVEQ; }
  "%="                  { FL; return yP_MODEQ; }
  "&="                  { FL; return yP_ANDEQ; }
  "|="                  { FL; return yP_OREQ; }
  "^="                  { FL; return yP_XOREQ; }
  "<<="                 { FL; return yP_SLEFTEQ; }
  ">>="                 { FL; return yP_SRIGHTEQ; }
  "<<<="                { FL; return yP_SLEFTEQ; }
  ">>>="                { FL; return yP_SSRIGHTEQ; }
  "->>"                 { FL; return yP_MINUSGTGT; }
  "##"                  { FL; return yP_POUNDPOUND; }
  "#-#"                 { FL; return yP_POUNDMINUSPD; }
  "#=#"                 { FL; return yP_POUNDEQPD; }
  "@@"                  { FL; return yP_ATAT; }
  "::"                  { FL; return yP_COLONCOLON; }
  ":="                  { FL; return yP_COLONEQ; }
  ":/"[^\/\*]           { FL; return yP_COLONDIV; }  /* : then comment is not ":/" */
  "|->"                 { FL; return yP_ORMINUSGT; }
  "|=>"                 { FL; return yP_OREQGT; }
  /* Some simulators allow whitespace here. Grr */
  "["{ws}*"*"           { FL; return yP_BRASTAR; }
  "["{ws}*"+"{ws}*"]"   { FL; return yP_BRAPLUSKET; }
  "["{ws}*"->"          { FL; return yP_BRAMINUSGT; }
  "["{ws}*"="           { FL; return yP_BRAEQ; }
}

  /* SystemVerilog 2009 Operators */
<S09,S12,S17,S23,SAX,VLT>{
  "<->"                 { FL; return yP_LTMINUSGT; }
}

  /* SystemVerilog 2023 Operators */
<S23,SAX,VLT>{
  "+/-"                 { FL; return yP_PLUSSLASHMINUS; }
  "+%-"                 { FL; return yP_PLUSPCTMINUS; }
}

  /* Identifiers and numbers */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX,VLT>{
  {escid}               { FL; yylval.strp = PARSEP->newString
                                  (AstNode::encodeName(std::string{yytext+1}));  // +1 to skip the backslash
                          return yaID__LEX;
                        }
  {id}                  { FL; yylval.strp = PARSEP->newString(AstNode::encodeName(std::string{yytext}));
                          return yaID__LEX;
                        }
  \"[^\"\\]*\"          { FL; yylval.strp = PARSEP->newString(yytext+1, yyleng-2);
                          return yaSTRING;
                        }
  \"                    { yy_push_state(STRING); yymore(); }
  \"\"\"                { yy_push_state(QQQ); yymore(); }
  {vnum}                {
                          /* "# 1'b0" is a delay value so must lex as "#" "1" "'b0" */
                          if (PARSEP->lexPrevToken()=='#') {
                              int shortlen = 0;
                              if (std::isdigit(yytext[shortlen])) {
                                  while (std::isdigit(yytext[shortlen])
                                         || yytext[shortlen]=='_') ++shortlen;
                              }
                              if (shortlen) {
                                  // Push rest past numbers for later parse
                                  PARSEP->lexUnputString(yytext + shortlen, yyleng - shortlen);
                                  // Return is stuff before the tick
                                  yyleng = shortlen;
                                  yytext[yyleng] = '\0';
                                  FL; yylval.nump = PARSEP->newNumber(yylval.fl, (char*)yytext);
                                  return yaINTNUM;
                              }
                          }
                          FL; yylval.nump = PARSEP->newNumber(yylval. fl, (char*)yytext);
                          return yaINTNUM;
                        }
  [0-9][_0-9]*          {
                          FL; yylval.nump = PARSEP->newNumber(yylval.fl, (char*)yytext);
                          return yaINTNUM;
                        }
  [0-9][_0-9]*(\.[_0-9]+)([eE][-+]?[_0-9]+)? {
                          FL; yylval.cdouble = lexParseDouble(yylval.fl, yytext, yyleng);
                          return yaFLOATNUM;
                        }
  [0-9][_0-9]*(\.[_0-9]+)?([eE][-+]?[_0-9]+) {
                          FL; yylval.cdouble = lexParseDouble(yylval.fl, yytext, yyleng);
                          return yaFLOATNUM;
                        }
  [0-9][_0-9]*(\.[_0-9]+)?(fs|ps|ns|us|ms|s) {
                          FL; yylval.cdouble = V3ParseImp::lexParseTimenum(yytext);
                          return yaTIMENUM;
                        }
  1step                 {
                          return ya1STEP;
                        }
}

  /************************************************************************/
  /* STRINGS */
<STRING><<EOF>>         { FL; yylval.fl->v3error("EOF in unterminated string");
                          yyleng = 0; yy_pop_state(); FL_BRK; yyterminate(); }
<STRING>{crnl}          { FL; yylval.fl->v3error("Unterminated string");
                          FL_BRK; }
<STRING>\\{crnl}        { yymore(); }
<STRING>\\.             { yymore(); }
<STRING>\"              { yy_pop_state();
                          FL; yylval.strp = PARSEP->newString(yytext+1, yyleng-2);
                          return yaSTRING; }
<STRING>{word}          { yymore(); }
<STRING>.               { yymore(); }

  /************************************************************************/
  /* """ */
<QQQ><<EOF>>            { FL; yylval.fl->v3error("EOF in unterminated \"\"\" string");
                          yyleng = 0; yy_pop_state(); FL_BRK; yyterminate(); }
<QQQ>\\.                { yymore(); }
<QQQ>\n                 { yymore(); }
<QQQ>\"\"\"             { yy_pop_state();
                          FL; yylval.strp = PARSEP->newString(yytext + 3, yyleng - 6);
                          return yaSTRING; }
<QQQ>\"                 { yymore(); }
<QQQ>{word}             { yymore(); }
<QQQ>.                  { yymore(); }

  /************************************************************************/
  /* Attributes */
<ATTRMODE>{crnl}        { yymore(); }
<ATTRMODE>"*)"          { FL_FWD; yy_pop_state(); FL_BRK; }
<ATTRMODE>{word}        { yymore(); }
<ATTRMODE>.             { yymore(); }
<ATTRMODE><<EOF>>       { FL; yylval.fl->v3error("EOF in (*");
                          yyleng = 0; yy_pop_state(); FL_BRK; yyterminate(); }

  /************************************************************************/
  /* Attributes */
  /* Note simulators vary in support for "(* /_*something*_/ foo*)" where _ doesn't exist */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX>{
    "(*"({ws}|{crnl})*({id}|{escid})    { yymore(); yy_push_state(ATTRMODE); }  /* Doesn't match (*), but (* attr_spec */
}

  /************************************************************************/
  /* Tables */
<TABLE>\\{crnl}         { yymore(); }
<TABLE>{crnl}           { yymore(); }
<TABLE>";"              { FL; yylval.strp = PARSEP->newString(yytext, yyleng); return yaTABLELINE; }
<TABLE>"endtable"       { yy_pop_state(); FL; return yENDTABLE; }
<TABLE>"`line"{ws}+[^\n\r]*{crnl}       { FL_FWD; PARSEP->lexPpline(yytext); FL_BRK; }
<TABLE>.                { yymore(); }
<TABLE><<EOF>>          { FL; yylval.fl->v3error("EOF in 'table'");
                          yyleng = 0; yy_pop_state(); FL_BRK; yyterminate(); }

  /************************************************************************/
  /* Preprocessor */
  /* Common for all SYSC header states */
  /* OPTIMIZE: we return one per line, make it one for the entire block */
  /* If add to this list also add to V3LanguageWords.h */
<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX,VLT,SYSCHDR,SYSCINT,SYSCIMP,SYSCIMPH,SYSCCTOR,SYSCDTOR>{
  "`accelerate"                         { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`autoexpand_vectornets"              { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`celldefine"                         { FL_FWD; PARSEP->lexFileline()->celldefineOn(true); FL_BRK; }
  "`default_decay_time"{ws}+[^\n\r]*    { FL_FWD; FL_BRK; }  // Verilog spec - delays only
  "`default_nettype"{ws}+"wire"         { FL_FWD; PARSEP->lexFileline()->warnOn(V3ErrorCode::I_DEF_NETTYPE_WIRE, true); FL_BRK; }
  "`default_nettype"{ws}+"none"         { FL_FWD; PARSEP->lexFileline()->warnOn(V3ErrorCode::I_DEF_NETTYPE_WIRE, false); FL_BRK; }
  "`default_nettype"{ws}+[a-zA-Z0-9]*   { FL; yylval.fl->v3warn(E_UNSUPPORTED, "Unsupported: `default_nettype of other than none or wire: '"
                                                                << yytext << "'");
                                          FL_BRK; }
  "`default_trireg_strength"{ws}+[^\n\r]*   { FL; yylval.fl->v3warn(E_UNSUPPORTED, "Unsupported: Verilog optional directive not implemented: '"
                                                                    << yytext << "'");
                                          FL_BRK; }
  "`delay_mode_distributed"             { FL_FWD; FL_BRK; }  // Verilog spec - delays only
  "`delay_mode_path"                    { FL_FWD; FL_BRK; }  // Verilog spec - delays only
  "`delay_mode_unit"                    { FL_FWD; FL_BRK; }  // Verilog spec - delays only
  "`delay_mode_zero"                    { FL_FWD; FL_BRK; }  // Verilog spec - delays only
  "`disable_portfaults"                 { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`enable_portfaults"                  { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`endcelldefine"                      { FL_FWD; PARSEP->lexFileline()->celldefineOn(false); FL_BRK; }
  "`endprotect"                         { FL_FWD; FL_BRK; }
  "`expand_vectornets"                  { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`inline"                             { FL_FWD; FL_BRK; }
  "`line"{ws}+[^\n\r]*{crnl}            { FL_FWD; PARSEP->lexPpline(yytext); FL_BRK; }
  "`noaccelerate"                       { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`noexpand_vectornets"                { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`noremove_gatenames"                 { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`noremove_netnames"                  { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`nosuppress_faults"                  { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`nounconnected_drive"                { FL; return yaT_NOUNCONNECTED; }
  "`portcoerce"                         { FL_FWD; FL_BRK; }
  "`pragma"{ws}*[^\n\r]*                { FL_FWD; FL_BRK; }  // Verilog 2005
  "`protect"                            { FL_FWD; FL_BRK; }
  "`remove_gatenames"                   { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`remove_netnames"                    { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`resetall"                           { FL; PARSEP->lexFileline()->warnOn(V3ErrorCode::I_DEF_NETTYPE_WIRE, true);
                                          return yaT_RESETALL; }  // Rest handled by preproc
  "`suppress_faults"                    { FL_FWD; FL_BRK; }  // Verilog-XL compatibility
  "`timescale"{ws}+[^\n\r]*             { FL; PARSEP->lexTimescaleParse(yylval.fl,
                                                                        yytext + std::strlen("`timescale"));
                                          FL_BRK; }
  "`unconnected_drive"{ws}+"pull0"      { FL; return yaT_UNCONNECTED_PULL0; }
  "`unconnected_drive"{ws}+"pull1"      { FL; return yaT_UNCONNECTED_PULL1; }
  "`unconnected_drive"                  { FL; yylval.fl->v3error("Bad `unconnected_drive syntax"); FL_BRK; }
  "`uselib"{ws}+[^\n\r]*                { FL_FWD; FL_BRK; }  // Verilog-XL compatibility

  /* See also setLanguage below */
  "`begin_keywords"[ \t]*\"1364-1995\"          { FL_FWD; yy_push_state(V95); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1364-2001\"          { FL_FWD; yy_push_state(V01C); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1364-2001-noconfig\" { FL_FWD; yy_push_state(V01NC); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1364-2005\"          { FL_FWD; yy_push_state(V05); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"VAMS[-0-9.]*\"       { FL_FWD; yy_push_state(VA5); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800-2005\"          { FL_FWD; yy_push_state(S05); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800-2009\"          { FL_FWD; yy_push_state(S09); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800-2012\"          { FL_FWD; yy_push_state(S12); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800-2017\"          { FL_FWD; yy_push_state(S17); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800-2023\"          { FL_FWD; yy_push_state(S23); PARSEP->lexPushKeywords(YY_START); FL_BRK; }
  "`begin_keywords"[ \t]*\"1800[+]VAMS\"        { FL_FWD; yy_push_state(SAX); PARSEP->lexPushKeywords(YY_START); FL_BRK; }  /*Latest SV*/
  "`end_keywords"                               { FL;
                                                  if (!PARSEP->lexPopKeywords()) {
                                                      yylval.fl->v3error("`end_keywords when not inside `begin_keywords block");
                                                  } else { yy_pop_state(); }
                                                  FL_BRK; }

  /* Verilator */
  "`systemc_ctor"                       { FL_FWD; BEGIN SYSCCTOR; FL_BRK; }
  "`systemc_dtor"                       { FL_FWD; BEGIN SYSCDTOR; FL_BRK; }
  "`systemc_header"                     { FL_FWD; BEGIN SYSCHDR; FL_BRK; }
  "`systemc_imp_header"                 { FL_FWD; BEGIN SYSCIMPH; FL_BRK; }
  "`systemc_implementation"             { FL_FWD; BEGIN SYSCIMP; FL_BRK; }
  "`systemc_interface"                  { FL_FWD; BEGIN SYSCINT; FL_BRK; }
  "`verilator_config"                   { FL_FWD; BEGIN VLT; FL_BRK; }
  "`verilog"                            { FL_FWD; BEGIN PARSEP->lexKwdLastState(); FL_BRK; }

  /* Errors */
  "<<<<<<<"[^\n\r]*                     { FL; yylval.fl->v3error("Version control conflict marker in file"); FL_BRK; }
  "======="[^\n\r]*                     { FL; yylval.fl->v3error("Version control conflict marker in file"); FL_BRK; }
  ">>>>>>>"[^\n\r]*                     { FL; yylval.fl->v3error("Version control conflict marker in file"); FL_BRK; }

  /* If add to this list also add to V3LanguageWords.h */
}

<SYSCHDR>[ \t]*[^` \t\n\r][^\n\r]*{crnl}        { FL; yylval.strp = PARSEP->newString(yytext); return yaSCHDR; }
<SYSCINT>[ \t]*[^` \t\n\r][^\n\r]*{crnl}        { FL; yylval.strp = PARSEP->newString(yytext); return yaSCINT; }
<SYSCIMP>[ \t]*[^` \t\n\r][^\n\r]*{crnl}        { FL; yylval.strp = PARSEP->newString(yytext); return yaSCIMP; }
<SYSCIMPH>[ \t]*[^` \t\n\r][^\n\r]*{crnl}       { FL; yylval.strp = PARSEP->newString(yytext); return yaSCIMPH; }
<SYSCCTOR>[ \t]*[^` \t\n\r][^\n\r]*{crnl}       { FL; yylval.strp = PARSEP->newString(yytext); return yaSCCTOR; }
<SYSCDTOR>[ \t]*[^` \t\n\r][^\n\r]*{crnl}       { FL; yylval.strp = PARSEP->newString(yytext); return yaSCDTOR; }

  /* Pick up text-type data */
<SYSCHDR,SYSCINT,SYSCIMP,SYSCIMPH,SYSCCTOR,SYSCDTOR>{
  {wsnr}*               { yymore(); }
  {crnl}                { yymore(); }
}

  /************************************************************************/
  /* Default rules - leave last */

<V95,V01NC,V01C,V05,VA5,S05,S09,S12,S17,S23,SAX,VLT>{
  "`"[a-zA-Z_0-9]+      { FL; V3ParseImp::lexErrorPreprocDirective(yylval.fl, yytext); FL_BRK; }
  "//"[^\n]*            { FL_FWD; FL_BRK; }  /* throw away single line comments */
  .                     { FL; return yytext[0]; }       /* return single char ops. */
}

  /* Catch all - absolutely last */
<*>.|\n                 { FL; yylval.fl->v3error(  // LCOV_EXCL_LINE
                                 "Missing verilog.l rule: Default rule invoked in state "
                                 << YY_START << " '" << yytext << "'");
                          FL_BRK; }
%%
// Avoid code here as cl format misindents
// For implementation functions see V3ParseImp.cpp

int V3ParseImp::stateVerilogRecent() { return STATE_VERILOG_RECENT; }
