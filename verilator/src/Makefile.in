# -*- Makefile -*-
#*****************************************************************************
#
# DESCRIPTION: Verilator: Makefile for verilog source
#
# Code available from: https://verilator.org
#
#*****************************************************************************
#
# Copyright 2003-2024 by <PERSON>. This program is free software; you
# can redistribute it and/or modify it under the terms of either the GNU
# Lesser General Public License Version 3 or the Perl Artistic License
# Version 2.0.
# SPDX-License-Identifier: LGPL-3.0-only OR Artistic-2.0
#
#****************************************************************************/

#### Start of system configuration section. ####

srcdir = @srcdir@
EXEEXT = @EXEEXT@
PYTHON3 = @PYTHON3@
# VPATH only does sources; fix install_test's not creating ../bin
vpath %.h @srcdir@

#### End of system configuration section. ####

default: dbg opt
debug: dbg
optimize: opt

ifneq ($(words $(CURDIR)),1)
 $(error Unsupported: GNU Make cannot build in directories containing spaces, build elsewhere: '$(CURDIR)')
endif

UNDER_GIT = $(wildcard ${srcdir}/../.git/logs/HEAD)

ifeq (,$(wildcard obj_dbg/bear.o))
  ifneq (, $(shell which bear 2>/dev/null))
    BEAR := $(shell which bear)
    ifeq (, $(shell $(BEAR) --output obj_dbg/comptest.json -- true))
      $(shell which bear 2>/dev/null >obj_dbg/bear.o)
    else
      # unsupported version
      BEAR :=
    endif
  endif
else
  BEAR := $(shell cat obj_dbg/bear.o)
endif

ifneq ($(BEAR),)
BEAR_OBJ_OPT := $(BEAR) --append --output obj_dbg/compile_commands.json --
else
BEAR_OBJ_OPT :=
endif

#*********************************************************************

obj_opt:
	mkdir -p $@
obj_dbg:
	mkdir -p $@
../bin:
	mkdir -p $@

.SUFFIXES:

.PHONY: ../bin/verilator_bin$(EXEEXT) ../bin/verilator_bin_dbg$(EXEEXT) ../bin/verilator_coverage_bin_dbg$(EXEEXT)

opt: ../bin/verilator_bin$(EXEEXT)
ifeq ($(VERILATOR_NO_OPT_BUILD),1)  # Faster laptop development... One build
../bin/verilator_bin$(EXEEXT): ../bin/verilator_bin_dbg$(EXEEXT)
	-cp -p $< $@.tmp
	-mv -f $@.tmp $@
else
../bin/verilator_bin$(EXEEXT): obj_opt ../bin prefiles
	$(MAKE) -C obj_opt -j 1  TGT=../$@ -f ../Makefile_obj serial
	$(MAKE) -C obj_opt       TGT=../$@ -f ../Makefile_obj
endif

dbg: ../bin/verilator_bin_dbg$(EXEEXT) ../bin/verilator_coverage_bin_dbg$(EXEEXT)
../bin/verilator_bin_dbg$(EXEEXT): obj_dbg ../bin prefiles
	$(BEAR_OBJ_OPT) $(MAKE) -C obj_dbg -j 1  TGT=../$@ VL_DEBUG=1 -f ../Makefile_obj serial
	$(BEAR_OBJ_OPT) $(MAKE) -C obj_dbg       TGT=../$@ VL_DEBUG=1 -f ../Makefile_obj

../bin/verilator_coverage_bin_dbg$(EXEEXT): obj_dbg ../bin prefiles
	$(MAKE) -C obj_dbg       TGT=../$@ VL_DEBUG=1 VL_VLCOV=1 -f ../Makefile_obj serial_vlcov
	$(MAKE) -C obj_dbg       TGT=../$@ VL_DEBUG=1 VL_VLCOV=1 -f ../Makefile_obj

ifneq ($(VERILATOR_NO_OPT_BUILD),1)  # Faster laptop development... don't rebuild each commit
 ifneq ($(UNDER_GIT),)  # If local git tree... else don't burden users
GIT_CHANGE_DEP = ${srcdir}/../.git/logs/HEAD
 endif
endif

prefiles::
prefiles:: config_rev.h
# This output goes into srcdir if locally configured, as we need to distribute it as part of the kit.
config_rev.h: ${srcdir}/config_rev $(GIT_CHANGE_DEP)
	$(PYTHON3) ${srcdir}/config_rev ${srcdir} >$@

# Human convenience
format:
	$(MAKE) -C .. $@
clang-format:
	$(MAKE) -C .. $@

maintainer-copy::
clean mostlyclean distclean maintainer-clean::
	-rm -rf obj_* *.log *.dmp *.vpd core
	-rm -f *.o *.d *_gen_*
	-rm -f *__gen* obj_*
	-rm -f .objcache*

distclean maintainer-clean::
	-rm -f Makefile Makefile_obj config_package.h

maintainer-clean::
	-rm -f config_rev.h
