[dram_structure]
protocol = DDR3
bankgroups = 1
banks_per_group = 8
rows = 32768
columns = 1024
device_width = 16
BL = 8

[timing]
tCK = 1.25
AL = 0
CL = 11
CWL = 8
tRCD = 11
tRP = 11
tRAS = 28
tRFC = 208
tRFC2 = 208
tRFC4 = 208
REFI = 6240
tRPRE = 0
tWPRE = 0
tRRD_S = 5
tRRD_L = 5
tWTR_S = 6
tWTR_L = 6
tFAW = 32
tWR = 12
tWR2 = 12
tRTP = 6
tCCD_S = 4
tCCD_L = 4
tCKE = 4
tCKESR = 5
tXS = 216
tXP = 5
tRTRS = 1

[power]
VDD = 1.35
IDD0 = 66
IPP0 = 0.0
IDD2P = 18
IDD2N = 32
IDD3P = 38
IDD3N = 47
IDD4W = 171
IDD4R = 235
IDD5AB = 235
IDD6x = 20

[system]
channel_size = 4096
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 800000
output_level = 1

