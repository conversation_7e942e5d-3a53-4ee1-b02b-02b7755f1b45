[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 131072
columns = 1024
device_width = 4
BL = 8

[timing]
tCK = 0.75
AL = 0
CL = 18
CWL = 14
tRCD = 18
tRP = 18
tRAS = 43
tRFC = 467
tRFC2 = 347
tRFC4 = 214
tREFI = 10398
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 7
tWTR_S = 4
tWTR_L = 10
tFAW = 16
tWR = 20
tWR2 = 21
tRTP = 10
tCCD_S = 4
tCCD_L = 7
tCKE = 7
tCKESR = 8
tXS = 480
tXP = 8
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 46
IPP0 = 3.0
IDD2P = 25
IDD2N = 35
IDD3P = 34
IDD3N = 41
IDD4W = 112
IDD4R = 121
IDD5AB = 250
IDD6x = 30

[system]
channel_size = 32768
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1333333
output_level = 1

