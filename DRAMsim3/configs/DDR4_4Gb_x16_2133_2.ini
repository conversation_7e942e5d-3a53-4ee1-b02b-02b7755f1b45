[dram_structure]
protocol = DDR4
bankgroups = 2
banks_per_group = 4
rows = 32768
columns = 1024
device_width = 16
BL = 8

[timing]
tCK = 0.94
AL = 0
CL = 15
CWL = 11
tRCD = 15
tRP = 15
tRAS = 36
tRFC = 278
tRFC2 = 171
tRFC4 = 118
tREFI = 8328
tRPRE = 1
tWPRE = 1
tRRD_S = 6
tRRD_L = 7
tWTR_S = 3
tWTR_L = 8
tFAW = 32
tWR = 16
tWR2 = 17
tRTP = 8
tCCD_S = 4
tCCD_L = 6
tCKE = 6
tCKESR = 7
tXS = 289
tXP = 7
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 65
IPP0 = 3.6
IDD2P = 27
IDD2N = 42
IDD3P = 40
IDD3N = 55
IDD4W = 250
IDD4R = 195
IDD5AB = 170
IDD6x = 20

[system]
channel_size = 4096
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1063829
output_level = 1

