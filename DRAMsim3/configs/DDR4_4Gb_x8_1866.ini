[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 32768
columns = 1024
device_width = 8
BL = 8

[timing]
tCK = 1.07
AL = 0
CL = 13
CWL = 10
tRCD = 13
tRP = 13
tRAS = 32
tRFC = 243
tRFC2 = 150
tRFC4 = 103
tREFI = 7285
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 5
tWTR_S = 3
tWTR_L = 7
tFAW = 22
tWR = 14
tWR2 = 15
tRTP = 7
tCCD_S = 4
tCCD_L = 5
tCKE = 5
tCKESR = 6
tXS = 253
tXP = 6
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 55
IPP0 = 3.0
IDD2P = 27
IDD2N = 40
IDD3P = 40
IDD3N = 55
IDD4W = 140
IDD4R = 125
IDD5AB = 170
IDD6x = 20

[system]
channel_size = 8192
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 934579
output_level = 1

