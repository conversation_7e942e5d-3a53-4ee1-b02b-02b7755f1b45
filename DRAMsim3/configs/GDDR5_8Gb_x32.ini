[dram_structure]
protocol = GDDR5
bankgroups = 4
banks_per_group = 4
rows = 16384
columns = 128
device_width = 32
BL = 8
bankgroup_enable = false

[timing]
tCK = 0.667
CL = 24
CWL = 7
tRCDRD = 24
tRCDWR = 20
tRP = 24
tRAS = 56
tRFC = 74
tREFI = 3800
tRPRE = 1; read preamble
tWPRE = 1; TODO figure this out, should be 1 or 2
tRRD_S = 10
tRRD_L = 10
tWTR_S = 10
tWTR_L = 10
tFAW = 40
tWR = 24
tCCD_S = 2
tCCD_L = 3
tXS = 94
tCKESR = 2
tXP = 12
tRTRS = 1
tRTP_L = 2
tRTP_S = 2
tPPD = 5
t32AW = 360
tRFCb = 30
tREFIb = 238

[power]
VDD = 1.5
IDD0 = 71
IDD2P = 45
IDD2N = 60
IDD3P = 50
IDD3N = 61
IDD4W = 231
IDD4R = 248
IDD5AB = 286
IDD5PB = 45
IDD6x = 35

[system]
channel_size = 4096
channels = 1
bus_width = 128
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1499250
output_level = 1

