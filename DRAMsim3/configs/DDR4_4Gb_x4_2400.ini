[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 65536
columns = 1024
device_width = 4
BL = 8

[timing]
tCK = 0.83
AL = 0
CL = 17
CWL = 12
tRCD = 17
tRP = 17
tRAS = 39
tRFC = 312
tRFC2 = 192
tRFC4 = 132
tREFI = 9360
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 6
tWTR_S = 3
tWTR_L = 9
tFAW = 16
tWR = 18
tWR2 = 19
tRTP = 9
tCCD_S = 4
tCCD_L = 6
tCKE = 6
tCKESR = 7
tXS = 324
tXP = 8
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 60
IPP0 = 3.0
IDD2P = 29
IDD2N = 45
IDD3P = 40
IDD3N = 60
IDD4W = 175
IDD4R = 145
IDD5AB = 175
IDD6x = 20

[system]
channel_size = 16384
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
cmd_queue_size = 8
trans_queue_size = 32
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE

[other]
epoch_period = 1204819
output_level = 1

