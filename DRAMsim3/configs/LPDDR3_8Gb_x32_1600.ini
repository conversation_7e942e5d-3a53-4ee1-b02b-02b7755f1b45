[dram_structure]
protocol = LPDDR3
bankgroups = 1
banks_per_group = 8
rows = 32768
columns = 1024
device_width = 32
BL = 8

[timing]
tCK = 1.25
AL = 0
CL = 12
CWL = 9
tRCD = 15
tRP = 15
tRAS = 34
tRFC = 168
tRFCb = 72
tRFC2 = 168
tRFC4 = 168
REFI = 3120
tREFIb = 390
tRPRE = 1
tWPRE = 1
tRRD_S = 8
tRRD_L = 8
tWTR_S = 6
tWTR_L = 6
tFAW = 40
tWR = 12
tWR2 = 12
tRTP = 6
tCCD_S = 4
tCCD_L = 4
tCKE = 6
tCKESR = 12
tXS = 176
tXP = 6
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 40.0
IPP0 = 0
IDD2P = 1.3
IDD2N = 20.5
IDD3P = 7
IDD3N = 21.0
IDD4W = 245
IDD4R = 250
IDD5AB = 150
IDD6x = 21.0

[system]
channel_size = 2048
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 800000
output_level = 1

