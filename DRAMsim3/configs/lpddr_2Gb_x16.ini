[dram_structure]
protocol = LPDDR
bankgroups = 1
banks_per_group = 4
rows = 16384
columns = 2048
device_width = 16
BL = 8

[timing]
tCK = 4.8
AL = 0
CL = 3
CWL = 3
tRCD = 4
tRP = 3
tRAS = 9
tRFC = 15
tREFI = 1625
tRPRE = 1
tWPRE = 1
tRRD_L = 3
tWTR_L = 2
tFAW = 4
tWR = 3
tCCD_L = 0
tRTP = 0
tXP = 2
tCKE = 1
tRTRS = 1
tCMD = 1
activation_window_depth = 4

[system]
channel_size = 1024
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
validation_output = lpddr_verification.log
epoch_period = 208333
output_level = 1

[thermal]
power_epoch_period = 10000; power epoch period (# cycle)
chip_dim_x = 0.008; chip size in x dimension [m]
chip_dim_y = 0.008; chip size in y dimension [m]
amb_temp = 40; The ambient temperature in [C]
mat_dim_x = 512;
mat_dim_y = 512;
bank_order = 1; 0: x direction first, 1: y direction first

