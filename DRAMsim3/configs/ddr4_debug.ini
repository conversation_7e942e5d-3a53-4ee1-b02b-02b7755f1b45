[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 65536
columns = 1024
device_width = 8
BL = 8

[timing]
tCK = 0.833
AL = 0
CL = 16
CWL = 16
tRCD = 16
tRP = 16
tRAS = 39
tRFC = 420
tRFC2 = 312
tRFC4 = 192
tREFI = 9364
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 6
tWTR_S = 3
tWTR_L = 9
tFAW = 26
tWR = 19
tWR2 = 1
tCCD_S = 4
tCCD_L = 6
tXS = 12
tCKSRE = 12
tXP = 8
tRFCb = 20
tREFIb = 1950
activation_window_depth = 4
tRREFD = 5
tRTRS = 2
tRTP = 10
tCAS = 3
tCWD = 3

[power]
VDD = 1.2
IDD0 = 48
IDD2P = 25
IDD2N = 34
IDD3P = 37
IDD3N = 44.0
IDD4W = 123
IDD4R = 135
IDD5AB = 250
IDD6x = 31

[system]
channel_size = 16384
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
validation_output = ddr4_verification.log
epoch_period = 1200480
output_level = 1

