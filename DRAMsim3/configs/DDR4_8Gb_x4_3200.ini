[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 131072
columns = 1024
device_width = 4
BL = 8

[timing]
tCK = 0.63
AL = 0
CL = 22
CWL = 16
tRCD = 22
tRP = 22
tRAS = 52
tRFC = 560
tRFC2 = 416
tRFC4 = 256
tREFI = 12480
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 8
tWTR_S = 4
tWTR_L = 12
tFAW = 16
tWR = 24
tWR2 = 25
tRTP = 12
tCCD_S = 4
tCCD_L = 8
tCKE = 8
tCKESR = 9
tXS = 576
tXP = 10
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 52
IPP0 = 3.0
IDD2P = 25
IDD2N = 37
IDD3P = 38
IDD3N = 47
IDD4W = 130
IDD4R = 143
IDD5AB = 250
IDD6x = 30

[system]
channel_size = 32768
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1587301
output_level = 1

