[dram_structure]
protocol = GDDR5
bankgroups = 4
banks_per_group = 4
rows = 4096
columns = 64
device_width = 32
BL = 8
bankgroup_enable = false

[timing]
tCK = 0.667
CL = 24
CWL = 7
tRCDRD = 18
tRCDWR = 15
tRP = 18
tRAS = 42
tRFC = 98
tREFI = 11699
tRPRE = 1; read preamble
tWPRE = 1; TODO figure this out, should be 1 or 2
tRRD_S = 9
tRRD_L = 9
tWTR_S = 8
tWTR_L = 8
tFAW = 35
tWR = 18
tCCD_S = 2
tCCD_L = 3
tXS = 116
tCKE = 16
tCKESR = 8
tXP = 12
tRTRS = 0
tRTP_L = 3
tRTP_S = 3
tPPD = 2
t32AW = 280

[power]
VDD = 1.5
IDD0 = 490
IDD2P = 210
IDD2N = 250
IDD3P = 310
IDD3N = 450
IDD4W = 1160
IDD4R = 1080
IDD5AB = 450
IDD5PB = 45
IDD6x = 60

[system]
channel_size = 1024
channels = 1
bus_width = 256
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1499250
output_level = 1

