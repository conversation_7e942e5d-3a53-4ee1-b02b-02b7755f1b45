[hmc]
num_links = 4
link_width = 16
link_speed = 10000
block_size = 128
xbar_queue_depth = 6

[dram_structure]
protocol = HMC
bankgroups = 1
banks_per_group = 8
rows = 65536
columns = 64
device_width = 32
BL = 2
num_dies = 4

[timing]
tCK = 0.8
CL = 17
CWL = 17
tRCD = 17
tRP = 17
tRAS = 34
tRFC = 420
tREFI = 9364; average periodic refresh interval, 3.9us
tRRD_S = 4
tRRD_L = 4
tWTR_S = 3
tWTR_L = 3
tFAW = 27
tWR = 17
tCCD_S = 6
tCCD_L = 6
tXS = 12
tCKSRE = 1
tXP = 8
tRTP_L = 8
tRTP_S = 8
tRTRS = 0

[power]
VDD = 1.2
IDD0 = 25
IDD2P = 17
IDD2N = 19
IDD3P = 20
IDD3N = 21
IDD4W = 61
IDD4R = 64
IDD5AB = 150
IDD6x = 21

[system]
channel_size = 128
channels = 16
bus_width = 32
address_mapping = rocorabgbach
queue_structure = PER_BANK
row_buf_policy = CLOSE_PAGE
cmd_queue_size = 8
trans_queue_size = 32
unified_queue = True

[other]
epoch_period = 1250000
output_level = 1

[thermal]
power_epoch_period = 10000; power epoch period (# cycle)
chip_dim_x = 0.008; chip size in x dimension [m]
chip_dim_y = 0.008; chip size in y dimension [m]
amb_temp = 40; The ambient temperature in [C]
mat_dim_x = 512;
mat_dim_y = 512;
bank_order = 1; 0: x direction first, 1: y direction first
bank_layer_order = 0;

