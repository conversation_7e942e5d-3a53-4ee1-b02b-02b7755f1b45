[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 65536
columns = 1024
device_width = 8
BL = 8

[timing]
tCK = 0.83
AL = 0
CL = 17
CWL = 12
tRCD = 17
tRP = 17
tRAS = 39
tRFC = 420
tRFC2 = 312
tRFC4 = 192
tREFI = 9360
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 6
tWTR_S = 3
tWTR_L = 9
tFAW = 26
tWR = 18
tWR2 = 19
tRTP = 9
tCCD_S = 4
tCCD_L = 6
tCKE = 6
tCKESR = 7
tXS = 432
tXP = 8
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 48
IPP0 = 3.0
IDD2P = 25
IDD2N = 34
IDD3P = 37
IDD3N = 43
IDD4W = 123
IDD4R = 135
IDD5AB = 250
IDD6x = 30

[system]
channel_size = 16384
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1204819
output_level = 1

[thermal]
loc_mapping = 33,33,32-31,30-29,26:13-27-28,12:3
power_epoch_period = 100000; power epoch period (# cycle)
chip_dim_x = 0.008; chip size in x dimension [m]
chip_dim_y = 0.008; chip size in y dimension [m]
amb_temp = 40; The ambient temperature in [C]
mat_dim_x = 512;
mat_dim_y = 512;
bank_order = 0; 0: x direction first, 1: y direction first

