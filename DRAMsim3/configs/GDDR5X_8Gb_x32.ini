[dram_structure]
protocol = GDDR5X
bankgroups = 4
banks_per_group = 4
rows = 16384
columns = 64
device_width = 32
BL = 16
bankgroup_enable = false

[timing]
tCK = 0.666 (1/1.5)
CL = 24
CWL = 7
tRCDRD = 18
tRCDWR = 15
tRP = 18
tRAS = 42
tRFC = 98
tREFI = 11699
tRPRE = 1; read preamble
tWPRE = 1; TODO figure this out, should be 1 or 2
tRRD_S = 9
tRRD_L = 9
tWTR_S = 8
tWTR_L = 8
tFAW = 35
tWR = 18
tCCD_S = 2
tCCD_L = 3
tXS = 116
tCKE = 16
tCKSRE = 8
tXP = 12
tRTRS = 0
tRTP_L = 3
tRTP_S = 3
tPPD = 2
t32AW = 280

[power]
VDD = 1.35
IDD0 = 500
IDD2P = 220
IDD2N = 260
IDD3P = 330
IDD3N = 480
IDD4W = 2320
IDD4R = 2160
IDD5AB = 600
IDD5PB = 60
IDD6x = 65

[system]
channel_size = 4096
channels = 1
bus_width = 128
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1501501
output_level = 1

[thermal]
power_epoch_period = 1000; power epoch period (# cycle)
chip_dim_x = 0.008; chip size in x dimension [m]
chip_dim_y = 0.008; chip size in y dimension [m]
amb_temp = 40; The ambient temperature in [C]
mat_dim_x = 512;
mat_dim_y = 512;
bank_order = 1; 0: x direction first, 1: y direction first

