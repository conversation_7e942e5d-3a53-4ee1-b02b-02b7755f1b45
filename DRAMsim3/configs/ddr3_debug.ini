[dram_structure]
protocol = DDR3
bankgroups = 1
banks_per_group = 8
rows = 16384
columns = 1024
device_width = 8
BL = 8

[timing]
tCK = 1.5
AL = 0
CL = 10
CWL = 7
tRCD = 10
tRP = 10
tRAS = 24
tRFC = 74
tREFI = 5200
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tWTR_S = 5
tFAW = 20
tWR = 10
tCCD_S = 4
tRTP = 5
tCKE = 4
tCKESR = 1
tXS = 81
tXP = 5
tRTRS = 1

[power]
VDD = 1.35
IDD0 = 33
IDD2P = 12
IDD2N = 17
IDD3P = 14
IDD3N = 23
IDD4W = 77
IDD4R = 72
IDD5AB = 155
IDD6x = 12

[system]
channel_size = 2048
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 666666
output_level = 1

