;  (C) Copyright 2006-2018 Barcelona Supercomputing Center (BSC) 
;
;The copyright holder is <PERSON>C-C<PERSON>, and the authorship correspond to <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. The complete explanation of the derivation of the data can be found in the following study: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. 2017. Enabling a reliable STT-MRAM main memory simulation. In Proceedings of the International Symposium on Memory Systems (MEMSYS '17). Washington DC, USA, 283-292. DOI: https://doi.org/10.1145/3132402.3132416

;Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

;1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

;2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

;THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRA<PERSON>IES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.”

; This configuration lists detailed timing parameters for STT-MRAM main memory, specifying a 1.5x deviation from respective DRAM timing parameters. Please note, the current parameters (IDD0, IDD1.. etc) DOES NOT correspond to STT-MRAM and should not be used for current/energy estimations.    

[dram_structure]
protocol = DDR3  ; it's STT-MRAM, but using DDR3 protocol
bankgroups = 1
banks_per_group = 8
rows = 32768
columns = 1024
device_width = 8
BL=4

[timing]
tCK = 1.25;
CL = 11;
CWL = 11
AL = 0;
tRCDRD=17
tRCDWR=17
tRP=17
tRAS=23
tRFC = 1
tREFI = 6240
tRRD_S = 8
tRRD_L = 8
tWTR_S = 6
tWTR_L = 6
tFAW = 36
tWR = 12
tCCD_S = 4
tCCD_L = 4
tCKE = 4
tXP = 5
tRTP_L = 6
tRTP_S = 6
; tRC=40
tRTRS=1
REFRESH_PERIOD=7800; 

; The following current parameters DOES NOT correspond to STT-MRAM, and should not be used used for current/energy estimations.    
[power]
VDD=1.5
IDD0=1305;      
IDD1=1395;      
IDD2P=846;      
IDD2Q=1030;     
IDD2N=1050;      
IDD3Pf=60;      
IDD3Ps=60;      
IDD3N=1310;     
IDD4W=1765;     
IDD4R=230;      
IDD5=1940;      
IDD6=246;       
IDD6L=246;      
IDD7=2160;      

[system]
channel_size = 4096
channels = 1
bus_width = 64
address_mapping = rorabgbachco
queue_structure = PER_RANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 16
trans_queue_size = 32

[other]
epoch_period = 100000
output_level = 1
