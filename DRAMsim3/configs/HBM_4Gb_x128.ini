[dram_structure]
protocol = HBM
bankgroups = 4
banks_per_group = 4
rows = 16384
columns = 64
device_width = 128
BL = 4
num_dies = 4

[timing]
tCK = 2
AL = 0
CL = 7
CWL = 4
tRCDRD = 7
tRCDWR = 6
tRP = 7
tRAS = 17
tRFC = 
tREFI = 1950
tRPRE = 1; read preamble
tWPRE = 1; TODO figure this out, should be 1 or 2
tRRD_S = 4
tRRD_L = 5
tWTR_S = 2
tWTR_L = 4
tFAW = 20
tWR = 8
tCCD_S = 2
tCCD_L = 3
tXS = 0
tCKSRE = 0
tXP = 5
tRTP_L = 7
tRTP_S = 6
tPPD = 5
t32AW = 330
tRFCb = 
tREFIb = 
tRREFD = 
tRFCPB = 

[power]
VDD = 1.2
IDD0 = 48
IDD2P = 25
IDD2N = 34
IDD3P = 37
IDD3N = 43
IDD4W = 123
IDD4R = 135
IDD5AB = 250
IDD6x = 31

[system]
channel_size = 512
channels = 8
bus_width = 128
address_mapping = rorabgbachco
queue_structure = PER_BANK
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32
unified_queue = False

[other]
epoch_period = 500000
output_level = 1

[thermal]
power_epoch_period = 10000; power epoch period (# cycle)
logic_bg_power = 5
logic_max_power = 25
chip_dim_x = 0.008; chip size in x dimension [m]
chip_dim_y = 0.008; chip size in y dimension [m]
amb_temp = 40; The ambient temperature in [C]
mat_dim_x = 1024;
mat_dim_y = 1024;
bank_order = 1; 0: x direction first, 1: y direction first
bank_layer_order = 0;

