[dram_structure]
protocol = LPDDR3
bankgroups = 1
banks_per_group = 8
rows = 32768
columns = 1024
device_width = 32
BL = 8

[timing]
tCK = 1.5
AL = 0
CL = 10
CWL = 8
tRCD = 12
tRP = 12
tRAS = 28
tRFC = 140
tRFCb = 60
tRFC2 = 140
tRFC4 = 140
REFI = 2600
tREFIb = 325
tRPRE = 1
tWPRE = 1
tRRD_S = 7
tRRD_L = 7
tWTR_S = 5
tWTR_L = 5
tFAW = 34
tWR = 10
tWR2 = 10
tRTP = 5
tCCD_S = 4
tCCD_L = 4
tCKE = 5
tCKESR = 10
tXS = 147
tXP = 5
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 40.0
IPP0 = 0
IDD2P = 1.3
IDD2N = 20.0
IDD3P = 7
IDD3N = 20.5
IDD4W = 215
IDD4R = 220
IDD5AB = 150
IDD6x = 20.5

[system]
channel_size = 2048
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 666666
output_level = 1

