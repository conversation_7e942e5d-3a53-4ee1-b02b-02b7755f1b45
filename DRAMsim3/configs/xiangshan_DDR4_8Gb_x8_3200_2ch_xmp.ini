[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 65536
columns = 1024
device_width = 8
BL = 8

[timing]
tCK = 0.63
AL = 0
CL = 16
CWL = 16
tRCD = 18
tRP = 18
tRAS = 52
tRFC = 560
tRFC2 = 416
tRFC4 = 256
tREFI = 12480
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 8
tWTR_S = 4
tWTR_L = 12
tFAW = 34
tWR = 24
tWR2 = 25
tRTP = 12
tCCD_S = 4
tCCD_L = 8
tCKE = 8
tCKESR = 9
tXS = 576
tXP = 10
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 57
IPP0 = 3.0
IDD2P = 25
IDD2N = 37
IDD3P = 43
IDD3N = 52
IDD4W = 150
IDD4R = 168
IDD5AB = 250
IDD6x = 30

[system]
channel_size = 16384
channels = 2
bus_width = 64
address_mapping = roracobabgch
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1587301
output_level = 1
cpu_freq = 3000
dram_freq = 1600
