[dram_structure]
protocol = DDR4
bankgroups = 4
banks_per_group = 4
rows = 131072
columns = 1024
device_width = 4
BL = 8

[timing]
tCK = 0.68
AL = 0
CL = 20
CWL = 16
tRCD = 20
tRP = 20
tRAS = 47
tRFC = 514
tRFC2 = 382
tRFC4 = 235
tREFI = 11439
tRPRE = 1
tWPRE = 1
tRRD_S = 4
tRRD_L = 8
tWTR_S = 4
tWTR_L = 11
tFAW = 16
tWR = 22
tWR2 = 23
tRTP = 11
tCCD_S = 4
tCCD_L = 8
tCKE = 8
tCKESR = 9
tXS = 528
tXP = 9
tRTRS = 1

[power]
VDD = 1.2
IDD0 = 49
IPP0 = 3.0
IDD2P = 25
IDD2N = 36
IDD3P = 36
IDD3N = 44
IDD4W = 121
IDD4R = 132
IDD5AB = 250
IDD6x = 30

[system]
channel_size = 32768
channels = 1
bus_width = 64
address_mapping = rochrababgco
queue_structure = PER_BANK
refresh_policy = RANK_LEVEL_STAGGERED
row_buf_policy = OPEN_PAGE
cmd_queue_size = 8
trans_queue_size = 32

[other]
epoch_period = 1470588
output_level = 1

