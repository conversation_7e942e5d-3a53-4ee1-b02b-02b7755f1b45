name: <PERSON><PERSON>

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  setup-develop-env:
    # The type of runner that the job will run on
    runs-on: ubuntu-24.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4

      - name: Setup XiangShan environment
        run: |
            git config --global url."https://github.com/".insteadOf **************:
            git config --global url."https://".insteadOf git://
            sudo -s ./setup-tools.sh
            source ./setup.sh
      
      - name: Sim NutShell using Verilator
        run: |
            cd $GITHUB_WORKSPACE
            source ./env-test.sh
