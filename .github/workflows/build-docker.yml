name: Build docker image

on:
  push:
    branches: [ master ]

jobs:
  build-ubuntu:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        version: [20.04, 22.04, 24.04]

    steps:
      - uses: actions/checkout@v4

      - name: Get current date
        id: date
        run: |
          echo "DATE=$(date +'%Y%m%d')" >> $GITHUB_ENV

      - uses: docker/metadata-action@v5
        id: metadata
        with:
          images: |
            ghcr.io/${{ github.repository_owner }}/xs-env
          tags: |
            ${{ matrix.version == '24.04' && 'latest' || '' }}
            ubuntu-${{ matrix.version }}
            ubuntu-${{ matrix.version }}-${{ env.DATE }}
          labels: |
            maintainer=${{ github.repository_owner }}

      - uses: docker/setup-buildx-action@v3

      - uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: ${{ steps.metadata.outputs.tags }}
          labels: ${{ steps.metadata.outputs.labels }}
          build-args: |
            BASE_IMAGE=ubuntu:${{ matrix.version }}

  build-centos7:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Get current date
        id: date
        run: |
          echo "DATE=$(date +'%Y%m%d')" >> $GITHUB_ENV

      - uses: docker/metadata-action@v5
        id: metadata
        with:
          images: |
            ghcr.io/${{ github.repository_owner }}/xs-env
          tags: |
            centos7
            centos7-${{ env.DATE }}
          labels: |
            maintainer=${{ github.repository_owner }}

      - uses: docker/setup-buildx-action@v3

      - uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: ${{ steps.metadata.outputs.tags }}
          labels: ${{ steps.metadata.outputs.labels }}
          file: ./centos.Dockerfile
